import { useState, useEffect, useCallback } from 'react';
import { Customer } from '../../main/database/models/Customer';

interface UseCustomersResult {
  customers: Customer[];
  inactiveCustomers: Customer[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getCustomerById: (id: number) => Promise<Customer | null>;
  createCustomer: (customerData: Partial<Customer>, forceCreate?: boolean) => Promise<Customer | null>;
  updateCustomer: (id: number, customerData: Partial<Customer>) => Promise<Customer | null>;
  deleteCustomer: (id: number) => Promise<boolean>;
  reactivateCustomer: (id: number) => Promise<Customer | null>;
  searchCustomers: (query: string) => Promise<Customer[]>;
  getPendingSalesByCustomerId: (customerId: number) => Promise<any[]>;
  markSaleAsPaid: (saleId: number) => Promise<boolean>;
  fetchInactiveCustomers: () => Promise<void>;
  inactiveCustomersLoaded: boolean;
}

export const useCustomers = (): UseCustomersResult => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [inactiveCustomers, setInactiveCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [inactiveCustomersLoaded, setInactiveCustomersLoaded] = useState<boolean>(false);

  const fetchCustomers = useCallback(async (includeInactive: boolean = false) => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customers:getAll', { activeOnly: !includeInactive });
      if (response.success) {
        // Fetch pending sales information for each customer
        const customersWithPending = await Promise.all(
          response.data.map(async (customer: any) => {
            const pendingSales = await getPendingSalesByCustomerId(customer.id);
            return {
              ...customer,
              hasPendingPayments: pendingSales.length > 0,
              pendingSales: pendingSales
            };
          })
        );
        
        // Filter active customers using status instead of is_hidden
        const active = customersWithPending.filter((c: any) => c.status === 'active');
        setCustomers(active);
        
        // Only update inactive customers if we're fetching them
        if (includeInactive) {
          const inactive = customersWithPending.filter((c: any) => c.status === 'inactive');
          setInactiveCustomers(inactive);
          setInactiveCustomersLoaded(true);
        }
      } else {
        setError(response.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      setError('Failed to fetch customers');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchInactiveCustomers = useCallback(async (forceRefresh: boolean = false) => {
    // Skip if already loaded and not forced to refresh
    if (inactiveCustomersLoaded && !forceRefresh) return;
    
    setLoading(true);
    setError(null);
    try {
      // Use customers:getInactive endpoint which specifically gets inactive customers
      const response = await window.electronAPI.invoke('customers:getInactive');
      if (response.success) {
        // Fetch pending sales information for each customer
        const inactiveWithPending = await Promise.all(
          response.data.map(async (customer: any) => {
            try {
              const pendingSales = await getPendingSalesByCustomerId(customer.id);
              return {
                ...customer,
                hasPendingPayments: pendingSales.length > 0,
                pendingSales: pendingSales
              };
            } catch (error) {
              console.error(`Error fetching pending sales for customer ${customer.id}:`, error);
              return {
                ...customer,
                hasPendingPayments: false,
                pendingSales: []
              };
            }
          })
        );
        
        setInactiveCustomers(inactiveWithPending);
        setInactiveCustomersLoaded(true);
      } else {
        setError(response.error || 'Unknown error occurred');
        // Even on error, mark as loaded to prevent repeated failed attempts
        setInactiveCustomersLoaded(true);
        setInactiveCustomers([]);
      }
    } catch (error) {
      console.error('Error fetching inactive customers:', error);
      setError('Failed to fetch inactive customers');
      // Even on error, mark as loaded to prevent repeated failed attempts
      setInactiveCustomersLoaded(true);
      setInactiveCustomers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const getCustomerById = useCallback(async (id: number): Promise<Customer | null> => {
    try {
      const response = await window.electronAPI.invoke('customers:getById', id);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch customer with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const createCustomer = useCallback(async (customerData: Partial<Customer>, forceCreate: boolean = false): Promise<Customer | null> => {
    try {
      const response = await window.electronAPI.invoke('customers:create', customerData, forceCreate);
      if (response.success) {
        await fetchCustomers(); // Refresh active customers only
        return response.data;
      }
      setError(response.error || 'Failed to create customer');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchCustomers]);

  const updateCustomer = useCallback(async (id: number, customerData: Partial<Customer>): Promise<Customer | null> => {
    try {
      const response = await window.electronAPI.invoke('customers:update', id, customerData);
      if (response.success) {
        await fetchCustomers(); // Refresh active customers only
        return response.data;
      }
      setError(response.error || `Failed to update customer with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchCustomers]);

  const deleteCustomer = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('customers:delete', id);
      if (response.success) {
        // After deletion, update both active and inactive lists
        await fetchCustomers();
        if (inactiveCustomersLoaded) {
          await fetchInactiveCustomers();
        }
        return true;
      }
      setError(response.error || `Failed to delete customer with ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchCustomers, fetchInactiveCustomers, inactiveCustomersLoaded]);

  const reactivateCustomer = useCallback(async (id: number): Promise<Customer | null> => {
    try {
      const response = await window.electronAPI.invoke('customers:reactivate', id);
      if (response.success) {
        // After reactivation, update lists more efficiently
        
        // First, update the active customers list by adding the reactivated customer
        const reactivatedCustomer = response.data;
        if (reactivatedCustomer) {
          // Add pending sales info
          const pendingSales = await getPendingSalesByCustomerId(reactivatedCustomer.id);
          const customerWithPending = {
            ...reactivatedCustomer,
            hasPendingPayments: pendingSales.length > 0,
            pendingSales: pendingSales
          };
          
          // Add to active customers
          setCustomers(prevCustomers => [...prevCustomers, customerWithPending]);
          
          // Remove from inactive customers if they're loaded
          if (inactiveCustomersLoaded) {
            setInactiveCustomers(prevInactive => 
              prevInactive.filter(c => c.id !== id)
            );
          }
        } else {
          // Fallback to full refresh if something went wrong
          await fetchCustomers();
          if (inactiveCustomersLoaded) {
            await fetchInactiveCustomers(true); // Force refresh inactive customers
          }
        }
        
        return response.data;
      }
      setError(response.error || `Failed to reactivate customer with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchCustomers, inactiveCustomersLoaded]);

  const searchCustomers = useCallback(async (query: string): Promise<Customer[]> => {
    try {
      const response = await window.electronAPI.invoke('customers:search', query);
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Failed to search customers');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  // Get pending sales for a customer
  const getPendingSalesByCustomerId = async (customerId: number): Promise<any[]> => {
    try {
      const response = await window.electronAPI.invoke('sales:getPendingByCustomer', customerId);
      if (response.success) {
        return response.data.map((sale: any) => ({
          id: sale.id,
          total_amount: sale.total_amount,
          sale_date: sale.sale_date,
          payment_method: sale.payment_method,
          items_count: sale.items?.length || 0
        }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching pending sales:', error);
      return [];
    }
  };

  // Mark a sale as paid and update just that customer's pending sales status
  const markSaleAsPaid = async (saleId: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('sales:updateStatus', saleId, 'paid');
      if (response.success) {
        // Update only the affected customer instead of all customers
        if (response.data?.customerId) {
          const customerId = response.data.customerId;
          
          // Update the customers list with the new pending payment status
          setCustomers(prevCustomers => {
            return prevCustomers.map(customer => {
              if (customer.id === customerId) {
                // Get updated pending sales for this customer
                return {
                  ...customer,
                  hasPendingPayments: response.data.hasPendingPayments || false
                };
              }
              return customer;
            });
          });
          
          return true;
        }
        
        // Fallback to full refresh if customerId not available
        await fetchCustomers();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error marking sale as paid:', error);
      return false;
    }
  };

  // Refresh the customers data
  const refetch = async () => {
    await fetchCustomers();
    // Only refetch inactive customers if they've been loaded before
    if (inactiveCustomersLoaded) {
      await fetchInactiveCustomers();
    }
  };

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  return {
    customers,
    inactiveCustomers,
    loading,
    error,
    refetch,
    getCustomerById,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    reactivateCustomer,
    searchCustomers,
    getPendingSalesByCustomerId,
    markSaleAsPaid,
    fetchInactiveCustomers,
    inactiveCustomersLoaded
  };
}; 