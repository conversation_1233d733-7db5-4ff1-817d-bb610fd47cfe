import React, { useState, useEffect, useRef, useCallback } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Grid from '@mui/material/Grid';
import FormHelperText from '@mui/material/FormHelperText';
import { SelectChangeEvent } from '@mui/material/Select';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';
import { PetFormData, PetType, PetTypes, Pet } from '../../types/pets';
import { Customer } from '../../types/pets';
import { PhotoCamera, Delete as DeleteIcon, AddAPhoto } from '@mui/icons-material';

interface PetFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (petData: PetFormData, imageFile?: File | null) => void;
  pet?: Pet;
  title: string;
  customers: Customer[];
}

export const PetFormDialog = React.memo(({
  open,
  onClose,
  onSave,
  pet,
  title,
  customers
}: PetFormDialogProps) => {
  const defaultFormData: PetFormData = {
    customer_id: 0,
    name: '',
    type: '',
    breed: '',
    age: null,
    size: '',
    gender: '',
    fur_type: '',
    additional_notes: '',
    photo_url: null
  };

  const [formData, setFormData] = useState<PetFormData>(defaultFormData);
  const [errors, setErrors] = useState<Partial<Record<keyof PetFormData, string>>>({});
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Cleanup object URL when component unmounts or when previewUrl changes
  useEffect(() => {
    return () => {
      if (previewUrl && !previewUrl.startsWith('data:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  useEffect(() => {
    // Cleanup any existing object URL when resetting the form
    if (previewUrl && !previewUrl.startsWith('data:')) {
      URL.revokeObjectURL(previewUrl);
    }
    
    if (pet) {
      setFormData({
        customer_id: pet.customer_id,
        name: pet.name,
        type: pet.type,
        breed: pet.breed || '',
        age: pet.age !== null ? pet.age : null,
        size: pet.size || '',
        gender: pet.gender || '',
        fur_type: pet.fur_type || '',
        additional_notes: pet.additional_notes || '',
        photo_url: pet.photo_url // Keep the stored URL reference, not the actual image data
      });
      
      if (pet.photo_url) {
        setPreviewUrl(pet.photo_url); // For existing pets, use the stored URL directly
        setImageFile(null); // No new file for existing images
      } else {
        setPreviewUrl(null);
        setImageFile(null);
      }
    } else {
      setFormData(defaultFormData);
      setPreviewUrl(null);
      setImageFile(null);
    }
    setErrors({});
  }, [pet, open]);

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<Record<keyof PetFormData, string>> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Nome do pet é obrigatório';
    }
    
    if (!formData.type) {
      newErrors.type = 'Tipo de pet é obrigatório';
    }
    
    if (formData.customer_id === 0) {
      newErrors.customer_id = 'Proprietário é obrigatório';
    }
    
    if (formData.age !== null && isNaN(formData.age)) {
      newErrors.age = 'Idade deve ser um número';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(() => {
    if (validateForm()) {
      // If we have a new image file, we'll handle it separately
      // Keep the original photo_url if no new file was selected
      const submittedData = {
        ...formData,
        age: formData.age,
        photo_url: imageFile ? null : formData.photo_url // Only pass photo_url if no new file
      };
      onSave(submittedData, imageFile);
    }
  }, [formData, onSave, validateForm, imageFile]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'age') {
      // Convert age input to number or null
      const ageValue = value === '' ? null : parseInt(value, 10);
      setFormData(prev => ({ 
        ...prev, 
        [name]: isNaN(ageValue as number) ? null : ageValue 
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    if (errors[name as keyof PetFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);

  const handleSelectChange = useCallback((e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof PetFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);

  const handleCustomerChange = useCallback((_event: React.SyntheticEvent, value: Customer | null) => {
    if (value) {
      setFormData(prev => ({ ...prev, customer_id: value.id }));
      if (errors.customer_id) {
        setErrors(prev => ({ ...prev, customer_id: '' }));
      }
    } else {
      setFormData(prev => ({ ...prev, customer_id: 0 }));
    }
  }, [errors]);

  const getSelectedCustomer = useCallback(() => {
    return customers.find(customer => customer.id === formData.customer_id) || null;
  }, [customers, formData.customer_id]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validação de tipo de arquivo
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setErrors(prev => ({ 
        ...prev, 
        photo_url: 'Formato inválido. Use JPEG, PNG, GIF ou WebP.' 
      }));
      return;
    }

    // Validação de tamanho (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ 
        ...prev, 
        photo_url: 'Imagem muito grande. Máximo 5MB.' 
      }));
      return;
    }

    // Clean up previous object URL if it exists
    if (previewUrl && !previewUrl.startsWith('data:')) {
      URL.revokeObjectURL(previewUrl);
    }

    // Create an object URL for preview instead of reading as base64
    const objectUrl = URL.createObjectURL(file);
    setImageFile(file); // Store the actual file object
    setPreviewUrl(objectUrl); // Use object URL for preview
    setFormData(prev => ({ ...prev, photo_url: 'file_selected' })); // Indicate a file was selected
    
    if (errors.photo_url) {
      setErrors(prev => ({ ...prev, photo_url: '' }));
    }
  }, [errors, previewUrl]);

  const handleRemovePhoto = useCallback(() => {
    // Clean up the object URL if it exists
    if (previewUrl && !previewUrl.startsWith('data:')) {
      URL.revokeObjectURL(previewUrl);
    }
    
    setPreviewUrl(null);
    setImageFile(null);
    setFormData(prev => ({ ...prev, photo_url: null }));
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [previewUrl]);

  const triggerFileInput = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  // Get pet type icon - Memoize this function
  const getPetTypeIcon = useCallback((type: string): string => {
    switch(type?.toLowerCase()) {
      case 'cachorro':
      case 'dog':
        return '🐕';
      case 'gato':
      case 'cat':
        return '🐈';
      case 'pássaro':
      case 'bird':
      case 'ave':
        return '🦜';
      case 'coelho':
      case 'rabbit':
        return '🐇';
      case 'hamster':
      case 'pequeno roedor':
        return '🐹';
      case 'peixe':
      case 'fish':
        return '🐠';
      case 'réptil':
      case 'reptil':
        return '🦎';
      default:
        return '🐾';
    }
  }, []);

  const getPetAvatarColor = useCallback((type: string): string => {
    switch (type?.toLowerCase()) {
      case 'cachorro':
      case 'dog':
        return '#9c27b0';
      case 'gato':
      case 'cat':
        return '#2196f3';
      case 'ave':
      case 'bird':
      case 'pássaro':
        return '#ff9800';
      case 'peixe':
      case 'fish':
        return '#4caf50';
      case 'réptil':
      case 'reptil':
        return '#f44336';
      case 'coelho':
      case 'rabbit':  
        return '#795548';
      case 'hamster':
      case 'pequeno roedor':
        return '#ff5722';
      default:
        return '#9e9e9e';
    }
  }, []);

  return (
    <Box sx={{ position: 'relative' }}>
      <Dialog 
        open={open} 
        onClose={onClose} 
        maxWidth="md" 
        fullWidth
        PaperProps={{
          sx: {
            overflow: 'visible', // Allow content to overflow for the avatar overlay
            marginTop: '60px', // Move the entire dialog down to prevent photo from being cut off
          }
        }}
      >
        {/* Add extra padding to the top of the dialog title to account for the photo */}
        <DialogTitle sx={{ pt: 14, pb: 3, textAlign: 'center' }}>{title}</DialogTitle>
        
        {/* Position the photo selector as an overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: -40, // Adjusted to be less negative - moves photo selector down
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 10,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              position: 'relative',
              width: 128,
              height: 128,
              borderRadius: '50%',
              bgcolor: previewUrl ? 'transparent' : (formData.type ? getPetAvatarColor(formData.type) : '#e0e0e0'),
              boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              overflow: 'hidden',
              border: '4px solid',
              borderColor: 'white',
            }}
          >
            {previewUrl ? (
              <Avatar 
                src={previewUrl}
                sx={{ 
                  width: '100%', 
                  height: '100%',
                  fontSize: '3.4rem'
                }}
              />
            ) : (
              <Typography variant="h1" sx={{ fontSize: '3.4rem' }}>
                {formData.type ? getPetTypeIcon(formData.type) : '🐾'}
              </Typography>
            )}
          </Box>
          
          {/* Camera/Delete button - changes based on whether a photo is selected */}
          <Box 
            onClick={previewUrl ? handleRemovePhoto : triggerFileInput}
            sx={{
              position: 'absolute',
              bottom: 0,
              right: 0,
              bgcolor: previewUrl ? 'error.main' : 'primary.main',
              color: 'white',
              borderRadius: '50%',
              width: 40,
              height: 40,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              border: '2px solid white',
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              '&:hover': {
                bgcolor: previewUrl ? 'error.dark' : 'primary.dark'
              },
              transition: 'background-color 0.3s ease',
              zIndex: 2
            }}
          >
            {previewUrl ? <DeleteIcon fontSize="small" /> : <AddAPhoto fontSize="small" />}
          </Box>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            style={{ display: 'none' }}
            onChange={handleFileChange}
          />
        </Box>

        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            {/* Error message for photo upload, if any */}
            {errors.photo_url && (
              <Grid item xs={12} sx={{ 
                display: 'flex', 
                justifyContent: 'center',
                mb: 2
              }}>
                <FormHelperText error sx={{ textAlign: 'center' }}>
                  {errors.photo_url}
                </FormHelperText>
              </Grid>
            )}
            
            {/* Form fields */}
            <Grid item xs={12} sm={6}>
              <Autocomplete
                options={customers}
                getOptionLabel={(option) => option.name}
                value={getSelectedCustomer()}
                onChange={handleCustomerChange}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Proprietário"
                    error={!!errors.customer_id}
                    helperText={errors.customer_id}
                    fullWidth
                    required
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Nome do Pet"
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.name}
                helperText={errors.name}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.type}>
                <InputLabel>Tipo de Pet</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  onChange={handleSelectChange}
                  label="Tipo de Pet"
                >
                  {PetTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
                {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="breed"
                label="Raça"
                value={formData.breed}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="age"
                label="Idade (Anos)"
                value={formData.age === null ? '' : formData.age}
                onChange={handleInputChange}
                fullWidth
                type="number"
                inputProps={{ min: 0 }}
                error={!!errors.age}
                helperText={errors.age}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Tamanho</InputLabel>
                <Select
                  name="size"
                  value={formData.size}
                  onChange={handleSelectChange}
                  label="Tamanho"
                >
                  <MenuItem value="">
                    <em>Não especificado</em>
                  </MenuItem>
                  <MenuItem value="Pequeno">Pequeno</MenuItem>
                  <MenuItem value="Médio">Médio</MenuItem>
                  <MenuItem value="Grande">Grande</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Gênero</InputLabel>
                <Select
                  name="gender"
                  value={formData.gender}
                  onChange={handleSelectChange}
                  label="Gênero"
                >
                  <MenuItem value="">
                    <em>Não especificado</em>
                  </MenuItem>
                  <MenuItem value="Macho">Macho</MenuItem>
                  <MenuItem value="Fêmea">Fêmea</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Tipo de Pelagem</InputLabel>
                <Select
                  name="fur_type"
                  value={formData.fur_type}
                  onChange={handleSelectChange}
                  label="Tipo de Pelagem"
                >
                  <MenuItem value="">
                    <em>Não especificado</em>
                  </MenuItem>
                  <MenuItem value="Curto">Curto</MenuItem>
                  <MenuItem value="Médio">Médio</MenuItem>
                  <MenuItem value="Longo">Longo</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="additional_notes"
                label="Observações Adicionais"
                value={formData.additional_notes}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={4}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            Salvar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}); 