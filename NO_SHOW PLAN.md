# NO_SHOW PLAN.md

## Context: Current Auto No-Show Implementation (Legacy)

### Where the Logic Lives
- **Frontend Only:** The current auto no-show logic is implemented entirely in the frontend React app.
- **Key Files:**
  - `src/renderer/components/Appointments/AppointmentCard.tsx`: Contains the timer/grace period logic. If an appointment's scheduled time + 30 minutes has passed and status is still `scheduled`, it triggers a status change to `no_show`.
  - `src/renderer/contexts/NotificationContext.tsx`: Listens for custom events (e.g., `appointment-needs-marking`) and sends notifications when appointments are auto-marked as no-show. Also manages notification deduplication.
  - `src/renderer/pages/Appointments.tsx`: The `handleStatusChange` function updates the appointment status in the database and dispatches events for notification and UI updates.
  - `src/renderer/types/appointments.ts`: Defines the `no_show` status and its labels/colors.

### How It Works
- When the app is open and the Appointments page (or any component using `AppointmentCard`) is rendered, it checks each appointment:
  - If the scheduled time + 30 minutes (grace period) has passed and status is still `scheduled`, it:
    1. Dispatches a custom event to mark the appointment as `no_show`.
    2. Calls the backend to update the status.
    3. Triggers a notification for the admin.
- **Manual marking** (by admin) also triggers similar notification logic.

### Limitations
- **Frontend-dependent:** If the app is closed or the relevant page is not open, no automatic marking occurs.
- **No true background automation:** Only works when the UI is running.
- **No package integration:** Does not trigger package no-show logic (status, logging, etc.).
- **No server-side enforcement:** Not suitable for multi-user/cloud or future backend expansion.

---

## Goal
Redesign the no-show logic for appointments to be robust, automation-friendly, and fully compatible with the offline, local-only nature of the app. Integrate this logic with the package system, ensuring correct handling of package appointments without decrementing remaining_occurrences on no-shows.

---

## 1. Core Approach: Catch-Up No-Show Logic

### 1.1. What is Catch-Up Logic?
- On every app startup and whenever the app regains focus, the system will scan for all appointments that:
  - Have status `scheduled`.
  - Their scheduled time + grace period (e.g., 30 minutes) has already passed.
- For each such appointment, the system will:
  1. Mark the appointment as `no_show` (if not already).
  2. If the appointment is a package appointment, trigger the package no-show logic (see Section 2).
- This ensures that even if the app was closed for days, all overdue appointments are handled immediately on next use.

### 1.2. Why This Approach?
- Works fully offline and locally.
- No dependency on the frontend being open to a specific page.
- No missed no-shows, even if the app is closed for a long time.
- Easy to maintain and test.

---

## 2. Package System Integration

### 2.1. When a Package Appointment is Marked as No-Show (Auto or Manual)
- **Do NOT decrement** the `remaining_occurrences` of the `CustomerPackage`.
- Set the `CustomerPackage.status` to `on_hold_no_show`.
- Log the no-show event in `PackageUsageHistory` with `status_at_usage: 'no_show'`.
- Do NOT auto-schedule the next appointment. The package is paused until admin intervention.
- The package should appear in the "Packages Requiring Attention" dashboard/widget.

### 2.2. Admin Resolution Flow
- Admin can resolve a no-show by selecting a new date/time for the next appointment.
- Upon resolution:
  - Set `CustomerPackage.status` back to `active`.
  - Schedule the next appointment for the chosen date/time.
  - Optionally log the resolution in `PackageUsageHistory`.

---

## 3. Implementation Steps

### 3.1. Backend (Main Process)
- Implement a function that runs on app startup and on window focus:
  - Scans for all `scheduled` appointments where scheduled time + grace period has passed.
  - For each, updates status to `no_show`.
  - If the appointment is a package appointment, calls the package no-show handler.
- Ensure this logic is called from the main Electron process, not just the frontend.

### 3.2. Package No-Show Handler
- When called (auto or manual):
  - Set `CustomerPackage.status` to `on_hold_no_show`.
  - Log in `PackageUsageHistory`.
  - Do NOT decrement `remaining_occurrences`.
  - Do NOT schedule the next appointment.

### 3.3. IPC & Frontend
- Expose an IPC channel to trigger the catch-up logic (for manual testing or future extensibility).
- Update frontend hooks/components to:
  - Remove any direct auto-marking logic from the UI.
  - Listen for package status changes and update the dashboard/widgets accordingly.

### 3.4. Notifications
- Notify the admin when appointments are auto-marked as no-show (especially for package appointments).
- Ensure all notifications are in PT-BR and use Brazil timezone.

### 3.5. Migration
- Remove the old frontend-driven auto no-show logic.
- Ensure all appointment status changes (manual or auto) go through the new backend logic.

---

## 4. Testing
- Test with the app closed for several days, then reopened: all overdue appointments should be correctly marked as no-show.
- Test both regular and package appointments.
- Test admin resolution flow for package no-shows.
- Test notifications and dashboard updates.

---

## 5. Future Improvements
- Optionally, allow configuration of the grace period in app settings.
- Consider logging all auto no-show events for auditability.

---

**Big Pappa, please review and confirm this plan before implementation!** 