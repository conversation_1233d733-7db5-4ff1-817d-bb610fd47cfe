import React, { useState, useEffect, useRef, useCallback, useMemo, lazy, Suspense } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import useMediaQuery from '@mui/material/useMediaQuery';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import useTheme from '@mui/material/styles/useTheme';
import Badge from '@mui/material/Badge';

import { useTheme as useMuiTheme } from '@mui/material/styles';
import { 
  Add as AddIcon, 
  Search as SearchIcon,
  FilterList as FilterIcon,
  TrendingUp as TrendingUpIcon,
  Print as PrintIcon,
  CalendarToday as CalendarIcon,
  Delete as DeleteIcon,
  Inventory as InventoryIcon,
  Close as CloseIcon,
  Warning as WarningIcon,
  ShoppingCart as ShoppingCartIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { SaleCard } from '../components/Sales/SaleCard';
// Use React.lazy for components that don't need to be loaded immediately
//const SaleForm = lazy(() => import('../components/Sales/SaleForm').then(module => ({ default: module.SaleForm })));
import { SaleForm } from '../components/Sales/SaleForm';
const Receipt = lazy(() => import('../components/Sales/Receipt').then(module => ({ default: module.Receipt })));
import { 
  Sale, 
  SaleFormData, 
  SaleItem,
  SaleItemFormData,
  formatCurrency,
  formatDate,
  paymentMethodLabels,
  SaleStatus,
  saleStatusLabels
} from '../types/sales';
import { Product } from '../types/inventory';
import { Customer } from '../types/customers';
import { Service } from '../types/appointments';
import { useSales } from '../hooks/useSales';
import { useCustomers } from '../hooks/useCustomers';
import { useProducts } from '../hooks/useProducts';
import { useServices } from '../hooks/useServices';
import { useNotifications } from '../contexts/NotificationContext';
import { useLocation } from 'react-router-dom';
import { usePets } from '../hooks/usePets';
import { useCustomerPackages } from '../hooks/useCustomerPackages';
// Import React Virtualized components
import {
  WindowScroller,
  AutoSizer,
  List,
  CellMeasurer,
  CellMeasurerCache
} from 'react-virtualized';

// Helper function to adapt database sale model to frontend type
const adaptSale = (dbSale: any): Sale => {
  // Handle the case where customer might be either an ID or a full object
  let customerId = 0;
  if (dbSale.customer) {
    if (typeof dbSale.customer === 'number') {
      customerId = dbSale.customer;
    } else if (typeof dbSale.customer === 'object' && dbSale.customer !== null) {
      customerId = dbSale.customer.id;
    }
  } else if (dbSale.customer_id) {
    customerId = dbSale.customer_id;
  } else if (dbSale.customerId) {
    customerId = dbSale.customerId;
  }
  
  return {
    id: dbSale.id,
    // Handle both camelCase (from DB) and snake_case (for frontend)
    customer_id: customerId,
    customerId: customerId,
    total_amount: dbSale.total_amount || 0,
    payment_method: dbSale.payment_method || 'cash',
    status: dbSale.status || 'paid', // Default to 'paid' for backward compatibility
    sale_date: dbSale.sale_date || new Date(),
    created_at: dbSale.created_at || new Date(),
    updated_at: dbSale.updated_at || new Date(),
    customer: dbSale.customer && typeof dbSale.customer === 'object' ? adaptCustomer(dbSale.customer) : undefined,
    items: dbSale.items || []
  };
};

// Helper function to adapt database sale item model to frontend type
const adaptSaleItem = (item: any, products: Product[], services: Service[]): SaleItem => {
  // Handle null or undefined item case
  if (!item) {
    return {
      id: 0,
      saleId: 0,
      sale_id: 0,
      productId: null,
      product_id: null,
      serviceId: null,
      service_id: null,
      quantity: 0,
      price_per_unit: 0,
      product: undefined,
      service: undefined,
      customer_package_id: null,
      is_package_service: false
    };
  }

  let product;
  let service;
  let productId = null;
  let serviceId = null;

  // Priority 1: Use product data directly attached to the item from the backend
  if (item.product && typeof item.product === 'object') {
    product = item.product;
    productId = item.product.id;
  } 
  // Priority 2: Fallback to finding in the active list
  else if (item.productId) {
    productId = item.productId;
    product = products && Array.isArray(products) 
      ? products.find((p: Product) => p && p.id === item.productId) 
      : undefined;
    
    // If product not found, create a placeholder for deleted products
    if (!product && productId) {
      product = {
        id: productId,
        name: "Produto Indisponível",
        category: '',
        description: '',
        price: item.price_per_unit || 0,
        stock_quantity: 0,
        min_stock_level: 0,
        is_deleted: true,
        created_at: new Date(),
        updated_at: new Date()
      };
    }
  }

  // Priority 1: Use service data directly attached to the item from the backend  
  if (item.service && typeof item.service === 'object') {
    service = item.service;
    serviceId = item.service.id;
  }
  // Priority 2: Fallback to finding in the active list
  else if (item.serviceId) {
    serviceId = item.serviceId;
    service = services && Array.isArray(services)
      ? services.find((s: Service) => s && s.id === item.serviceId)
      : undefined;
    
    // If service not found, create a placeholder for deleted services
    if (!service && serviceId) {
      service = {
        id: serviceId,
        name: "Serviço Indisponível",
        price: item.price_per_unit || 0,
        duration_minutes: 30
      };
    }
  }

  // Check if this is a package service
  const isPackageService = !!item.customer_package_id || !!item.is_package_service;

  return {
    id: item.id || 0,
    saleId: item.saleId || 0,
    sale_id: item.saleId || 0,
    productId: productId,
    product_id: productId,
    serviceId: serviceId,
    service_id: serviceId,
    quantity: item.quantity || 0,
    price_per_unit: item.price_per_unit || 0,
    product,
    service,
    customer_package_id: item.customer_package_id || null,
    is_package_service: isPackageService
  };
};

// Helper function to adapt database customer model to frontend type
const adaptCustomer = (dbCustomer: any): Customer => {
  if (!dbCustomer) {
    return {
      id: 0,
      name: 'Cliente não informado',
      email: '',
      phone: '',
      address: '',
      additional_notes: '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  return {
    id: dbCustomer.id || 0,
    name: dbCustomer.name || '',
    email: dbCustomer.email || '',
    phone: dbCustomer.phone || '',
    address: dbCustomer.address || '',
    additional_notes: dbCustomer.additional_notes || '',
    created_at: dbCustomer.created_at || new Date().toISOString(),
    updated_at: dbCustomer.updated_at || new Date().toISOString()
  };
};

const adaptProduct = (dbProduct: any): Product => {
  if (!dbProduct) {
    return {
      id: 0,
      name: '',
      category: '',
      description: '',
      price: 0,
      cost_price: 0,
      stock_quantity: 0,
      min_stock_level: 0,
      is_deleted: false,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  return {
    id: dbProduct.id || 0,
    name: dbProduct.name || '',
    category: dbProduct.category || '',
    description: dbProduct.description || '',
    price: dbProduct.price || 0,
    cost_price: dbProduct.cost_price || 0,
    stock_quantity: dbProduct.stock_quantity || 0,
    min_stock_level: dbProduct.min_stock_level || 0,
    is_deleted: dbProduct.is_deleted || false,
    created_at: dbProduct.created_at || new Date(),
    updated_at: dbProduct.updated_at || new Date()
  };
};

// Helper function to adapt database service model to frontend type
const adaptService = (dbService: any): Service => {
  if (!dbService) {
    return {
      id: 0,
      name: 'Serviço não encontrado',
      price: 0,
      duration_minutes: 0
    };
  }

  return {
    id: dbService.id,
    name: dbService.name || 'Serviço não encontrado',
    price: dbService.price || 0,
    duration_minutes: dbService.duration_minutes || 0
  };
};

// Add pet adapter function near other adapters
const adaptPet = (dbPet: any): any => {
  if (!dbPet) {
    return {
      id: 0,
      customer_id: 0,
      name: '',
      type: '',
      breed: null,
      age: null,
      size: null,
      additional_notes: null,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  return {
    id: dbPet.id || 0,
    customer_id: dbPet.customer_id || (dbPet.customer ? dbPet.customer.id : 0),
    name: dbPet.name || '',
    type: dbPet.type || '',
    breed: dbPet.breed || null,
    age: dbPet.age || null,
    size: dbPet.size || null,
    additional_notes: dbPet.additional_notes || null,
    created_at: dbPet.created_at || new Date(),
    updated_at: dbPet.updated_at || new Date(),
    customer: dbPet.customer ? {
      id: dbPet.customer.id,
      name: dbPet.customer.name,
      email: dbPet.customer.email || null,
      phone: dbPet.customer.phone || null
    } : undefined
  };
};

enum ViewMode {
  LIST = 'list',
  NEW_SALE = 'new_sale'
}

const Sales: React.FC = () => {
  // Use hook and extract getSaleById
  const { 
    sales: dbSales, 
    loading: salesLoading, 
    error: salesError,
    createSale,
    getSaleItems,
    getSaleById,
    deleteSale: deleteSaleFromDb,
    updateSaleStatus,
    refetch: refetchSales
  } = useSales();
  
  const {
    customers: dbCustomers,
    loading: customersLoading,
    error: customersError
  } = useCustomers();

  const {
    products: dbProducts,
    loading: productsLoading,
    error: productsError,
    refetch: refetchProducts
  } = useProducts();

  const {
    services: dbServices,
    loading: servicesLoading,
    error: servicesError,
    getServiceById
  } = useServices();

  // Add the pets hook
  const {
    pets: dbPets,
    loading: petsLoading,
    error: petsError
  } = usePets();

  // Add the packages hook
  const { getCustomerPackages } = useCustomerPackages();

  // Add the notifications context
  const { addNotification } = useNotifications();

  // Add state for appointment data from previous page
  const location = useLocation();
  const appointmentData = location.state?.appointmentData;
  const createFromAppointment = location.state?.createFromAppointment;

  // Convert database models to frontend types
  const customers = useMemo(() => dbCustomers ? dbCustomers.map(adaptCustomer) : [], [dbCustomers]);
  const products = useMemo(() => dbProducts ? dbProducts.map(adaptProduct) : [], [dbProducts]);
  const services = useMemo(() => dbServices ? dbServices.map(adaptService) : [], [dbServices]);
  const pets = useMemo(() => dbPets ? dbPets.map(adaptPet) : [], [dbPets]);

  // State for sales with items
  const [sales, setSales] = useState<Sale[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const prevSearchTermRef = useRef('');
  const prevShowOnlyPendingRef = useRef(false);
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.LIST);
  const [selectedSaleForReceipt, setSelectedSaleForReceipt] = useState<Sale | null>(null);
  const [loadedSales, setLoadedSales] = useState<any[]>([]);
  const [isLoadingDetails, setIsLoadingDetails] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Add new state for pending sales filter
  const [showOnlyPendingSales, setShowOnlyPendingSales] = useState<boolean>(false);
  
  // Add new state for the delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [saleToDelete, setSaleToDelete] = useState<number | null>(null);
  
  const muiTheme = useMuiTheme();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  // Add a ref to track which sales we've already loaded to prevent repeated fetching
  const loadedSalesRef = useRef<Set<number>>(new Set());

  // Add state for lazy loading
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMoreSales, setHasMoreSales] = useState<boolean>(true);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const SALES_PER_PAGE = 10; // Number of sales to load per page
  const salesEndRef = useRef<HTMLDivElement>(null);

  // Read thresholdDays setting from localStorage once for all SaleCard components
  const [thresholdDays, setThresholdDays] = useState<number>(30); // Default to 30 days
  
  // Load threshold days setting on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('pendingSalesSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        setThresholdDays(settings.thresholdDays);
      }
    } catch (error) {
      console.error('[SALES] Error loading threshold days setting:', error);
    }
  }, []);

  // Add CellMeasurerCache for variable height rows
  const cache = useMemo(() => new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 300, // Approximate height of a sale card
    minHeight: 250,
  }), []);
  
  // Reset cache when filter criteria change
  useEffect(() => {
    // Check if the actual filter criteria changed
    if (debouncedSearchTerm !== prevSearchTermRef.current || showOnlyPendingSales !== prevShowOnlyPendingRef.current) {
      cache.clearAll();
    }
    // Update refs for the next comparison
    prevSearchTermRef.current = debouncedSearchTerm;
    prevShowOnlyPendingRef.current = showOnlyPendingSales;
  }, [debouncedSearchTerm, showOnlyPendingSales, cache]); // Keep cache in deps

  // Add cleanup effect for cache
  useEffect(() => {
    return () => {
      // Clear cache when component unmounts
      cache.clearAll();
    };
  }, [cache]);

  // Calculate number of columns based on screen width
  const getColumnCount = (width: number) => {
    if (width < 600) return 1; // Mobile view
    return 2; // Desktop view - 2 columns
  };

  // Calculate row count based on items and columns
  const getRowCount = (itemCount: number, columnCount: number) => {
    return Math.ceil(itemCount / columnCount);
  };

  // Get item at index accounting for column layout
  const getItemAtIndex = (items: Sale[], rowIndex: number, columnIndex: number, columnCount: number) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    return itemIndex < items.length ? items[itemIndex] : null;
  };

  // Dedicated search function to ensure consistent filtering
  const performSearch = useCallback((term: string, allSales: Sale[]) => {
    // Simple early return if no search term
    if (!term.trim()) {
      return [...(allSales || [])];
    }
    
    // Exit early if no sales
    if (!allSales || allSales.length === 0) {
      return [];
    }
    
    const normalizedTerm = term.toLowerCase().trim();
    
    // Define month mapping for Portuguese
    const monthMap: Record<string, number> = {
      'janeiro': 0, 'jan': 0,
      'fevereiro': 1, 'fev': 1,
      'março': 2, 'mar': 2, 'marco': 2,
      'abril': 3, 'abr': 3,
      'maio': 4, 'mai': 4,
      'junho': 5, 'jun': 5,
      'julho': 6, 'jul': 6,
      'agosto': 7, 'ago': 7,
      'setembro': 8, 'set': 8,
      'outubro': 9, 'out': 9,
      'novembro': 10, 'nov': 10,
      'dezembro': 11, 'dez': 11
    };
    
    // Check if search term might be a month
    const searchingForMonth = Object.keys(monthMap).some(month => 
      month.includes(normalizedTerm) || normalizedTerm.includes(month)
    );
    
    // Check if search term might be a specific date like "20 de março" or "20 de mar"
    let dayToSearch: number | null = null;
    let monthToSearch: number | null = null;
    
    // Regex to match patterns like "X de Y" where X is a number and Y is a month
    const dateRegex = /(\d+)\s+de\s+([a-zç]+)/i;
    const dateMatch = normalizedTerm.match(dateRegex);
    
    if (dateMatch && dateMatch.length >= 3) {
      const day = parseInt(dateMatch[1], 10);
      const monthStr = dateMatch[2].toLowerCase();
      
      // Find the matching month
      for (const [monthName, monthIndex] of Object.entries(monthMap)) {
        if (monthName.includes(monthStr) || monthStr.includes(monthName)) {
          dayToSearch = day;
          monthToSearch = monthIndex;
          break;
        }
      }
    }
    
    // Perform matching with detailed logging
    const results = allSales.filter(sale => {
      // 1. Check customer name
      if (sale.customer?.name && 
          sale.customer.name.toLowerCase().includes(normalizedTerm)) {
        return true;
      }
      
      // 2. Check payment method
      if (sale.payment_method) {
        const methodLabel = paymentMethodLabels[sale.payment_method as keyof typeof paymentMethodLabels]?.toLowerCase();
        if (methodLabel && methodLabel.includes(normalizedTerm)) {
          return true;
        }
      }
      
      // 3. Check formatted date
      if (sale.sale_date) {
        try {
          const formattedDate = formatDate(sale.sale_date.toString()).toLowerCase();
          if (formattedDate.includes(normalizedTerm)) {
            return true;
          }
          
          // 3.1 Check for specific day and month match if parsing succeeded
          if (dayToSearch !== null && monthToSearch !== null) {
            const saleDate = new Date(sale.sale_date);
            const saleDay = saleDate.getDate();
            const saleMonth = saleDate.getMonth();
            
            if (saleDay === dayToSearch && saleMonth === monthToSearch) {
              return true;
            }
          }
          // 3.2 Check if searching for month matches this sale's date
          else if (searchingForMonth) {
            const saleDate = new Date(sale.sale_date);
            const saleMonth = saleDate.getMonth(); // 0-11
            
            // Check if any month name/abbreviation in our search matches this sale's month
            for (const [monthName, monthIndex] of Object.entries(monthMap)) {
              if ((monthName.includes(normalizedTerm) || normalizedTerm.includes(monthName)) && 
                  saleMonth === monthIndex) {
                return true;
              }
            }
          }
        } catch (e) {}
      }
      
      // 4. Check price formats
      if (sale.total_amount !== undefined) {
        const formattedPrice = formatCurrency(sale.total_amount).toLowerCase();
        const priceWithComma = sale.total_amount.toString().replace('.', ',');
        
        if (formattedPrice.includes(normalizedTerm)) {
          return true;
        }
        
        if (priceWithComma.includes(normalizedTerm)) {
          return true;
        }
      }
      
      // 5. Check items
      if (sale.items && sale.items.length > 0) {
        for (const item of sale.items) {
          if (item.product?.name && 
              item.product.name.toLowerCase().includes(normalizedTerm)) {
            return true;
          }
          
          if (item.service?.name && 
              item.service.name.toLowerCase().includes(normalizedTerm)) {
            return true;
          }
        }
      }
      
      return false;
    });
    
    return results;
  }, []);
  
  // Add a new function to batch fetch sale items for multiple sales
  const batchGetSaleItems = useCallback(async (saleIds: number[]): Promise<Record<number, any[]>> => {
    if (!saleIds.length) return {};
    
    // Create a map to store results
    const itemsMap: Record<number, any[]> = {};
    
    try {
      // Call the backend API - here we simulate batching with Promise.all
      // In a real implementation, you would create a new backend endpoint that accepts an array of IDs
      const results = await Promise.all(saleIds.map(id => getSaleItems(id)));
      
      // Map results to their respective sale IDs
      saleIds.forEach((id, index) => {
        itemsMap[id] = results[index] || [];
      });
      
      return itemsMap;
    } catch (error) {
      console.error('[Sales] Error batch fetching sale items:', error);
      return {};
    }
  }, [getSaleItems]);
  
  // Add a function to batch fetch missing service details
  const batchGetServiceDetails = useCallback(async (serviceIds: number[]): Promise<Record<number, Service>> => {
    if (!serviceIds.length) return {};
    
    // Create a map to store results
    const servicesMap: Record<number, Service> = {};
    
    try {
      // Call the backend API - here we simulate batching with Promise.all
      // In a real implementation, you would create a new backend endpoint that accepts an array of IDs
      const results = await Promise.all(serviceIds.map(id => getServiceById(id)));
      
      // Map results to their respective service IDs
      serviceIds.forEach((id, index) => {
        if (results[index]) {
          // Ensure result is not null before assigning
          servicesMap[id] = results[index] as Service;
        }
      });
      
      return servicesMap;
    } catch (error) {
      console.error('[Sales] Error batch fetching service details:', error);
      return {};
    }
  }, [getServiceById]);

  // Add debounce effect for search term
  useEffect(() => {
    // Set a timeout to update the debounced search term
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 150); // 150ms debounce delay
    
    // Cleanup function to clear the timeout if component unmounts or searchTerm changes again
    return () => {
      clearTimeout(timeoutId);
    };
  }, [searchTerm]);
  
  // Apply the search whenever debounced search term, sales, or pending filter changes
  useEffect(() => {
    // If no search term and no pending filter, use the regular lazy loading approach
    if (!debouncedSearchTerm.trim() && !showOnlyPendingSales) {
      if (sales.length > 0) {
        // Sort sales by date, newest first
        const sortedSales = [...sales].sort((a, b) => {
          const dateA = new Date(a.sale_date).getTime();
          const dateB = new Date(b.sale_date).getTime();
          return dateB - dateA;
        });
        setFilteredSales(sortedSales);
      } else {
        setFilteredSales([]);
      }
      return;
    }
    
    // When searching or filtering for pending sales, we need to consider ALL sales in dbSales
    const executeSearch = async () => {
      // Set loading state
      if (!isLoadingDetails) {
        setIsLoadingDetails(true);
      }
      
      try {
        // First, check if we have basic data in dbSales
        if (dbSales && dbSales.length > 0) {
          // Convert all dbSales to adapted format for basic search
          const adaptedBasicSales = dbSales.map(adaptSale);
          
          // Run initial basic search across all database sales
          const basicMatches = performSearch(debouncedSearchTerm, adaptedBasicSales);
          
          // Apply pending filter if enabled
          const filteredBasicMatches = showOnlyPendingSales 
            ? basicMatches.filter(sale => sale.status === 'pending')
            : basicMatches;
          
          // Find which matching sales are already loaded with details
          const loadedMatches = filteredBasicMatches.filter(match => 
            sales.some(loadedSale => loadedSale.id === match.id)
          );
          
          // Find which matching sales need to be loaded
          const unloadedMatchIds = filteredBasicMatches
            .filter(match => !sales.some(loadedSale => loadedSale.id === match.id))
            .map(match => match.id);
          
          // Create a map of all matches for quick lookup
          const matchMap = new Map(
            filteredBasicMatches.map(match => [match.id, match])
          );
          
          // If there are unloaded matches that we need to fetch details for
          let newlyLoadedSales: Sale[] = [];
          if (unloadedMatchIds.length > 0) {
            // For each unloaded match, fetch its details asynchronously
            const detailedSalesPromises = unloadedMatchIds.map(async (id) => {
              try {
                // Mark this sale as processed so we don't try to load it again
                loadedSalesRef.current.add(id);
                
                // Get the sale items
                const items = await getSaleItems(id);
                
                // Get the basic match as a starting point
                const basicMatch = matchMap.get(id);
                if (!basicMatch) return null;
                
                // Create a new enhanced sale with items
                const enhancedSale: Sale = {
                  ...basicMatch,
                  items: items.map(item => adaptSaleItem(item, products, services))
                };
                
                return enhancedSale;
              } catch (err) {
                console.error(`Error loading details for sale ${id}:`, err);
                return null;
              }
            });
            
            // Wait for all sale details to be loaded
            const loadedDetailedSales = await Promise.all(detailedSalesPromises);
            
            // Filter out any nulls from failed loads
            newlyLoadedSales = loadedDetailedSales.filter(Boolean) as Sale[];
            
            // Update the sales state with these newly loaded sales
            if (newlyLoadedSales.length > 0) {
              setSales(prevSales => {
                // Create a new array with all previous sales
                const updatedSales = [...prevSales];
                
                // Add or update with newly loaded sales
                newlyLoadedSales.forEach(newSale => {
                  const existingIndex = updatedSales.findIndex(s => s.id === newSale.id);
                  if (existingIndex >= 0) {
                    // Update existing
                    updatedSales[existingIndex] = newSale;
                  } else {
                    // Add new
                    updatedSales.push(newSale);
                  }
                });
                
                return updatedSales;
              });
            }
          }
          
          // Create the final results list with both previously loaded and newly loaded sales
          const finalResults = [
            ...loadedMatches.map(match => {
              // Find the fully loaded version of this sale if it exists
              const loadedVersion = sales.find(s => s.id === match.id);
              return loadedVersion || match;
            }),
            ...newlyLoadedSales
          ];
          
          // Remove duplicates (a sale might be in both loadedMatches and newlyLoadedSales)
          const uniqueResults = finalResults.filter((sale, index, self) => 
            index === self.findIndex(s => s.id === sale.id)
          );
          
          // Sort results by date, newest first
          uniqueResults.sort((a, b) => {
            const dateA = new Date(a.sale_date).getTime();
            const dateB = new Date(b.sale_date).getTime();
            return dateB - dateA;
          });
          
          // Update the filtered sales
          setFilteredSales(uniqueResults);
        } else {
          // No sales in database
          setFilteredSales([]);
        }
      } catch (error) {
        console.error('Error performing sales search:', error);
      } finally {
        // Clear loading state
        setIsLoadingDetails(false);
      }
    };
    
    // Run the search
    executeSearch();
    
  }, [debouncedSearchTerm, sales, dbSales, showOnlyPendingSales, performSearch, adaptSale, products, services, getSaleItems, isLoadingDetails]);

  // Replace the useEffect for loading sales with a lazy loading approach
  useEffect(() => {
    // Only run if we have all the necessary data and we're not loading
    if (salesLoading) {
      return;
    }
    
    // If there are no sales in the database, set empty arrays and return early
    if (!dbSales || dbSales.length === 0) {
      setSales([]);
      setFilteredSales([]);
      setHasMoreSales(false);
      return;
    }
    
    // Skip if we're already loading details
    if (isLoadingDetails) {
      return;
    }
    
    setIsLoadingDetails(true);
    
    // Calculate which sales to load for the current page
    const startIndex = 0;
    const endIndex = currentPage * SALES_PER_PAGE;
    const salesToLoad = dbSales
      .slice(0, endIndex)
      .filter(sale => !loadedSalesRef.current.has(sale.id));
    
    // Check if we've reached the end of available sales
    if (endIndex >= dbSales.length) {
      setHasMoreSales(false);
    }
    
    // If no new sales to load, just finish
    if (salesToLoad.length === 0) {
      setIsLoadingDetails(false);
      return;
    }
    
    // Add a safety timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (isLoadingDetails) {
        console.error('[Sales] Timeout occurred while loading sales details');
        setIsLoadingDetails(false);
        setError('Timeout loading sales data. Please try again.');
      }
    }, 10000); // 10 second timeout
    
    // Get all sale IDs to fetch items for
    const saleIds = salesToLoad.map(sale => sale.id);
    
    // OPTIMIZATION: Batch fetch sale items instead of fetching individually
    const loadSalesData = async () => {
      try {
        // Step 1: Batch fetch all sale items
        const allSaleItems = await batchGetSaleItems(saleIds);
        
        // Step 2: Collect all service IDs that need to be fetched
        const missingServiceIds = new Set<number>();
        
        // Loop through all items to find missing service objects but present service IDs
        Object.values(allSaleItems).forEach(items => {
          items.forEach((item: any) => {
            if (item.serviceId && !item.service) {
              missingServiceIds.add(item.serviceId);
            }
          });
        });
        
        // Step 3: Batch fetch missing service details
        const serviceDetailsMap = await batchGetServiceDetails(Array.from(missingServiceIds));
        
        // Step 4: Create enhanced sales
        const enhancedSales = salesToLoad.map(sale => {
          // Mark this sale as loaded
          loadedSalesRef.current.add(sale.id);
          
          // Create base sale object
          const enhancedSale = adaptSale(sale);
          
          // Get items for this sale
          const items = allSaleItems[sale.id] || [];
          
          // Process and enhance each item
          enhancedSale.items = items.map((item: any) => {
            // Create enhanced item with product info
            const enhancedItem = adaptSaleItem(item, products, services);
            
            // If this item has a service ID but no service object, add it from our batch fetched map
            if (enhancedItem.service_id && !enhancedItem.service && serviceDetailsMap[enhancedItem.service_id]) {
              enhancedItem.service = serviceDetailsMap[enhancedItem.service_id];
            } else if (enhancedItem.service_id && !enhancedItem.service) {
              // Create fallback service if still missing
              enhancedItem.service = {
                id: enhancedItem.service_id,
                name: "Serviço Indisponível",
                price: enhancedItem.price_per_unit || 0,
                duration_minutes: 0
              };
            }
            
            // Ensure package information is correctly set
            enhancedItem.is_package_service = !!enhancedItem.customer_package_id || !!enhancedItem.is_package_service;
            
            return enhancedItem;
          });
          
          return enhancedSale;
        });
        
        // Update state with new enhanced sales
        setSales(prevSales => {
          const combinedSales = [...prevSales];
          
          // Add or replace the enhanced sales
          enhancedSales.forEach(newSale => {
            const existingIndex = combinedSales.findIndex(s => s.id === newSale.id);
            if (existingIndex >= 0) {
              combinedSales[existingIndex] = newSale;
            } else {
              combinedSales.push(newSale);
            }
          });
          
          // Sort sales by date, newest first
          combinedSales.sort((a, b) => {
            const dateA = new Date(a.sale_date).getTime();
            const dateB = new Date(b.sale_date).getTime();
            return dateB - dateA;
          });
          
          return combinedSales;
        });
        
        setIsLoadingDetails(false);
        setIsLoadingMore(false);
        clearTimeout(timeoutId);
      } catch (error: any) {
        console.error('[Sales] Error enhancing sales:', error);
        setIsLoadingDetails(false);
        setIsLoadingMore(false);
        setError('Failed to load sales details. Please try again.');
        clearTimeout(timeoutId);
      }
    };
    
    loadSalesData();
    
    // Cleanup timeout on unmount
    return () => {
      clearTimeout(timeoutId);
    };
  }, [dbSales, salesLoading, products, services, currentPage, batchGetSaleItems, batchGetServiceDetails, adaptSale]);

  // Improved search handlers
  const handleClearSearch = () => {
    setSearchTerm('');
    setDebouncedSearchTerm(''); // Also clear the debounced term immediately
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newTerm = event.target.value;
    setSearchTerm(newTerm);
  };

  const handleNewSale = () => {
    // Only allow changing to new sale mode if we're not loading data
    if (isLoadingDetails) {
      return;
    }
    setViewMode(ViewMode.NEW_SALE);
  };

  const handleCancelNewSale = () => {
    setViewMode(ViewMode.LIST);
  };

  const handleSaveSale = async (saleData: SaleFormData) => {
    try {
      // Transform form data into the expected format for the API
      const apiSaleData = {
        customerId: saleData.customer_id,
        payment_method: saleData.payment_method,
        status: saleData.status, // Make sure status is passed to API
        total_amount: saleData.items.reduce((sum, item) => {
          // Don't include package services in the total amount calculation
          return sum + (item.is_package_service ? 0 : (item.price_per_unit * item.quantity));
        }, 0)
      };
      
      // Convert product/service IDs to the expected format
      const apiItems = saleData.items.map(item => {
        const apiItem: any = {
          quantity: item.quantity,
          price_per_unit: item.is_package_service ? 0 : item.price_per_unit
        };
        
        if (item.type === 'product') {
          apiItem.productId = item.id;
          apiItem.serviceId = null;
        } else {
          apiItem.productId = null;
          apiItem.serviceId = item.id;
        }
        
        if (item.petId) {
          apiItem.petId = item.petId;
        }
        
        if (item.is_package_service && item.customer_package_id) {
          apiItem.customer_package_id = item.customer_package_id;
          apiItem.is_package_service = true;
        }
        
        return apiItem;
      });
      
      // Create the sale
      const createdSale = await createSale(apiSaleData, apiItems);
      
      if (createdSale) {
        // Process package items - decrement remaining services for each package used
        const packageItems = saleData.items.filter(item => 
          item.type === 'package' && item.customer_package_id && item.is_package_service
        );
        
        if (packageItems.length > 0 && createdSale) {
          // Process each package use sequentially
          for (const packageItem of packageItems) {
            if (packageItem.customer_package_id) {
              try {
                // Find the sale item in the created sale that corresponds to this package item
                const items = await getSaleItems(createdSale.id);
                const saleItem = items.find(si => 
                  ((si.service?.id === packageItem.id || si.service?.name === packageItem.name)) && 
                  si.customer_package_id === packageItem.customer_package_id
                );
                
                // Pass additional info to usePackageService
                await usePackageService(packageItem.customer_package_id);
              } catch (error) {
                console.error(`Error updating package usage for ID ${packageItem.customer_package_id}:`, error);
              }
            }
          }
        }

        // Show a success notification
        addNotification({
          title: 'Venda Realizada',
          message: `Venda #${createdSale.id} foi cadastrada com sucesso`,
          type: 'success'
        });
        
        // Refresh the sales list
        await refetchSales();
        await refetchProducts(); // Refresh products to update stock quantities
        
        // Only show the receipt if the sale is paid
        if (saleData.status === 'paid') {
          // Load the sale with items for the receipt
          const sale = await getSaleById(createdSale.id);
          if (sale) {
            // Load the items for the sale
            const items = await getSaleItems(sale.id);
            
            // Create a frontend sale object for the receipt
            const saleWithItems: Sale = {
              ...adaptSale(sale),
              items: items.map(item => adaptSaleItem(item, products, services))
            };
            
            // Show the receipt 
            setSelectedSaleForReceipt(saleWithItems);
          }
        }
        
        // Return to list view
        setViewMode(ViewMode.LIST);
      }
    } catch (error) {
      console.error('Error creating sale:', error);
      addNotification({
        title: 'Erro ao Cadastrar Venda',
        message: `Não foi possível cadastrar a venda: ${error}`,
        type: 'error'
      });
    }
  };

  const handlePrintReceipt = (saleId: number) => {
    const sale = sales.find(s => s.id === saleId);
    if (sale) {
      // Make a deep copy to ensure we don't modify the original sale object
      const receiptSale = { 
        ...sale,
        items: sale.items.map(item => {
          // Determine if this is a package service item
          const isPackageItem = !!item.customer_package_id || !!item.is_package_service;
          
          // Create a new item with correct package flags
          return {
            ...item,
            // Keep the original customer_package_id or set to null
            customer_package_id: item.customer_package_id || null,
            is_package_service: isPackageItem
          };
        })
      } as Sale; // Cast to Sale type to satisfy TypeScript
      
      setSelectedSaleForReceipt(receiptSale);
    }
  };

  const handleCloseReceipt = () => {
    setSelectedSaleForReceipt(null);
  };

  // Function to check if a sale has products that can be restocked
  const hasSaleRestockableItems = (saleId: number): boolean => {
    const sale = sales.find(s => s.id === saleId);
    if (!sale || !sale.items || sale.items.length === 0) {
      return false;
    }
    
    // Check if there's at least one product item (not a service)
    return sale.items.some(item => item.product_id !== null && item.product);
  };

  // Modify handleDeleteSale to check for restockable items
  const handleDeleteSale = (saleId: number) => {
    setSaleToDelete(saleId);
    setDeleteDialogOpen(true);
  };

  // New function to perform the actual deletion with or without restocking
  const performDeleteSale = async (withRestocking: boolean) => {
    if (saleToDelete === null) return;
    
    try {
      // Pass the withRestocking parameter to the delete function
      const success = await deleteSaleFromDb(saleToDelete, withRestocking);
      
      if (success) {
        // Immediately update local state to remove the deleted sale
        setSales(prevSales => prevSales.filter(sale => sale.id !== saleToDelete));
        
        // Also update filtered sales state
        setFilteredSales(prevSales => prevSales.filter(sale => sale.id !== saleToDelete));
        
        // Clear the sale from our loaded sales ref
        loadedSalesRef.current.delete(saleToDelete);
        
        // Then refetch to ensure everything is in sync
        await refetchSales();
        
        // If we're restocking, also refresh the products data
        if (withRestocking) {
          await refetchProducts();
        }
      }
    } catch (error) {
      console.error('Error deleting sale:', error);
    } finally {
      // Close the dialog
      handleCloseDeleteDialog();
    }
  };

  // Close the delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSaleToDelete(null);
  };

  // Handle loading states for the component (rename variables)
  const isLoading = salesLoading || customersLoading || productsLoading || servicesLoading || petsLoading;
  const hasError = salesError || customersError || productsError || servicesError || petsError;

  const [storeInfo, setStoreInfo] = useState({
    name: 'Pet Shop Management',
    address: 'Rua Principal, 123, Centro, São Paulo - SP',
    phone: '(11) 98765-4321',
    email: '<EMAIL>',
    website: 'www.petshop.com.br',
    cnpj: '12.345.678/0001-90',
    additionalInfo: 'Obrigado pela preferência!'
  });

  // Load store info from localStorage
  useEffect(() => {
    const savedStoreInfo = localStorage.getItem('storeInfo');
    if (savedStoreInfo) {
      setStoreInfo(JSON.parse(savedStoreInfo));
    }
  }, []);

  // Check for appointment data on component mount
  useEffect(() => {
    if (createFromAppointment && appointmentData) {
      // Switch to new sale mode automatically
      setViewMode(ViewMode.NEW_SALE);
    }
  }, [createFromAppointment, appointmentData]);

  // Add a method to handle updating sale status
  const handleUpdateSaleStatus = async (saleId: number, status: SaleStatus) => {
    try {
      const updatedSale = await updateSaleStatus(saleId, status);
      if (updatedSale) {
        addNotification({
          title: 'Status Atualizado',
          message: `Venda #${saleId} atualizada para ${status === 'paid' ? 'Paga' : status}`,
          type: 'success'
        });
        
        // Refresh sales data
        await refetchSales();
        // Also update our local state
        setSales(prev => prev.map(sale => 
          sale.id === saleId ? { ...sale, status } : sale
        ));
      }
    } catch (error) {
      console.error(`Error updating sale status:`, error);
      addNotification({
        title: 'Erro',
        message: `Erro ao atualizar status da venda: ${error}`,
        type: 'error'
      });
    }
  };

  // Add toggle function for pending sales filter
  const toggleShowPendingSales = () => {
    setShowOnlyPendingSales(!showOnlyPendingSales);
  };

  // Calculate pending sales count
  const pendingSalesCount = useMemo(() => 
    sales.filter(sale => sale.status === 'pending').length
  , [sales]);

  // Add local usePackageService implementation
  const usePackageService = async (customerPackageId: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('customerPackages:useService', customerPackageId);
      return response.success;
    } catch (err) {
      return false;
    }
  };

  // Show a loading indicator for initial data fetch
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show any errors that occurred
  if (hasError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar dados: {hasError}
        </Alert>
      </Box>
    );
  }

  // Main render with view modes and loading state awareness
  return (
    <Box sx={{ p: 3 }}>
      {/* Show loading indicator while loading details */}
      {isLoadingDetails && !isLoadingMore && (
        <Box sx={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 1000
        }}>
          <CircularProgress />
        </Box>
      )}
      {viewMode === ViewMode.LIST ? (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ShoppingCartIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 'bold', 
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -8,
                    left: 0,
                    width: 60,
                    height: 4,
                    backgroundColor: theme.palette.primary.main,
                    borderRadius: 2,
                  }
                }}
              >
                Gerenciamento de Vendas
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {/* Add pending sales filter button with badge */}
              <Badge 
                badgeContent={pendingSalesCount} 
                color="warning"
                sx={{ 
                  '& .MuiBadge-badge': { 
                    fontSize: '0.6rem', 
                    height: '16px', 
                    minWidth: '16px' 
                  }
                }}
                invisible={pendingSalesCount === 0}
              >
                <Button
                  variant={showOnlyPendingSales ? "contained" : "outlined"}
                  onClick={toggleShowPendingSales}
                  color="warning"
                  startIcon={<PaymentIcon />}
                >
                  {showOnlyPendingSales ? "Todas as Vendas" : "Vendas Pendentes"}
                </Button>
              </Badge>
              
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleNewSale}
              >
                Nova Venda
              </Button>
            </Box>
          </Box>

          {/* Display any errors */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              placeholder="Buscar por cliente, método de pagamento, data ou valor"
              variant="outlined"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm ? (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="limpar busca"
                      onClick={handleClearSearch}
                      edge="end"
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </InputAdornment>
                ) : null,
              }}
            />
            {searchTerm && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Buscando por: "{searchTerm}"
                </Typography>
              </Box>
            )}
          </Box>

          <Divider sx={{ mb: 3 }} />
          
          {/* MAIN CONTENT RENDERING */}
          {(() => {
            // If there are no sales at all and we're not loading
            if ((!sales || sales.length === 0) && !isLoadingDetails && !hasMoreSales) {
              return (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Typography variant="h6" color="text.secondary">
                    Nenhuma venda encontrada. Crie uma nova venda para começar.
                  </Typography>
                </Box>
              );
            }
            
            // Show loading placeholder if we're in initial loading
            if ((!sales || sales.length === 0) && isLoadingDetails) {
              return (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <CircularProgress size={32} />
                  <Typography variant="body1" color="text.secondary" sx={{ ml: 2 }}>
                    Carregando vendas...
                  </Typography>
                </Box>
              );
            }
            
            // If searching/filtering but found nothing
            if ((searchTerm.trim() || showOnlyPendingSales) && filteredSales.length === 0) {
              return (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Typography variant="h6" color="text.secondary">
                    {showOnlyPendingSales 
                      ? "Nenhuma venda pendente encontrada."
                      : `Nenhuma venda corresponde à sua busca por "${searchTerm}". Tente um termo diferente.`}
                  </Typography>
                </Box>
              );
            }
            
            // Show results using filteredSales state
            return (
              <>
                {(searchTerm.trim() || showOnlyPendingSales) && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      {showOnlyPendingSales && (
                        <>Mostrando {filteredSales.length} vendas pendentes</>
                      )}
                      {searchTerm.trim() && !showOnlyPendingSales && (
                        <>Encontradas {filteredSales.length} de {sales.length} vendas correspondentes a "{searchTerm}"</>
                      )}
                      {searchTerm.trim() && showOnlyPendingSales && (
                        <> correspondentes a "{searchTerm}"</>
                      )}
                    </Typography>
                  </Box>
                )}
                
                {/* Virtualized List */}
                <Box sx={{ height: 'calc(100vh - 250px)', width: '100%' }}>
                  <WindowScroller>
                    {({ height, isScrolling, onChildScroll, scrollTop }) => (
                      <AutoSizer disableHeight>
                        {({ width }) => {
                          const columnCount = getColumnCount(width);
                          const rowCount = getRowCount(filteredSales.length, columnCount);
                          
                          return (
                            <List
                              autoHeight
                              height={height || 500}
                              isScrolling={isScrolling}
                              onScroll={onChildScroll}
                              rowCount={rowCount}
                              rowHeight={cache.rowHeight}
                              scrollTop={scrollTop}
                              width={width}
                              overscanRowCount={3}
                              deferredMeasurementCache={cache}
                              style={{
                                outline: 'none',
                                paddingBottom: '20px',
                                overflowX: 'hidden'
                              }}
                              onRowsRendered={({ overscanStopIndex }) => {
                                // Load more when we reach near the end
                                if (hasMoreSales && 
                                    !isLoadingMore && 
                                    overscanStopIndex >= rowCount - 3) {
                                  setIsLoadingMore(true);
                                  setCurrentPage(prev => prev + 1);
                                }
                              }}
                              rowRenderer={({ index, key, style, parent }) => {
                                return (
                                  <CellMeasurer
                                    cache={cache}
                                    columnIndex={0}
                                    key={key}
                                    parent={parent}
                                    rowIndex={index}
                                  >
                                    {({ measure }) => (
                                      <div 
                                        style={{ 
                                          ...style, 
                                          padding: '8px',
                                          boxSizing: 'border-box',
                                          display: 'flex'
                                        }}
                                      >
                                        {/* Create a row with potentially multiple columns */}
                                        {Array.from({ length: columnCount }).map((_, colIndex) => {
                                          const sale = getItemAtIndex(filteredSales, index, colIndex, columnCount);
                                          if (!sale) return <div key={colIndex} style={{ flex: 1 }} />;
                                          
                                          return (
                                            <div 
                                              key={colIndex} 
                                              style={{ 
                                                flex: 1, 
                                                padding: '0 8px',
                                                boxSizing: 'border-box'
                                              }}
                                            >
                                              <SaleCard 
                                                sale={sale} 
                                                onPrint={handlePrintReceipt}
                                                onDelete={handleDeleteSale}
                                                onUpdateStatus={handleUpdateSaleStatus}
                                                onLoad={measure}
                                                thresholdDays={thresholdDays}
                                              />
                                            </div>
                                          );
                                        })}
                                      </div>
                                    )}
                                  </CellMeasurer>
                                );
                              }}
                            />
                          );
                        }}
                      </AutoSizer>
                    )}
                  </WindowScroller>
                </Box>
                
                {/* Loading indicator */}
                {isLoadingMore && (
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      justifyContent: 'center', 
                      py: 3
                    }}
                  >
                    <CircularProgress size={24} />
                  </Box>
                )}
                
                {/* "No more sales" message */}
                {!hasMoreSales && !isLoadingMore && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      {hasMoreSales 
                        ? "Arraste para baixo para carregar mais vendas" 
                        : "Não há mais vendas para carregar"}
                    </Typography>
                  </Box>
                )}
              </>
            );
          })()}
        </>
      ) : (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4">Nova Venda</Typography>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={handleCancelNewSale}
            >
              Cancelar
            </Button>
          </Box>

          {/* Display any errors */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}><CircularProgress /></Box>}>
            <SaleForm 
              onSubmit={handleSaveSale}
              onCancel={handleCancelNewSale}
              products={products}
              services={services}
              customers={customers}
              pets={pets}
              getCustomerPackages={getCustomerPackages}
              initialData={appointmentData ? {
                customer_id: appointmentData.customerId,
                payment_method: 'cash', // Default payment method
                items: appointmentData.serviceId ? [{
                  id: appointmentData.serviceId,
                  type: 'service',
                  name: appointmentData.serviceName,
                  price_per_unit: appointmentData.servicePrice,
                  quantity: 1,
                  petId: appointmentData.petId || null // Add the pet ID to the item
                }] : []
              } : undefined}
            />
          </Suspense>
        </Box>
      )}

      {/* Receipt Dialog - use lazy-loaded component with Suspense */}
      <Suspense fallback={
        selectedSaleForReceipt ? (
          <Dialog
            open={true}
            maxWidth="md"
            fullWidth
          >
            <DialogContent>
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            </DialogContent>
          </Dialog>
        ) : null
      }>
        {selectedSaleForReceipt && (
          <Dialog
            open={true}
            onClose={handleCloseReceipt}
            fullScreen={fullScreen}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>
              Recibo #{selectedSaleForReceipt?.id}
            </DialogTitle>
            <DialogContent>
              <Receipt 
                sale={selectedSaleForReceipt}
                storeName={storeInfo.name}
                storeAddress={storeInfo.address}
                storePhone={storeInfo.phone}
                storeEmail={storeInfo.email}
                storeWebsite={storeInfo.website}
                storeCnpj={storeInfo.cnpj}
                additionalInfo={storeInfo.additionalInfo}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseReceipt}>Fechar</Button>
              <Button 
                variant="contained" 
                color="primary" 
                startIcon={<PrintIcon />}
                onClick={() => {
                  window.print();
                }}
              >
                Imprimir
              </Button>
            </DialogActions>
          </Dialog>
        )}
      </Suspense>

      {/* Add the delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-sale-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle id="delete-sale-dialog-title" sx={{ pb: 1 }}>
          <Box display="flex" alignItems="center">
            <WarningIcon color="warning" sx={{ mr: 1 }} />
            Confirmar Exclusão de Venda
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pb: 2 }}>
          {saleToDelete !== null && hasSaleRestockableItems(saleToDelete) ? (
            <>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Como você gostaria de lidar com o estoque para esta venda?
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 2, 
                    borderColor: theme.palette.error.light,
                    '&:hover': { borderColor: theme.palette.error.main }
                  }}
                >
                  <Box display="flex" flexDirection="row" alignItems="flex-start">
                    <DeleteIcon color="error" sx={{ mr: 2, mt: 0.5 }} />
                    <Box>
                      <Typography variant="subtitle1" fontWeight="bold" color="error.main">
                        Apenas Excluir Venda
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        A venda será excluída, mas os itens NÃO serão devolvidos ao estoque.
                        Use esta opção se os produtos já foram entregues ou consumidos.
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
                
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 2, 
                    borderColor: theme.palette.primary.light,
                    '&:hover': { borderColor: theme.palette.primary.main }
                  }}
                >
                  <Box display="flex" flexDirection="row" alignItems="flex-start">
                    <InventoryIcon color="primary" sx={{ mr: 2, mt: 0.5 }} />
                    <Box>
                      <Typography variant="subtitle1" fontWeight="bold" color="primary.main">
                        Excluir e Restaurar Estoque
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        A venda será excluída E todas as quantidades de produtos serão devolvidas ao estoque.
                        Use esta opção se os produtos estão sendo devolvidos ao estoque.
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              </Box>
            </>
          ) : (
            <Typography variant="body1">
              Tem certeza que deseja excluir esta venda? Esta ação não pode ser revertida.
            </Typography>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button 
            onClick={handleCloseDeleteDialog} 
            color="inherit"
            startIcon={<CloseIcon />}
          >
            Cancelar
          </Button>
          
          {saleToDelete !== null && hasSaleRestockableItems(saleToDelete) ? (
            <>
              <Button 
                onClick={() => performDeleteSale(false)} 
                color="error"
                variant="outlined"
                startIcon={<DeleteIcon />}
              >
                Apenas Excluir
              </Button>
              <Button 
                onClick={() => performDeleteSale(true)} 
                color="primary"
                variant="contained"
                startIcon={<InventoryIcon />}
              >
                Excluir e Restaurar Estoque
              </Button>
            </>
          ) : (
            <Button 
              onClick={() => performDeleteSale(false)} 
              color="error"
              variant="contained"
              startIcon={<DeleteIcon />}
            >
              Excluir Venda
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Sales; 