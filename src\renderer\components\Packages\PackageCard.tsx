import React, { useMemo } from 'react';
import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import IconButton from '@mui/material/IconButton';
import { useTheme } from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  CalendarToday as CalendarTodayIcon,
  FormatListNumbered as FormatListNumberedIcon
} from '@mui/icons-material';
import { Package } from '../../types/packages';

interface PackageCardProps {
  packageItem: Package;
  servicesMap: Map<number, { name: string; price: number }>;
  onEdit: (pkg: Package) => void;
  onDelete: (id: number) => void;
}

const PackageCard: React.FC<PackageCardProps> = React.memo(({
  packageItem,
  servicesMap,
  onEdit,
  onDelete
}) => {
  const theme = useTheme();

  const handleEdit = () => onEdit(packageItem);
  const handleDelete = () => onDelete(packageItem.id);

  return (
    <Card sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      transition: 'all 0.2s',
      '&:hover': { 
        boxShadow: 4,
        transform: 'translateY(-4px)'
      }
    }}>
      <Box sx={{ 
        p: 1, 
        bgcolor: theme.palette.primary.main,
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          {packageItem.name}
        </Typography>
        {!packageItem.is_active && (
          <Chip label="Inativo" size="small" sx={{ bgcolor: 'grey.300', color: 'grey.800', fontWeight: 'bold' }} />
        )}
      </Box>
      <CardContent sx={{ flexGrow: 1 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold', mr: 1 }}>
                Serviço:
              </Typography>
              <Typography variant="body1">{servicesMap.get(packageItem.service_id)?.name || 'Serviço'}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <FormatListNumberedIcon sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Ocorrências:
              </Typography>
            </Box>
            <Typography variant="body1">
              {packageItem.total_occurrences}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <CalendarTodayIcon sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Frequência:
              </Typography>
            </Box>
            <Typography variant="body1">
              {packageItem.frequency_type === 'weekly' && `Semanal (cada ${packageItem.frequency_interval} semana${packageItem.frequency_interval > 1 ? 's' : ''})`}
              {packageItem.frequency_type === 'monthly' && `Mensal (cada ${packageItem.frequency_interval} mês${packageItem.frequency_interval > 1 ? 'es' : ''})`}
              {packageItem.frequency_type === 'custom_days' && `A cada ${packageItem.frequency_interval} dia${packageItem.frequency_interval > 1 ? 's' : ''}`}
            </Typography>
          </Grid>
        </Grid>
        {packageItem.description && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {packageItem.description}
            </Typography>
          </Box>
        )}
        <Divider sx={{ my: 2 }} />
        <Box sx={{ 
          mt: 2, 
          p: 1.5, 
          bgcolor: 'background.default', 
          borderRadius: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Preço:
            </Typography>
            <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>
              R$ {packageItem.price.toFixed(2)}
            </Typography>
          </Box>
        </Box>
      </CardContent>
      <CardActions sx={{ justifyContent: 'flex-end', p: 1, bgcolor: 'background.default' }}>
        <IconButton 
          size="small"
          onClick={handleEdit}
          color="primary"
        >
          <EditIcon />
        </IconButton>
        <IconButton 
          size="small"
          onClick={handleDelete}
          color="error"
        >
          <DeleteIcon />
        </IconButton>
      </CardActions>
    </Card>
  );
});

export default PackageCard; 