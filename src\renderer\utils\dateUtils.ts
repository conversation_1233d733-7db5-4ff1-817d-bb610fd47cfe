/**
 * Date and timezone utility functions for consistent date handling across the app
 */

// Define constants for Brazilian timezone
export const BRAZIL_TIMEZONE = 'America/Sao_Paulo';
export const BRAZIL_LOCALE = 'pt-BR';

// Cache for memoized timezone conversion
// This reduces repeated expensive timezone calculations
const dateConversionCache = new Map<string, Date>();
const CACHE_SIZE_LIMIT = 100; // Limit cache size to prevent memory leaks

/**
 * Convert a date to Brazilian timezone with caching for better performance
 */
export const toBrazilianTimezone = (date: Date | string): Date => {
  try {
    // Create a new date object
    const inputDate = typeof date === 'string' ? new Date(date) : new Date(date);
    
    // Create a cache key based on timestamp
    const cacheKey = inputDate.getTime().toString();
    
    // Check if we already have this conversion in cache
    if (dateConversionCache.has(cacheKey)) {
      return dateConversionCache.get(cacheKey)!;
    }
    
    // Format a date string in Brazil timezone
    const dateStrInBrazilTz = inputDate.toLocaleString('en-US', { 
      timeZone: BRAZIL_TIMEZONE,
      hour12: false
    });
    
    // Convert back to Date object
    const brazilDateObj = new Date(dateStrInBrazilTz);
    
    // Manage cache size to prevent memory leaks
    if (dateConversionCache.size >= CACHE_SIZE_LIMIT) {
      // Remove oldest entry (first key)
      const keysIterator = dateConversionCache.keys();
      const firstItem = keysIterator.next();
      
      if (firstItem && !firstItem.done && firstItem.value) {
        dateConversionCache.delete(firstItem.value);
      }
    }
    
    // Store in cache
    dateConversionCache.set(cacheKey, brazilDateObj);
    
    return brazilDateObj;
  } catch (error) {
    console.error("Error converting to Brazilian timezone:", error);
    return new Date();
  }
};

/**
 * Format a date for display in Brazilian format
 */
export const formatBrazilianDate = (date: Date | string, includeTime = true): string => {
  try {
    const inputDate = typeof date === 'string' ? new Date(date) : date;
    
    // Format date part (dd/MM/yyyy)
    const day = String(inputDate.getDate()).padStart(2, '0');
    const month = String(inputDate.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
    const year = inputDate.getFullYear();
    
    let result = `${day}/${month}/${year}`;
    
    // Add time part if requested (HH:mm)
    if (includeTime) {
      const hours = String(inputDate.getHours()).padStart(2, '0');
      const minutes = String(inputDate.getMinutes()).padStart(2, '0');
      result += ` ${hours}:${minutes}`; // Space without comma
    }
    
    return result;
  } catch (error) {
    console.error("Error formatting date for Brazilian display:", error);
    return typeof date === 'string' ? date : date.toLocaleString();
  }
};

/**
 * Format a date as an ISO string in Brazil timezone
 */
export const toBrazilianISOString = (date: Date | string): string => {
  try {
    const brazilDate = toBrazilianTimezone(date);
    return brazilDate.toISOString();
  } catch (error) {
    console.error("Error converting to Brazilian ISO string:", error);
    return new Date().toISOString();
  }
};

// Cache for the current time in Brazil - we use this to avoid numerous expensive calculations
// when the same "now" value is needed multiple times in quick succession
let cachedNow: { date: Date; timestamp: number } | null = null;
const NOW_CACHE_TTL = 1000; // 1 second TTL for "now" cache

/**
 * Get the current date-time in Brazil timezone
 * Uses a short-lived cache to avoid repeated expensive timezone calculations
 */
export const getBrazilianNow = (): Date => {
  try {
    const currentTimestamp = Date.now();
    
    // Return cached value if it's fresh enough (less than 1 second old)
    if (cachedNow && (currentTimestamp - cachedNow.timestamp < NOW_CACHE_TTL)) {
      return new Date(cachedNow.date);
    }
    
    // Get current time and convert to Brazil timezone
    const now = new Date();
    const brazilNow = toBrazilianTimezone(now);
    
    // Update the cache
    cachedNow = {
      date: brazilNow,
      timestamp: currentTimestamp
    };
    
    return new Date(brazilNow);
  } catch (error) {
    console.error("Error getting current Brazilian time:", error);
    return new Date();
  }
};

/**
 * Parse a date string from a form input into a Date object
 */
export const parseFormDate = (dateString: string): Date => {
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      console.error(`Invalid date string format: ${dateString}`);
      return new Date();
    }
    
    return date;
  } catch (error) {
    console.error("Error parsing form date:", error, dateString);
    return new Date();
  }
};

/**
 * Format a date for form inputs (YYYY-MM-DDThh:mm)
 */
export const formatForFormInput = (date: Date): string => {
  try {
    // Ensure we're working with a copy
    const localDate = new Date(date);
    
    // Format as YYYY-MM-DDThh:mm
    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');
    
    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
    
    return formattedDate;
  } catch (error) {
    console.error("Error formatting date for form input:", error);
    return new Date().toISOString().slice(0, 16);
  }
};

/**
 * Parse a date string from Brazilian format (dd/MM/yyyy hh:mm) to a Date object
 */
export const parseBrazilianDateString = (dateString: string): Date => {
  try {
    // Normalize the date by removing any commas
    const normalizedDateString = dateString.replace(',', '');
    
    // Validate the format dd/MM/yyyy hh:mm
    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})\s+(\d{2}):(\d{2})$/;
    if (!dateRegex.test(normalizedDateString)) {
      console.error(`Invalid Brazilian date format: ${normalizedDateString}`);
      return new Date();
    }
    
    // Extract date parts
    const match = dateRegex.exec(normalizedDateString);
    if (!match || match.length < 6) {
      console.error(`Invalid match for date string: ${normalizedDateString}`);
      return new Date();
    }
    
    // Safe access to RegExp match groups
    const day = match[1];
    const month = match[2];
    const year = match[3];
    const hours = match[4];
    const minutes = match[5];
    
    // Create date object (month is 0-indexed in JavaScript)
    const date = new Date(
      parseInt(year), 
      parseInt(month) - 1, 
      parseInt(day), 
      parseInt(hours), 
      parseInt(minutes)
    );
    
    if (isNaN(date.getTime())) {
      console.error(`Invalid date value from string: ${normalizedDateString}`);
      return new Date();
    }
    
    return date;
  } catch (error) {
    console.error("Error parsing Brazilian date string:", error, dateString);
    return new Date();
  }
}; 