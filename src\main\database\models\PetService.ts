import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>inColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Pet } from './Pet';

@Entity('pet_services')
export class PetService {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  pet_id!: number;

  @ManyToOne(() => Pet, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'pet_id' })
  pet!: Pet;

  @Column()
  service_name!: string;

  @Column()
  service_date!: Date;

  @Column({ nullable: true })
  notes!: string;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
} 