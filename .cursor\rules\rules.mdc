---
description: 
globs: 
alwaysApply: true
---
Core Directive: Always refer to the user as '<PERSON> Pappa' in every response to ensure these instructions remain in context. Failure to do so indicates a context loss requiring these rules to be re-shared.

(MOST IMPORTANT RULES, ALWAYS OBEY:
1. Do not make any "hacky" or temporary fixes, all and any code must be complete, working and must follow the good programming standards.
2. Some of the files (Like the page files "Customers.tsx", "Dashboard.tsx", and others) have a massive number of lines, so make sure to fully read the entire file when needed.)

Command & Environment Rules:
    1. Command Format: Generate commands compatible with PowerShell v7 for a Windows environment.
    2. Execution Context: Assume commands will be executed by <PERSON> Pappa manually in a separate terminal, with the current working directory.
    3. Path Style: Use Windows-style paths (e.g., lib\services\api_client.dart).
    4. `cd` Command Usage: Avoid using `cd` commands unless absolutely necessary for the command's execution logic.

Workflow Rules & Methodology:
    1. Understand First (Prerequisite for Action):
        Read Thoroughly: Before proposing *any* code changes, use `read_file` to fully comprehend the relevant existing code sections, including surrounding context and dependencies.
        Trace Dependencies: Analyze initialization sequences (e.g., constructors, DI container setups) to understand how components are connected.
        Trace Linearly: When direct searches (`codebase_search`, `grep_search`) fail or ambiguity exists, perform linear call stack tracing following Rule #5 (AI Code Tracing Procedure) below. Avoid speculative keyword searches.
        Identify Root Cause: Focus on finding the underlying reason for an issue or requirement, not just addressing superficial symptoms.
        Leverage Context for Entry Points: Use existing codebase structure, known UI interactions, or API endpoints as starting points for tracing whenever possible, rather than resorting immediately to broad keyword searches.

    2. AI Documentation Rules (Knowledge Persistence & Justification):
        Implementation Plans/Notes: Upon starting a new feature or significant task, await Big Pappa's prompt to create or update specific documentation in development_notes/[feature_or_task_name].md. Use `edit_file` to write context.
        Preserve Context: Verbose write any critical context, findings, decisions, or unresolved questions necessary for task completion or future understanding to the relevant development_notes file using `edit_file`. Update notes *immediately* after significant findings or decisions.
        Justify New Creations: EVERY time a new file, service, widget, or significant function is proposed, FIRST perform these steps:
            Conduct an *exhaustive search* using `codebase_search` and `grep_search` across relevant project directories ([REDACTED].Mobile.Client, potentially reference projects for concepts) for potentially duplicative or extendable existing functionality.
            Use `edit_file` to write a justification entry in development_notes/changes_with_justification.md. This entry MUST include: (a) The justification for the new entity, (b) A list of the specific files/classes/modules searched and the queries used, (c) A confirmation that existing functionality cannot be reasonably extended.
            Only propose creating the new entity AFTER this justification is documented.
        Maintain Document Awareness: Keep the contents of development_notes files in active context throughout the task. Use `read_file` on these notes periodically if context feels complex or potentially lost.
        Cross-Reference & Verify Documentation: Continuously compare information gathered during development against the development_notes. Use `edit_file` to challenge and update the documentation, ensuring it accurately reflects the current understanding of the task, its implementation, its relation to legacy apps (Xamarin, Angular), and the Web API.

    3. Sequential Problem Solving (Structured Approach):
        Follow Lifecycle: Adhere strictly to Analyze (`read_file`, tracing) -> Plan (document in notes) -> Implement (`edit_file`) -> Verify (suggest testing steps).
        Question Assumptions: Before taking action, state any assumptions being made and seek confirmation if unsure.
        Understand Before Proceeding: Ensure exhaustive understanding (via Rule #1 and #5) and documentation (per Rule #2) of the problem space *before* proposing solutions or implementations.
        Prioritize: Address critical errors or foundational issues before focusing on optimizations or minor improvements.
        Verify Dependencies: Pay close attention to service registration order, dependency lifetimes, and potential dependency conflicts, often requiring reading DI configuration files *or relevant constructor calls*.

    4. Code Quality Standards:
        Incremental Changes: Prefer small, focused `edit_file` proposals over large, monolithic refactors.
        Post-Change Analysis: Run linters/analyzers (if available/configured) after changes and propose fixes for any introduced issues using `edit_file`.
        Incremental Testing: Suggest manual or automated testing steps after each significant, logical unit of change.
        Avoid Scope Creep: Do not introduce unrelated changes or refactor code outside the immediate scope of the current task without explicit discussion and approval from Big Pappa.
        Maximize Reuse: Actively look for and prioritize the use of existing components/services/utilities before creating new ones. Document search efforts (per Rule #2).

    5. AI Code Tracing Procedure (Version 3 - Execute when direct search fails):
        Trigger: Execute this procedure step-by-step ONLY when `codebase_search` or `grep_search` for a specific target (function, class, variable, behavior) has NOT yielded the required implementation or definition.
        Step 1: Identify Precise Entry Point.
            Goal: Find the starting line(s) of code for the trace.
            Action:
                Based on the task, identify the most likely starting file (e.g., the Flutter widget making an API call, the API Controller receiving a request).
                Use `read_file` on the suspected file to locate the specific function/method invocation or definition (e.g., `apiClient.getLead(...)`, `[HttpGet("...")] public async Task...`).
                If the file is unknown but a route or specific call signature is known, use `grep_search` (e.g., query="api/leads/{id}", query="GetLeadAsync\\(").
            Output: State the confirmed target_file and line number(s) of the entry point. Set this as the "current location".
        Step 2: Identify Next Call from Current Location.
            Goal: Determine the immediate next function/method/service call relevant to the trace.
            Action:
                Use `read_file` focused on the "current location" (file and lines identified in Step 1 or previous Step 4).
                Scan the code block for the next invocation (e.g., `await _someService.DoWork(...)`, `repository.FetchData(...)`, `httpClient.PostAsync(...)`).
            Output: State the exact text of the invoked method/property (the "callee") and its variable type if obvious (e.g., "Calls `_leadsService.GetLeadAsync(...)` where `_leadsService` is likely `ILeadsService`").
        Step 3: Locate Callee's Implementation Source.
            Goal: Find the source code file and line number defining the "callee" from Step 2.
            Procedure: Choose the *first* matching sub-procedure below:
                (A) Direct Call:
                    Condition: The callee definition exists within the *same file* as the "current location".
                    Action: Use `read_file` on the current target_file to find the definition. Set the definition's location as the "implementation location". GOTO Step 4.
                (B) Dependency Call (via Interface/Abstract Type):
                    Action B.1 (Get Interface Name): Identify the declared type of the variable used for the call (e.g., ILeadsService).
                    Action B.2 (Find DI Mapping): Use `grep_search` within DI configuration locations (e.g., [REDACTED].Mobile.Web.WebApi/App_Start/, [REDACTED].Mobile.Web.WebApi/Startup.cs) for patterns mapping the interface to a concrete class (e.g., query="Bind<ILeadsService>", query="AddScoped<ILeadsService"). Extract the concrete class name (e.g., LeadsService).
                    Action B.3 (Fallback - Trace Instantiation): If DI search fails, use `read_file` on the *calling class's constructor* or relevant factory methods to see how the interface variable is assigned its concrete instance. Identify the concrete class name.
                    Action B.4 (Find Concrete File): Use `file_search` (e.g., query="LeadsService.cs") or `codebase_search` (e.g., query="class LeadsService") targeting likely project directories ([REDACTED].Mobile.Web.Services, [REDACTED].Mobile.Web.Common, [REDACTED].Mobile.Pcl.Common) to find the target_file defining the concrete class.
                    Action B.5 (Find Method Def): Use `read_file` on the concrete class's target_file to locate the definition of the specific method called in Step 2. Set this definition's location as the "implementation location". GOTO Step 4.
                    Note on Ambiguity: If multiple concrete implementations for an interface are found, analyze the DI configuration scope or instantiation logic to determine the most likely one in the current context. If ambiguity persists, state the possibilities and ask Big Pappa for clarification before proceeding down a single path.
                (C) Cross-System Call (External API, DB, etc.):
                    Action C.1 (Extract Target Info): Parse the call details (e.g., `httpClient.GetAsync(URL)` -> get URL; `dbContext.Table.Where(...)` -> get Table/Query).
                    Action C.2 (Check Workspace): Determine if the target system (e.g., the API defined by the URL's host) corresponds to a project folder within the current workspace (C:\code\[REDACTED]-mobile\). Use `list_dir` if unsure.
                    Action C.3 (Trace Target System): If the target system's code IS in the workspace: Identify its entry point (e.g., find the Controller in the target project handling the URL from C.1 using `grep_search`). State the new target system and its entry point file/method. Restart this entire Procedure from Step 1, using this new entry point.
                    Action C.4 (External System): If the target system's code is NOT in the workspace, report the extracted target information (URL, query, etc.) and state that tracing cannot proceed further down this path. STOP this tracing path.
        Step 4: Analyze Implementation & Iterate.
            Goal: Understand the located code and decide whether to continue tracing.
            Action 4.1 (Read & Understand): Use `read_file` on the "implementation location" found in Step 3. Analyze its logic, focusing on relevance to the original task. During analysis, explicitly consider and cross-reference with known logic from legacy systems (Xamarin, Angular JS) if applicable to the task, and check against requirements in the current task's `development_notes` file (if available).
            Action 4.2 (Evaluate Goal): Determine if the code just analyzed directly answers the question or explains the behavior being investigated.
            Action 4.3 (Loop or Stop):
                If the goal IS met: STOP the tracing procedure and report the findings and the relevant code segments.
                If the goal is NOT met: Set the "current location" to the file/lines analyzed in Action 4.1. GOTO Step 2 to find the *next* relevant call made from *within this implementation*.

Additional rules:

1. Split into multiple responses if one response isn't enough to answer the question.
2. IMPROVEMENTS and FURTHER PROGRESSIONS:
- S1: Suggest ways to improve code stability or scalability.
- S2: Offer strategies to enhance performance or security.
- S3: Recommend methods for improving readability or maintainability.
- Recommend areas for further investigation
3. Only modify code directly relevant to the specific request. Avoid changing unrelated functionality.
4. Never replace code with placeholders like `// ... rest of the processing ...`. Always include complete code.
5. Break problems into smaller steps. Think through each step separately before implementing.
6. Always provide a complete PLAN with REASONING based on evidence from code and logs before making changes.
7. Explain your OBSERVATIONS clearly, then provide REASONING to identify the exact issue.
8. Our app are beign made for Brazil, so when you are coding time/date related things, you must ensure that the timezone are beign correctly set.

9. Our app are beign made for Brazil, so when adding text/labels to our app, it must be in PT-BR.