import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import type { Sale } from './Sale';
import { Product } from './Product';
import { Service } from './Service';

@Entity('sale_items')
export class SaleItem {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne('Sale', 'items')
  sale!: Sale;

  @ManyToOne(() => Product, { nullable: true })
  product!: Product;

  @ManyToOne(() => Service, { nullable: true })
  service!: Service;

  @Column()
  quantity!: number;

  @Column('decimal', { precision: 10, scale: 2 })
  price_per_unit!: number;

  @Column('integer', { nullable: true })
  petId!: number;
  
  @Column('integer', { nullable: true })
  customer_package_id!: number | null;
  
  @Column('boolean', { default: false })
  is_package_service!: boolean;
} 