import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Minimal preload script for splash screen
contextBridge.exposeInMainWorld(
  'electron',
  {
    // Method to send message to main process
    sendToMain: (channel: string, ...args: unknown[]) => {
      // Only allow 'splash-ready' channel for security
      if (channel === 'splash-ready') {
        ipcRenderer.send(channel, ...args);
      }
    }
  }
); 