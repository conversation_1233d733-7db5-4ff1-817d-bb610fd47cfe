// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts

import { contextBridge, ipc<PERSON>enderer } from 'electron';

contextBridge.exposeInMainWorld('electron', {
  // Add any functions you want to expose to the renderer process here
  store: {
    async get(key: string) {
      return await ipcRenderer.invoke('electron-store-get', key);
    },
    async set(key: string, val: any) {
      return await ipcRenderer.invoke('electron-store-set', key, val);
    }
  }
}); 