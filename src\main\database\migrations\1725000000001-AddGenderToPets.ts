import { MigrationInterface, QueryRunner } from "typeorm";

export class AddGenderToPets1725000000001 implements MigrationInterface {
    name = 'AddGenderToPets1725000000001';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add gender column to pets table
        await queryRunner.query(`ALTER TABLE "pets" ADD COLUMN "gender" TEXT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove gender column if migration needs to be reverted
        await queryRunner.query(`ALTER TABLE "pets" DROP COLUMN "gender"`);
    }
} 