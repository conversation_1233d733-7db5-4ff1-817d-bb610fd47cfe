import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Grid from '@mui/material/Grid';
import Divider from '@mui/material/Divider';
import Chip from '@mui/material/Chip';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import FormHelperText from '@mui/material/FormHelperText';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import useTheme from '@mui/material/styles/useTheme';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import IconButton from '@mui/material/IconButton';
import {
  CardGiftcard as CardGiftcardIcon,
  ShoppingCart as ShoppingCartIcon,
  CheckCircle as CheckCircleIcon,
  Receipt as ReceiptIcon,
  CalendarToday as CalendarTodayIcon,
  LocalOffer as LocalOfferIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { Package } from '../../types/packages';
import { Service } from '../../types/sales';
import { PaymentMethod, paymentMethodLabels } from '../../types/sales';
import PackageCardItem from './PackageCardItem';
import TextField from '@mui/material/TextField';
import {
  LocalizationProvider,
  DatePicker,
  TimePicker,
} from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { ptBR } from 'date-fns/locale/pt-BR';
import { format } from 'date-fns';

declare interface BuyPackageDialogProps {
  open: boolean;
  onClose: () => void;
  onPurchase: (
    packageId: number,
    petId: number,
    paymentMethod: string,
    firstAppointmentDate: string,
    firstAppointmentTime: string,
    expiryDate?: string,
    finalPrice?: number
  ) => void;
  packages: Package[];
  services: Service[];
  customerName: string;
  loading: boolean;
  error: string | null;
  pets: { id: number; name: string }[];
}

export const BuyPackageDialog: React.FC<BuyPackageDialogProps> = ({
  open,
  onClose,
  onPurchase,
  packages,
  services,
  customerName,
  loading,
  error,
  pets
}) => {
  const [selectedPackageId, setSelectedPackageId] = useState<number | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [errors, setErrors] = useState<{ package?: string; payment?: string }>({});
  const [activeStep, setActiveStep] = useState(0);
  const [selectedPetId, setSelectedPetId] = useState<number | null>(null);
  const [firstAppointmentDate, setFirstAppointmentDate] = useState<Date | null>(null);
  const [firstAppointmentTime, setFirstAppointmentTime] = useState<Date | null>(null);
  const [firstAppointmentError, setFirstAppointmentError] = useState<string | null>(null);
  const [expiryType, setExpiryType] = useState<'none' | 'preset' | 'custom'>('none');
  const [expiryPreset, setExpiryPreset] = useState<string>('');
  const [customExpiryDate, setCustomExpiryDate] = useState<string>('');
  const [expiryError, setExpiryError] = useState<string | null>(null);
  const [petError, setPetError] = useState<string | undefined>(undefined);
  const [finalPrice, setFinalPrice] = useState<number>(0);
  const [priceError, setPriceError] = useState<string | null>(null);

  const steps = ['Seleção de Pacote', 'Seleção de Pet', 'Primeiro Agendamento', 'Validade do Pacote', 'Forma de Pagamento', 'Resumo da Compra'];

  useEffect(() => {
    if (open) {
      setSelectedPackageId(null);
      setPaymentMethod('cash');
      setErrors({});
      setActiveStep(0);
      setSelectedPetId(null);
      setFirstAppointmentDate(null);
      setFirstAppointmentTime(null);
      setFirstAppointmentError(null);
      setExpiryType('none');
      setExpiryPreset('');
      setCustomExpiryDate('');
      setExpiryError(null);
      setPetError(undefined);
      setFinalPrice(0);
      setPriceError(null);
    }
  }, [open]);

  // Update final price when package is selected
  useEffect(() => {
    if (selectedPackageId) {
      const selectedPackage = packages.find(p => p.id === selectedPackageId);
      if (selectedPackage) {
        setFinalPrice(selectedPackage.price);
      }
    }
  }, [selectedPackageId, packages]);

  const handlePaymentMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPaymentMethod(event.target.value as PaymentMethod);
    if (errors.payment) {
      setErrors(prev => ({ ...prev, payment: undefined }));
    }
  };

  const handleSelectPackage = (packageId: number) => {
    setSelectedPackageId(packageId);
    if (errors.package) {
      setErrors(prev => ({ ...prev, package: undefined }));
    }
  };

  const handleSelectPet = (petId: number) => {
    setSelectedPetId(petId);
    if (errors.payment) {
      setErrors(prev => ({ ...prev, payment: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { package?: string; payment?: string } = {};

    if (!selectedPackageId) {
      newErrors.package = 'Selecione um pacote';
    }

    if (!paymentMethod) {
      newErrors.payment = 'Selecione um método de pagamento';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePurchase = () => {
    if (
      selectedPackageId &&
      selectedPetId &&
      firstAppointmentDate &&
      firstAppointmentTime &&
      paymentMethod
    ) {
      onPurchase(
        selectedPackageId,
        selectedPetId,
        paymentMethod,
        format(firstAppointmentDate, 'yyyy-MM-dd'),
        format(firstAppointmentTime, 'HH:mm'),
        getExpiryDate(),
        finalPrice
      );
    }
  };

  const handleNext = () => {
    if (activeStep === 0 && !selectedPackageId) {
      setErrors({ ...errors, package: 'Selecione um pacote para continuar' });
      return;
    }
    if (activeStep === 1 && !selectedPetId) {
      setPetError('Selecione um pet para continuar.');
      return;
    }
    if (activeStep === 2) {
      if (!firstAppointmentDate || !firstAppointmentTime) {
        setFirstAppointmentError('Selecione a data e a hora do primeiro serviço');
        return;
      }
      setFirstAppointmentError(null);
    }
    if (activeStep === 3) {
      if (expiryType === 'custom' && !customExpiryDate) {
        setExpiryError('Selecione a data de expiração personalizada');
        return;
      }
      setExpiryError(null);
    }
    if (activeStep < steps.length - 1) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Get the selected package
  const selectedPackage = packages.find(p => p.id === selectedPackageId);

  // Helper to get frequency label
  const getFrequencyLabel = (pkg: Package) => {
    if (pkg.frequency_type === 'weekly') return `Semanal (cada ${pkg.frequency_interval} semana${pkg.frequency_interval > 1 ? 's' : ''})`;
    if (pkg.frequency_type === 'monthly') return `Mensal (cada ${pkg.frequency_interval} mês${pkg.frequency_interval > 1 ? 'es' : ''})`;
    if (pkg.frequency_type === 'custom_days') return `A cada ${pkg.frequency_interval} dia${pkg.frequency_interval > 1 ? 's' : ''}`;
    return '';
  };

  // Render package selection step
  const renderPackageSelectionStep = () => (
    <>
      <Typography variant="subtitle1" sx={{ mb: 2 }}>
        Selecione um Pacote:
      </Typography>
      {errors.package && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.package}
        </Alert>
      )}
      <Grid container spacing={2}>
        {packages.filter(pkg => pkg.is_active).map((pkg) => {
          const isSelected = selectedPackageId === pkg.id;
          return (
            <Grid item xs={12} md={6} key={pkg.id}>
              <Card
                variant={isSelected ? 'outlined' : undefined}
                sx={{
                  borderColor: isSelected ? 'primary.main' : undefined,
                  boxShadow: isSelected ? 4 : 1,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': { boxShadow: 6 }
                }}
                onClick={() => handleSelectPackage(pkg.id)}
              >
                <CardContent>
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    {pkg.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {pkg.description}
                  </Typography>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="body2"><b>Frequência:</b> {getFrequencyLabel(pkg)}</Typography>
                  <Typography variant="body2"><b>Preço:</b> R$ {pkg.price.toFixed(2)}</Typography>
                  {!pkg.is_active && (
                    <Chip label="Inativo" size="small" sx={{ bgcolor: 'grey.300', color: 'grey.800', fontWeight: 'bold', mt: 1 }} />
                  )}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </>
  );

  // Render pet selection step
  const renderPetSelectionStep = () => (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handleBack} size="small" sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h6">Seleção de Pet</Typography>
      </Box>

      {errors.payment && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.payment}
        </Alert>
      )}

      {petError && (
        <Alert severity="error" sx={{ mt: 2 }}>{petError}</Alert>
      )}

      <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
        <FormControl component="fieldset">
          <InputLabel id="pet-select-label">Selecione um Pet</InputLabel>
          <Select
            labelId="pet-select-label"
            id="pet-select"
            value={selectedPetId || ''}
            label="Selecione um Pet"
            onChange={(e) => handleSelectPet(Number(e.target.value))}
            sx={{ minWidth: 220 }}
          >
            <MenuItem value="">
              <em>Selecione um Pet</em>
            </MenuItem>
            {pets.map((pet) => (
              <MenuItem key={pet.id} value={pet.id}>
                {pet.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </Box>
  );

  // Render first appointment step
  const renderFirstAppointmentStep = () => (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handleBack} size="small" sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h6">Primeiro Agendamento</Typography>
      </Box>
      <Typography variant="subtitle1" sx={{ mb: 2 }}>
        Data e Hora do Primeiro Serviço:
      </Typography>
      {firstAppointmentError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {firstAppointmentError}
        </Alert>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Data do Primeiro Serviço"
              value={firstAppointmentDate}
              onChange={(newValue) => {
                setFirstAppointmentDate(newValue);
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!firstAppointmentError && !firstAppointmentDate,
                  helperText: !!firstAppointmentError && !firstAppointmentDate ? 'Data é obrigatória' : '',
                }
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TimePicker
              label="Hora do Primeiro Serviço"
              value={firstAppointmentTime}
              onChange={(newValue) => {
                setFirstAppointmentTime(newValue);
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!firstAppointmentError && !firstAppointmentTime,
                  helperText: !!firstAppointmentError && !firstAppointmentTime ? 'Hora é obrigatória' : '',
                }
              }}
              ampm={false} // For 24-hour format
            />
          </Grid>
        </Grid>
      </LocalizationProvider>
    </>
  );

  // Render expiry step
  const renderExpiryStep = () => (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handleBack} size="small" sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h6">Validade do Pacote</Typography>
      </Box>
      <Typography variant="subtitle1" sx={{ mb: 2 }}>
        Defina a validade do pacote para o cliente:
      </Typography>
      {expiryError && (
        <Alert severity="error" sx={{ mb: 2 }}>{expiryError}</Alert>
      )}
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel id="expiry-type-label">Validade</InputLabel>
            <Select
              labelId="expiry-type-label"
              value={expiryType === 'custom' ? 'custom' : expiryPreset || 'none'}
              label="Validade"
              onChange={e => {
                const value = e.target.value;
                if (value === 'custom') {
                  setExpiryType('custom');
                  setExpiryPreset('');
                } else if (value === 'none') {
                  setExpiryType('none');
                  setExpiryPreset('');
                  setCustomExpiryDate('');
                } else {
                  setExpiryType('preset');
                  setExpiryPreset(value);
                  setCustomExpiryDate('');
                }
              }}
            >
              {expiryOptions.map(opt => (
                <MenuItem key={opt.value} value={opt.value}>{opt.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        {expiryType === 'custom' && (
          <Grid item xs={12} md={6}>
            <TextField
              label="Data de Expiração Personalizada"
              type="date"
              value={customExpiryDate}
              onChange={e => setCustomExpiryDate(e.target.value)}
              InputLabelProps={{ shrink: true }}
              fullWidth
              required
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );

  // Render payment method step
  const renderPaymentMethodStep = () => (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handleBack} size="small" sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h6">Forma de Pagamento</Typography>
      </Box>

      {selectedPackage && (
        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="subtitle2" color="text.secondary">
            Pacote selecionado:
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CardGiftcardIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="subtitle1" fontWeight="bold">
              {selectedPackage.name} - R$ {selectedPackage.price.toFixed(2)}
            </Typography>
          </Box>
        </Box>
      )}

      <Typography variant="subtitle1" gutterBottom>
        Selecione a forma de pagamento:
      </Typography>

      {errors.payment && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.payment}
        </Alert>
      )}

      <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
        <FormControl component="fieldset">
          <RadioGroup
            row
            value={paymentMethod}
            onChange={handlePaymentMethodChange}
          >
            {Object.entries(paymentMethodLabels).map(([value, label]) => (
              <FormControlLabel
                key={value}
                value={value}
                control={<Radio />}
                label={label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>
    </Box>
  );

  // Render purchase summary step
  const renderPurchaseSummaryStep = () => {
    if (!selectedPackage) return null;
    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={handleBack} size="small" sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h6">Resumo da Compra</Typography>
        </Box>
        <Box
          sx={{
            p: 3,
            borderRadius: 1,
            border: '1px dashed',
            borderColor: 'primary.main',
            bgcolor: 'background.paper'
          }}
        >
          <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ReceiptIcon sx={{ mr: 1, fontSize: 20 }} />
            Detalhes da Compra
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Pacote:
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {selectedPackage.name}
              </Typography>
              <Box sx={{ mt: 1.5 }}>
                <Typography variant="body2" color="text.secondary">
                  Frequência: <b>{getFrequencyLabel(selectedPackage)}</b>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Cliente:
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {customerName}
              </Typography>
              <Box sx={{ mt: 1.5 }}>
                <Typography variant="body2" color="text.secondary">
                  Forma de Pagamento:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PaymentIcon sx={{ mr: 0.5, fontSize: 16, color: 'primary.main' }} />
                  <Typography variant="body1">
                    {paymentMethodLabels[paymentMethod]}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Preço Original: R$ {selectedPackage.price.toFixed(2)}
                  </Typography>
                  <TextField
                    label="Valor Final"
                    type="number"
                    value={finalPrice}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      if (!isNaN(value) && value >= 0) {
                        setFinalPrice(value);
                        setPriceError(null);
                      } else if (e.target.value === '') {
                        setFinalPrice(0);
                        setPriceError(null);
                      } else {
                        setPriceError('Valor deve ser um número positivo');
                      }
                    }}
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>R$</Typography>,
                    }}
                    size="small"
                    error={!!priceError}
                    helperText={priceError || 'Você pode ajustar o preço final (ex: aplicar desconto)'}
                    sx={{ minWidth: 200 }}
                  />
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" color="text.secondary">
                    Valor Total:
                  </Typography>
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    R$ {finalPrice.toFixed(2)}
                  </Typography>
                  {finalPrice !== selectedPackage.price && (
                    <Typography variant="caption" color="warning.main">
                      {finalPrice < selectedPackage.price ? 'Desconto aplicado' : 'Valor aumentado'}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
    );
  };

  // Render the active step content
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return renderPackageSelectionStep();
      case 1:
        return renderPetSelectionStep();
      case 2:
        return renderFirstAppointmentStep();
      case 3:
        return renderExpiryStep();
      case 4:
        return renderPaymentMethodStep();
      case 5:
        return renderPurchaseSummaryStep();
      default:
        return null;
    }
  };

  // Helper to get expiry date
  const getExpiryDate = () => {
    const now = new Date();
    if (expiryType === 'none') return undefined;
    if (expiryType === 'preset') {
      let expiry = new Date(now);
      switch (expiryPreset) {
        case '1m':
          expiry.setMonth(expiry.getMonth() + 1);
          break;
        case '3m':
          expiry.setMonth(expiry.getMonth() + 3);
          break;
        case '6m':
          expiry.setMonth(expiry.getMonth() + 6);
          break;
        case '1y':
          expiry.setFullYear(expiry.getFullYear() + 1);
          break;
        default:
          return undefined;
      }
      // Return as ISO string (yyyy-MM-dd)
      return expiry.toISOString().slice(0, 10);
    }
    if (expiryType === 'custom') return customExpiryDate || undefined;
    return undefined;
  };

  // Helper to get expiry options
  const expiryOptions = [
    { label: 'Sem validade', value: 'none' },
    { label: '1 mês', value: '1m' },
    { label: '3 meses', value: '3m' },
    { label: '6 meses', value: '6m' },
    { label: '1 ano', value: '1y' },
    { label: 'Data personalizada', value: 'custom' }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CardGiftcardIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            Comprar Pacote para {customerName}
          </Typography>
        </Box>
      </DialogTitle>

      <Divider />

      <Box sx={{ width: '100%', p: 2 }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>

      <DialogContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : packages.length === 0 ? (
          <Typography variant="subtitle1" sx={{ textAlign: 'center', py: 3 }}>
            Nenhum pacote disponível. Crie pacotes antes de continuar.
          </Typography>
        ) : (
          getStepContent(activeStep)
        )}
      </DialogContent>

      <DialogActions>
        <Button
          onClick={onClose}
          color="inherit"
        >
          Cancelar
        </Button>

        {activeStep < steps.length - 1 && (
          <Button
            variant="contained"
            color="primary"
            onClick={handleNext}
            disabled={activeStep === 0 && !selectedPackageId}
            endIcon={<ArrowForwardIcon />}
          >
            Avançar
          </Button>
        )}

        {activeStep === steps.length - 1 && (
          <Button
            onClick={handlePurchase}
            color="primary"
            variant="contained"
            startIcon={<ShoppingCartIcon />}
            disabled={!selectedPackageId || !selectedPetId || loading}
          >
            Finalizar Compra
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};