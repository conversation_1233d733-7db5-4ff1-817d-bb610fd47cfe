import React, { useState, useEffect } from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import Divider from '@mui/material/Divider';
import Collapse from '@mui/material/Collapse';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { alpha } from '@mui/material';
import { 
  Print as PrintIcon, 
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CalendarToday as CalendarIcon,
  Person as PersonIcon,
  Payment as PaymentIcon,
  Delete as DeleteIcon,
  PendingActions as PendingIcon,
  CheckCircle as PaidIcon,
  ListAlt as ListIcon,
  Warning as WarningIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { 
  Sale, 
  SaleItem, 
  paymentMethodLabels, 
  saleStatusLabels,
  formatCurrency,
  formatDate,
  SaleStatus
} from '../../types/sales';

// Helper functions moved outside the component
const getItemName = (item: SaleItem): string => {
  if (item.product && item.product.name) {
    return item.product.name;
  }
  
  if (item.service && item.service.name) {
    // Check if service is inactive - safely check if property exists 
    if (item.service.hasOwnProperty('is_active') && item.service.is_active === false) {
      return `${item.service.name} (Excluído)`;
    }
    return item.service.name;
  }
  
  if (item.productId || item.product_id) {
    return `Produto #${item.productId || item.product_id}`;
  }
  
  if (item.serviceId || item.service_id) {
    return `Serviço #${item.serviceId || item.service_id}`;
  }
  
  return 'Item Desconhecido';
};

const getItemType = (item: SaleItem): string => {
  // Check for package services first
  if (item.customer_package_id || item.is_package_service) {
    return 'Pacote';
  }
  
  // Check if the service is inactive - safely check if property exists
  if (item.service && item.service.hasOwnProperty('is_active') && item.service.is_active === false) {
    return 'Serviço Inativo';
  }
  
  if (item.product) return 'Produto';
  if (item.service) return 'Serviço';
  
  if (item.productId || item.product_id) return 'Produto';
  if (item.serviceId || item.service_id) return 'Serviço';
  
  return 'Desconhecido';
};

interface SaleCardProps {
  sale: Sale;
  onPrint: (saleId: number) => void;
  onDelete?: (saleId: number) => void;
  onUpdateStatus?: (saleId: number, status: SaleStatus) => void;
  onLoad?: () => void;
  thresholdDays?: number;
}

const SaleCardComponent: React.FC<SaleCardProps> = ({ 
  sale, 
  onPrint,
  onDelete,
  onUpdateStatus,
  onLoad,
  thresholdDays = 30
}) => {
  const [itemsDialogOpen, setItemsDialogOpen] = useState(false);
  
  // Calculate if the sale is overdue (more than threshold days old and still pending)
  const isOverdue = React.useMemo(() => {
    if (sale.status !== 'pending') return false;
    
    const saleDate = new Date(sale.sale_date);
    const now = new Date();
    const daysSinceSale = Math.floor((now.getTime() - saleDate.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysSinceSale >= thresholdDays;
  }, [sale.status, sale.sale_date, thresholdDays]);
  
  useEffect(() => {
    // console.log('SALECARD - Sale data:', {
    //   id: sale.id,
    //   status: sale.status,
    //   items: sale.items?.map(item => ({
    //    id: item.id,
    //    product_id: item.product_id,
    //    productId: item.productId,
    //    service_id: item.service_id,
    //    serviceId: item.serviceId,
    //    hasProduct: !!item.product,
    //    hasService: !!item.service,
    //    productName: item.product?.name,
    //    serviceName: item.service?.name
    //  }))
    // });
    
    if (onLoad) {
      onLoad();
    }
  }, [sale, onLoad]);
  
  const handleOpenItemsDialog = React.useCallback(() => {
    setItemsDialogOpen(true);
  }, []);

  const handleCloseItemsDialog = React.useCallback(() => {
    setItemsDialogOpen(false);
  }, []);

  const handleUpdateToPaid = React.useCallback(() => {
    if (onUpdateStatus && sale.status === 'pending') {
      onUpdateStatus(sale.id, 'paid' as SaleStatus);
    }
  }, [onUpdateStatus, sale.id, sale.status]);

  // Memoize the formatted date to avoid recreating it on every render
  const formattedDate = React.useMemo(() => {
    return formatDate(sale.sale_date.toString());
  }, [sale.sale_date]);

  return (
    <>
      <Card sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        ...(isOverdue && { 
          boxShadow: '0 0 8px rgba(255, 152, 0, 0.5)',
          border: '1px solid rgba(255, 152, 0, 0.4)'
        })
      }}>
        <Box sx={{ 
          display: 'flex', 
          p: 2, 
          alignItems: 'center', 
          bgcolor: isOverdue ? alpha('#ff9800', 0.1) : '#f5f5f5' 
        }}>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" component="div">
              Venda #{sale.id}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <CalendarIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {formattedDate}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>
              {formatCurrency(sale.total_amount)}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Chip 
                size="small" 
                label={saleStatusLabels[sale.status] || sale.status}
                icon={sale.status === 'paid' ? <PaidIcon /> : <PendingIcon />}
                sx={{ 
                  bgcolor: alpha(sale.status === 'paid' ? '#4caf50' : '#ff9800', 0.1),
                  color: sale.status === 'paid' ? '#2e7d32' : '#ed6c02',
                  border: `1px solid ${alpha(sale.status === 'paid' ? '#4caf50' : '#ff9800', 0.2)}`,
                  fontSize: '0.75rem',
                  height: 28,
                  fontWeight: 'medium',
                  cursor: 'default',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    bgcolor: alpha(sale.status === 'paid' ? '#4caf50' : '#ff9800', 0.2),
                    boxShadow: `0 2px 4px ${alpha(sale.status === 'paid' ? '#4caf50' : '#ff9800', 0.2)}`,
                    transform: 'translateY(-1px)'
                  },
                  '& .MuiChip-icon': {
                    color: sale.status === 'paid' ? '#2e7d32' : '#ed6c02',
                    marginLeft: '4px'
                  },
                  '& .MuiChip-label': {
                    paddingLeft: '6px'
                  }
                }} 
              />
              {isOverdue && (
                <Tooltip title={`Em atraso há mais de ${thresholdDays} dias`} placement="top">
                  <WarningIcon 
                    color="warning" 
                    sx={{ 
                      ml: 0.5, 
                      fontSize: 18, 
                      animation: 'pulse 2s infinite ease-in-out',
                      '@keyframes pulse': {
                        '0%': { opacity: 0.6 },
                        '50%': { opacity: 1 },
                        '100%': { opacity: 0.6 }
                      }
                    }} 
                  />
                </Tooltip>
              )}
            </Box>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column', ml: 2 }}>
            {sale.status === 'paid' ? (
              <Tooltip title="Imprimir recibo" placement="bottom" disableInteractive>
                <IconButton 
                  onClick={() => onPrint(sale.id)} 
                  aria-label="imprimir recibo"
                  size="small"
                  color="primary"
                  sx={{ mb: 0.5 }}
                >
                  <PrintIcon />
                </IconButton>
              </Tooltip>
            ) : onUpdateStatus && (
              <Tooltip title="Marcar como pago" placement="bottom" disableInteractive>
                <IconButton 
                  onClick={handleUpdateToPaid} 
                  aria-label="marcar como pago"
                  size="small"
                  color="success"
                  sx={{ mb: 0.5 }}
                >
                  <PaidIcon />
                </IconButton>
              </Tooltip>
            )}
            {onDelete && (
              <Tooltip title="Excluir venda" placement="bottom" disableInteractive>
                <IconButton 
                  onClick={() => onDelete(sale.id)} 
                  aria-label="excluir venda"
                  color="error"
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
        <Divider />
        <CardContent sx={{ pt: 2, pb: 1, flexGrow: 1 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2">
                Cliente: {sale.customer?.name || 'Cliente Avulso'}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PaymentIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2">
                Pagamento: {paymentMethodLabels[sale.payment_method]}
              </Typography>
            </Box>
            
            {isOverdue && (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                bgcolor: alpha('#ff9800', 0.1),
                border: '1px solid ' + alpha('#ff9800', 0.3),
                borderRadius: 1,
                p: 0.7,
                mt: 0.5
              }}>
                <WarningIcon fontSize="small" sx={{ mr: 1, color: '#ed6c02' }} />
                <Typography variant="body2" color="#ed6c02" fontWeight="medium">
                  Em atraso há mais de {thresholdDays} dias
                </Typography>
              </Box>
            )}
            
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                {(sale.items?.length || 0)} {(sale.items?.length || 0) === 1 ? 'item' : 'itens'}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ListIcon />}
                onClick={handleOpenItemsDialog}
                sx={{ 
                  borderRadius: 4,
                  fontSize: '0.75rem',
                  py: 0.5,
                  textTransform: 'none'
                }}
              >
                Ver Itens
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
      
      {/* Items Dialog */}
      <Dialog
        open={itemsDialogOpen}
        onClose={handleCloseItemsDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              Itens da Venda #{sale.id}
            </Typography>
            <IconButton onClick={handleCloseItemsDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell width="40%">Item</TableCell>
                <TableCell align="center" width="15%">Tipo</TableCell>
                <TableCell align="center" width="10%">Qtd</TableCell>
                <TableCell align="right" width="15%">Preço</TableCell>
                <TableCell align="right" width="20%">Total</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sale.items?.map((item) => {
                const isInactiveService = item.service && item.service.is_active === false;
                
                return (
                  <TableRow 
                    key={item.id} 
                    sx={{ 
                      ...(isInactiveService && { 
                        bgcolor: (theme) => alpha(theme.palette.error.light, 0.1) 
                      }) 
                    }}
                  >
                    <TableCell>
                      {getItemName(item)}
                    </TableCell>
                    <TableCell align="center">
                      <Chip 
                        label={getItemType(item)} 
                        size="small" 
                        color={
                          (item.customer_package_id || item.is_package_service) ? 'info' : 
                          isInactiveService ? 'error' :
                          (item.product_id ? 'primary' : 'secondary')
                        }
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="center">{item.quantity}</TableCell>
                    <TableCell align="right">
                      {(item.customer_package_id || item.is_package_service) ? (
                        "-"
                      ) : (
                        formatCurrency(item.price_per_unit)
                      )}
                    </TableCell>
                    <TableCell align="right">
                      {(item.customer_package_id || item.is_package_service) ? (
                        "-"
                      ) : (
                        formatCurrency(item.quantity * item.price_per_unit)
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
              <TableRow>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>
                  Total:
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {formatCurrency(sale.total_amount)}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Export the memoized version of the component
export const SaleCard = React.memo(SaleCardComponent); 