import React, { useState, useEffect, useMemo } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';
import FormHelperText from '@mui/material/FormHelperText';
import InputAdornment from '@mui/material/InputAdornment';
import Alert from '@mui/material/Alert';
import Collapse from '@mui/material/Collapse';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Tooltip from '@mui/material/Tooltip';
import CircularProgress from '@mui/material/CircularProgress';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton from '@mui/material/ToggleButton';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ShoppingCart as ShoppingCartIcon,
  Person as PersonIcon,
  Receipt as ReceiptIcon,
  CardGiftcard as CardGiftcardIcon,
  CheckCircle as CheckCircleIcon,
  Paid as PaidIcon,
  Pending as PendingIcon
} from '@mui/icons-material';
import { Customer } from '../../types/customers';
import { Product } from '../../types/inventory';
import { Service, SaleFormData, SaleItemFormData, PaymentMethod, paymentMethodLabels, SaleStatus, saleStatusLabels } from '../../types/sales';
import { Pet } from '../../types/pets';
import { CustomerPackage } from '../../types/packages';

interface NewSaleFormProps {
  customers: Customer[];
  products: Product[];
  services: Service[];
  onSubmit: (data: SaleFormData) => void;
  onCancel: () => void;
  getCustomerPets: (customerId: number) => Promise<Pet[]>;
  getCustomerPackages: (customerId: number) => Promise<CustomerPackage[]>;
  usePackageService: (customerPackageId: number) => Promise<boolean>;
}

interface FormItem extends SaleItemFormData {
  id: number;
  using_package?: boolean;
  customer_package_id?: number;
  item_id?: number;
}

export const NewSaleForm: React.FC<NewSaleFormProps> = ({
  customers,
  products,
  services,
  onSubmit,
  onCancel,
  getCustomerPets,
  getCustomerPackages,
  usePackageService
}) => {
  const [customerId, setCustomerId] = useState<number>(0);
  const [pets, setPets] = useState<Pet[]>([]);
  const [packagesList, setPackagesList] = useState<CustomerPackage[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [saleStatus, setSaleStatus] = useState<SaleStatus>('paid');
  const [items, setItems] = useState<FormItem[]>([]);
  const [nextItemId, setNextItemId] = useState(1);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loadingPets, setLoadingPets] = useState(false);
  const [loadingPackages, setLoadingPackages] = useState(false);

  // Load customer pets when customer changes
  useEffect(() => {
    const loadCustomerData = async () => {
      if (customerId) {
        // Load pets
        setLoadingPets(true);
        try {
          const customerPets = await getCustomerPets(customerId);
          setPets(customerPets);
        } catch (error) {
          console.error('Error loading customer pets:', error);
        } finally {
          setLoadingPets(false);
        }

        // Load packages
        setLoadingPackages(true);
        try {
          const customerPackages = await getCustomerPackages(customerId);
          setPackagesList(customerPackages.filter(pkg => pkg.remaining_occurrences > 0 && pkg.status === 'active'));
        } catch (error) {
          console.error('Error loading customer packages:', error);
        } finally {
          setLoadingPackages(false);
        }
      } else {
        setPets([]);
        setPackagesList([]);
      }
    };

    loadCustomerData();
  }, [customerId, getCustomerPets, getCustomerPackages]);

  const calculateTotal = useMemo(() => {
    return items.reduce((sum, item) => {
      // Only add to total if not using a package
      return sum + (item.using_package ? 0 : (item.price_per_unit * item.quantity));
    }, 0);
  }, [items]);

  const handleAddItem = () => {
    setItems([
      ...items,
      {
        id: nextItemId,
        type: 'product',
        quantity: 1,
        price_per_unit: 0,
        name: '',
        petId: null,
        using_package: false,
        customer_package_id: undefined
      }
    ]);
    setNextItemId(nextItemId + 1);
  };

  const handleItemChange = (itemId: number, field: string, value: any) => {
    const updatedItems = items.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        
        // If type is changed, reset various fields
        if (field === 'type') {
          updatedItem.price_per_unit = 0;
          updatedItem.name = '';
          updatedItem.using_package = false;
          updatedItem.customer_package_id = undefined;
          updatedItem.item_id = undefined;
        }
        
        // If product or service is selected, update price and name
        if (field === 'item_id') {
          if (updatedItem.type === 'product') {
            const product = products.find(p => p.id === value);
            if (product) {
              updatedItem.price_per_unit = product.price;
              updatedItem.name = product.name;
            }
          } else if (updatedItem.type === 'service') {
            const service = services.find(s => s.id === value);
            if (service) {
              updatedItem.price_per_unit = service.price;
              updatedItem.name = service.name;
            }
          }
          
          // Reset package selection when item changes
          updatedItem.using_package = false;
          updatedItem.customer_package_id = undefined;
        }
        
        // If using package is toggled, handle the update
        if (field === 'using_package') {
          if (value) {
            // Find appropriate package for this service
            const serviceId = updatedItem.item_id || 0;
            const matchingPackages = packagesList.filter(pkg => 
              pkg.package?.service_id === serviceId && 
              pkg.remaining_occurrences > 0
            );
            
            if (matchingPackages.length > 0) {
              // Use the first matching package (most recently purchased or with fewest remaining uses would be better)
              updatedItem.customer_package_id = matchingPackages[0].id;
            } else {
              // No matching package, reset the toggle
              return item;
            }
          } else {
            // Not using package anymore
            updatedItem.customer_package_id = undefined;
          }
        }
        
        // If package is selected directly, update the using_package flag
        if (field === 'customer_package_id') {
          updatedItem.using_package = true;
        }
        
        return updatedItem;
      }
      return item;
    });
    
    setItems(updatedItems);
    
    // Clear any error for this item
    if (errors[`item-${itemId}`]) {
      const newErrors = { ...errors };
      delete newErrors[`item-${itemId}`];
      setErrors(newErrors);
    }
  };

  const handleRemoveItem = (itemId: number) => {
    setItems(items.filter(item => item.id !== itemId));
    
    // Clear any error for this item
    if (errors[`item-${itemId}`]) {
      const newErrors = { ...errors };
      delete newErrors[`item-${itemId}`];
      setErrors(newErrors);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!customerId) {
      newErrors.customer = 'Selecione um cliente';
    }
    
    if (!paymentMethod) {
      newErrors.payment = 'Selecione um método de pagamento';
    }
    
    if (!saleStatus) {
      newErrors.status = 'Selecione o status de pagamento';
    }
    
    if (items.length === 0) {
      newErrors.items = 'Adicione pelo menos um item';
    } else {
      items.forEach(item => {
        if (item.type === 'product') {
          if (!item.item_id) {
            newErrors[`item-${item.id}`] = 'Selecione um produto';
          } else {
            const product = products.find(p => p.id === item.item_id);
            if (product && item.quantity > product.stock_quantity) {
              newErrors[`item-${item.id}`] = `Estoque insuficiente. Disponível: ${product.stock_quantity}`;
            }
          }
        } else if (item.type === 'service') {
          if (!item.item_id) {
            newErrors[`item-${item.id}`] = 'Selecione um serviço';
          }
          
          // If using package, validate package selection
          if (item.using_package && !item.customer_package_id) {
            newErrors[`item-${item.id}-package`] = 'Selecione um pacote para usar';
          }
          
          // If using package, check if pet is selected when required
          if (item.using_package && !item.petId) {
            newErrors[`item-${item.id}-pet`] = 'Selecione um pet para este serviço';
          }
        }
      });
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (validateForm()) {
      try {
        // Process items to check if any are using packages
        for (const item of items) {
          if (item.using_package && item.customer_package_id) {
            // Use the package service
            const success = await usePackageService(item.customer_package_id);
            if (!success) {
              setErrors({
                submit: 'Erro ao utilizar serviço do pacote'
              });
              return;
            }
          }
        }
        
        // Map form data to the expected format
        const formData: SaleFormData = {
          customer_id: customerId,
          payment_method: paymentMethod,
          status: saleStatus,
          items: items.map(item => ({
            type: item.type,
            quantity: item.quantity,
            price_per_unit: item.price_per_unit,
            name: item.name,
            id: item.item_id || 0,
            petId: item.petId,
            customer_package_id: item.customer_package_id,
            is_package_service: item.using_package
          }))
        };
        
        onSubmit(formData);
      } catch (error) {
        console.error('Error in form submission:', error);
        setErrors({
          submit: 'Erro ao processar venda'
        });
      }
    }
  };

  // Find matching packages for a given service
  const getMatchingPackages = (serviceId: number): CustomerPackage[] => {
    return packagesList.filter(pkg => 
      pkg.package?.service_id === serviceId && 
      pkg.remaining_occurrences > 0 &&
      pkg.status === 'active'
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Nova Venda
      </Typography>
      
      {errors.submit && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.submit}
        </Alert>
      )}
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!errors.customer}>
            <InputLabel id="customer-select-label">Cliente</InputLabel>
            <Select
              labelId="customer-select-label"
              value={customerId}
              label="Cliente"
              onChange={(e) => setCustomerId(Number(e.target.value))}
              startAdornment={
                <InputAdornment position="start">
                  <PersonIcon />
                </InputAdornment>
              }
            >
              <MenuItem value={0} disabled>Selecione um cliente</MenuItem>
              {customers.map((customer) => (
                <MenuItem key={customer.id} value={customer.id}>
                  {customer.name}
                </MenuItem>
              ))}
            </Select>
            {errors.customer && <FormHelperText>{errors.customer}</FormHelperText>}
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!errors.payment}>
            <InputLabel id="payment-method-label">Forma de Pagamento</InputLabel>
            <Select
              labelId="payment-method-label"
              value={paymentMethod}
              label="Forma de Pagamento"
              onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
              startAdornment={
                <InputAdornment position="start">
                  <ReceiptIcon />
                </InputAdornment>
              }
            >
              {Object.entries(paymentMethodLabels).map(([value, label]) => (
                <MenuItem key={value} value={value}>
                  {label}
                </MenuItem>
              ))}
            </Select>
            {errors.payment && <FormHelperText>{errors.payment}</FormHelperText>}
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <Box sx={{ mb: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              Status do Pagamento
            </Typography>
            <ToggleButtonGroup
              value={saleStatus}
              exclusive
              onChange={(e, newValue) => {
                if (newValue !== null) { // Prevent deselecting both options
                  setSaleStatus(newValue as SaleStatus);
                }
              }}
              fullWidth
              color={saleStatus === 'paid' ? 'success' : 'warning'}
              sx={{ mb: 1 }}
            >
              <ToggleButton 
                value="paid" 
                sx={{ 
                  py: 2,
                  borderWidth: 2,
                  '&.Mui-selected': { 
                    backgroundColor: '#e8f5e9', 
                    borderColor: 'success.main',
                    '&:hover': {
                      backgroundColor: '#c8e6c9'
                    }
                  } 
                }}
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <PaidIcon sx={{ fontSize: 36, mb: 1 }} />
                  <Typography variant="subtitle1" fontWeight="bold">Pagar Agora</Typography>
                  <Typography variant="caption" align="center">
                    Venda será marcada como PAGA
                  </Typography>
                </Box>
              </ToggleButton>
              <ToggleButton 
                value="pending" 
                sx={{ 
                  py: 2,
                  borderWidth: 2,
                  '&.Mui-selected': { 
                    backgroundColor: '#fff3e0', 
                    borderColor: 'warning.main',
                    '&:hover': {
                      backgroundColor: '#ffe0b2'
                    }
                  } 
                }}
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <PendingIcon sx={{ fontSize: 36, mb: 1 }} />
                  <Typography variant="subtitle1" fontWeight="bold">Pagar Depois</Typography>
                  <Typography variant="caption" align="center">
                    Venda será marcada como PENDENTE
                  </Typography>
                </Box>
              </ToggleButton>
            </ToggleButtonGroup>
            {errors.status && <FormHelperText error>{errors.status}</FormHelperText>}
          </Box>
        </Grid>
      </Grid>
      
      {/* Status explanation box with more details */}
      <Box sx={{ mt: 2, bgcolor: saleStatus === 'paid' ? '#e8f5e9' : '#fff3e0', borderRadius: 1, p: 2 }}>
        {saleStatus === 'paid' ? (
          <>
            <Typography variant="subtitle2" color="success.dark" gutterBottom fontWeight="bold">
              Venda com Pagamento Imediato
            </Typography>
            <Typography variant="body2">
              • O valor será contabilizado imediatamente nas receitas<br />
              • Um recibo será gerado automaticamente após a venda<br />
              • O status da venda será "Pago" na listagem
            </Typography>
          </>
        ) : (
          <>
            <Typography variant="subtitle2" color="warning.dark" gutterBottom fontWeight="bold">
              Venda com Pagamento Futuro
            </Typography>
            <Typography variant="body2">
              • O valor NÃO será contabilizado nas receitas até o pagamento<br />
              • Nenhum recibo será gerado até o pagamento<br />
              • Você poderá marcar como "Pago" posteriormente através da listagem de vendas
            </Typography>
          </>
        )}
      </Box>
      
      {/* Show active packages if available */}
      {customerId > 0 && packagesList.length > 0 && (
        <Box sx={{ mt: 3, mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            <CardGiftcardIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
            Pacotes Disponíveis
          </Typography>
          <Grid container spacing={2}>
            {packagesList.map(pkg => (
              <Grid item xs={12} sm={6} md={4} key={pkg.id}>
                <Card variant="outlined">
                  <CardContent sx={{ pb: 1 }}>
                    <Typography variant="subtitle1" color="primary" gutterBottom>
                      {pkg.package?.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Chip 
                        label={`${pkg.remaining_occurrences} serviço(s) restante(s)`} 
                        color="success" 
                        size="small"
                      />
                      <Typography variant="caption" color="text.secondary">
                        Válido até {pkg.expiry_date ? new Date(pkg.expiry_date).toLocaleDateString() : 'Sem validade'}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Inclui: {(() => {
                        const service = services.find(s => s.id === pkg.package?.service_id);
                        return service ? service.name : '';
                      })()}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
      
      <Box sx={{ mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">
            Itens da Venda
          </Typography>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleAddItem}
          >
            Adicionar Item
          </Button>
        </Box>
        
        {errors.items && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {errors.items}
          </Alert>
        )}
        
        {items.length === 0 ? (
          <Paper sx={{ p: 2, textAlign: 'center', mb: 2 }}>
            <Typography color="text.secondary">
              Nenhum item adicionado. Clique em "Adicionar Item" para começar.
            </Typography>
          </Paper>
        ) : (
          <Box>
            {items.map((item, index) => {
              // Find matching packages for this service if it's a service type
              const matchingPackages = 
                item.type === 'service' && item.item_id 
                  ? getMatchingPackages(item.item_id) 
                  : [];
              
              const canUsePackage = matchingPackages.length > 0;
              
              return (
                <Paper key={item.id} sx={{ p: 2, mb: 2, position: 'relative' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={3}>
                      <FormControl fullWidth error={!!errors[`item-${item.id}`]}>
                        <InputLabel>Tipo</InputLabel>
                        <Select
                          value={item.type}
                          label="Tipo"
                          onChange={(e) => handleItemChange(item.id, 'type', e.target.value)}
                        >
                          <MenuItem value="product">Produto</MenuItem>
                          <MenuItem value="service">Serviço</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} sm={5}>
                      <FormControl fullWidth error={!!errors[`item-${item.id}`]}>
                        <InputLabel>{item.type === 'product' ? 'Produto' : 'Serviço'}</InputLabel>
                        <Select
                          value={item.item_id || ''}
                          label={item.type === 'product' ? 'Produto' : 'Serviço'}
                          onChange={(e) => handleItemChange(item.id, 'item_id', Number(e.target.value))}
                        >
                          <MenuItem value="" disabled>Selecione um {item.type === 'product' ? 'produto' : 'serviço'}</MenuItem>
                          {item.type === 'product' 
                            ? products.map(product => (
                                <MenuItem 
                                  key={product.id} 
                                  value={product.id}
                                  disabled={product.stock_quantity <= 0}
                                >
                                  {product.name} - R$ {product.price.toFixed(2)} 
                                  {product.stock_quantity <= 0 && ' (Sem estoque)'}
                                </MenuItem>
                              ))
                            : services.map(service => (
                                <MenuItem key={service.id} value={service.id}>
                                  {service.name} - R$ {service.price.toFixed(2)}
                                </MenuItem>
                              ))
                          }
                        </Select>
                        {errors[`item-${item.id}`] && (
                          <FormHelperText>{errors[`item-${item.id}`]}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={6} sm={2}>
                      <TextField
                        label="Quantidade"
                        type="number"
                        fullWidth
                        value={item.quantity}
                        onChange={(e) => handleItemChange(item.id, 'quantity', Number(e.target.value))}
                        InputProps={{ 
                          inputProps: { min: 1 } 
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={6} sm={2}>
                      <TextField
                        label="Preço Unitário"
                        type="number"
                        fullWidth
                        value={item.price_per_unit}
                        onChange={(e) => handleItemChange(item.id, 'price_per_unit', Number(e.target.value))}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                          readOnly: item.using_package // Make price readonly if using package
                        }}
                        disabled={item.using_package}
                      />
                    </Grid>
                    
                    {/* Pet selection for services */}
                    {item.type === 'service' && (
                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <InputLabel>Pet</InputLabel>
                          <Select
                            value={item.petId || ''}
                            label="Pet"
                            onChange={(e) => handleItemChange(item.id, 'petId', e.target.value ? Number(e.target.value) : null)}
                            disabled={loadingPets}
                            error={!!errors[`item-${item.id}-pet`]}
                          >
                            <MenuItem value="">
                              {loadingPets ? 'Carregando pets...' : 'Selecione um pet (opcional)'}
                            </MenuItem>
                            {pets.map(pet => (
                              <MenuItem key={pet.id} value={pet.id}>
                                {pet.name} ({pet.type}, {pet.breed})
                              </MenuItem>
                            ))}
                          </Select>
                          {errors[`item-${item.id}-pet`] && (
                            <FormHelperText error>{errors[`item-${item.id}-pet`]}</FormHelperText>
                          )}
                        </FormControl>
                      </Grid>
                    )}
                    
                    {/* Package usage option for services */}
                    {item.type === 'service' && item.item_id && canUsePackage && (
                      <Grid item xs={12}>
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center',
                          bgcolor: 'action.hover',
                          p: 1,
                          borderRadius: 1
                        }}>
                          <CardGiftcardIcon color="secondary" sx={{ mr: 1 }} />
                          
                          <FormControl fullWidth component="fieldset">
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Button
                                variant={item.using_package ? "contained" : "outlined"}
                                color="secondary"
                                size="small"
                                startIcon={
                                  item.using_package 
                                    ? <CheckCircleIcon /> 
                                    : <CardGiftcardIcon />
                                }
                                onClick={() => handleItemChange(item.id, 'using_package', !item.using_package)}
                                sx={{ mr: 2 }}
                              >
                                {item.using_package ? 'Usando Pacote' : 'Usar Pacote'}
                              </Button>
                              
                              {item.using_package && (
                                <FormControl sx={{ minWidth: 200 }} error={!!errors[`item-${item.id}-package`]}>
                                  <InputLabel>Pacote</InputLabel>
                                  <Select
                                    value={item.customer_package_id || ''}
                                    label="Pacote"
                                    onChange={(e) => handleItemChange(item.id, 'customer_package_id', Number(e.target.value))}
                                    size="small"
                                  >
                                    <MenuItem value="" disabled>Selecione um pacote</MenuItem>
                                    {matchingPackages.map(pkg => (
                                      <MenuItem key={pkg.id} value={pkg.id}>
                                        {pkg.package?.name} ({pkg.remaining_occurrences} restantes)
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {errors[`item-${item.id}-package`] && (
                                    <FormHelperText>{errors[`item-${item.id}-package`]}</FormHelperText>
                                  )}
                                </FormControl>
                              )}
                            </Box>
                          </FormControl>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                  
                  <IconButton
                    sx={{ position: 'absolute', top: 8, right: 8 }}
                    onClick={() => handleRemoveItem(item.id)}
                    color="error"
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                  
                  {item.using_package && (
                    <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                      <Chip 
                        label="Sem custo (usando pacote)" 
                        color="success" 
                        size="small"
                        icon={<CardGiftcardIcon />}
                      />
                    </Box>
                  )}
                </Paper>
              );
            })}
          </Box>
        )}
      </Box>
      
      <Divider sx={{ my: 3 }} />
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Total: R$ {calculateTotal.toFixed(2)}
        </Typography>
        <Box>
          <Button onClick={onCancel} sx={{ mr: 1 }}>
            Cancelar
          </Button>
          <Button 
            variant="contained" 
            color="primary"
            onClick={handleSubmit}
            startIcon={<ShoppingCartIcon />}
          >
            Finalizar Venda
          </Button>
        </Box>
      </Box>
    </Box>
  );
}; 