import { useState, useEffect, useCallback } from 'react';
import { Package, PackageFormData } from '../types/packages';

interface UsePackagesResult {
  packages: Package[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getPackageById: (id: number) => Promise<Package | null>;
  createPackage: (packageData: PackageFormData) => Promise<Package | null>;
  updatePackage: (id: number, packageData: Partial<PackageFormData>) => Promise<Package | null>;
  deletePackage: (id: number) => Promise<boolean>;
  getAllActivePackages: () => Promise<Package[]>;
}

export const usePackages = (): UsePackagesResult => {
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPackages = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('packages:getAll');
      if (response.success) {
        setPackages(response.data);
      } else {
        setError(response.error || 'Falha ao buscar pacotes');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const getPackageById = useCallback(async (id: number): Promise<Package | null> => {
    try {
      const response = await window.electronAPI.invoke('packages:getById', id);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Falha ao buscar pacote com ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const createPackage = useCallback(async (packageData: PackageFormData): Promise<Package | null> => {
    try {
      const response = await window.electronAPI.invoke('packages:create', packageData);
      if (response.success) {
        await fetchPackages();
        return response.data;
      }
      setError(response.error || 'Falha ao criar pacote');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchPackages]);

  const updatePackage = useCallback(async (id: number, packageData: Partial<PackageFormData>): Promise<Package | null> => {
    try {
      const response = await window.electronAPI.invoke('packages:update', id, packageData);
      if (response.success) {
        await fetchPackages();
        return response.data;
      }
      setError(response.error || `Falha ao atualizar pacote com ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchPackages]);

  const deletePackage = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('packages:delete', id);
      if (response.success) {
        await fetchPackages();
        return true;
      }
      setError(response.error || `Falha ao desativar pacote com ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchPackages]);

  const getAllActivePackages = useCallback(async (): Promise<Package[]> => {
    try {
      const response = await window.electronAPI.invoke('packages:getAllActive');
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao buscar pacotes ativos');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  return {
    packages,
    loading,
    error,
    refetch: fetchPackages,
    getPackageById,
    createPackage,
    updatePackage,
    deletePackage,
    getAllActivePackages
  };
}; 