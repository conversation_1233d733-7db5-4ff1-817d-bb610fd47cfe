import React, { useState, useEffect, useRef, useCallback } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Alert from '@mui/material/Alert';
import Grid from '@mui/material/Grid';
import Divider from '@mui/material/Divider';
import CircularProgress from '@mui/material/CircularProgress';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import IconButton from '@mui/material/IconButton';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Snackbar from '@mui/material/Snackbar';
import InputAdornment from '@mui/material/InputAdornment';
import Tooltip from '@mui/material/Tooltip';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Slider from '@mui/material/Slider';
import { Save as SaveIcon } from '@mui/icons-material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  KeyboardArrowRight as ArrowIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ColorLens as ColorLensIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  Receipt as ReceiptIcon,
  Healing as HealingIcon,
  Payments as PaymentsIcon,
} from '@mui/icons-material';
import { Backup as BackupIcon, CloudUpload as CloudUploadIcon, CloudDownload as CloudDownloadIcon } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useServices } from '../hooks/useServices';
import { alpha, ThemeProvider, createTheme, useTheme } from '@mui/material/styles';
import { theme as defaultTheme } from '../theme';
import { useAppTheme } from '../contexts/ThemeContext';
import { useLocation } from 'react-router-dom';
import { useNotifications } from '../contexts/NotificationContext';
import { HexColorPicker } from 'react-colorful';

// Interface para dados do formulário de informações da loja
interface StoreInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  cnpj: string;
  additionalInfo: string;
}

// Interface para formulário de serviço
interface ServiceFormData {
  id?: number;
  name: string;
  description: string;
  duration_minutes: number;
  price: number;
}

// Interface para configurações de notificações de vendas pendentes
interface PendingSalesSettings {
  enabled: boolean;
  thresholdDays: number;
}

const Settings: React.FC = () => {
  const { user, setPin, isPinConfigured, checkPinConfigured } = useAuth();
  const { addNotification } = useNotifications();
  const { services, loading, createService, updateService, deleteService, softDeleteService } = useServices();
  const { primaryColor: currentPrimaryColor, secondaryColor: currentSecondaryColor, updateTheme } = useAppTheme();
  const location = useLocation();
  const theme = useTheme();
  
  // Check for tabIndex in location state
  const locationState = location.state as { tabIndex?: number } | null;
  const initialTabValue = locationState?.tabIndex ?? 0;
  
  // Estado para gerenciar a aba atual
  const [tabValue, setTabValue] = useState(initialTabValue);
  
  // Estados para alteração de senha
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Estados para alteração de PIN
  const [currentPin, setCurrentPin] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [pinError, setPinError] = useState('');
  const [pinSuccess, setPinSuccess] = useState(false);
  const [isPinChanging, setIsPinChanging] = useState(false);
  
  // Estados para alteração de cor
  const [primaryColor, setPrimaryColor] = useState(currentPrimaryColor);
  const [secondaryColor, setSecondaryColor] = useState(currentSecondaryColor);
  const [themePreview, setThemePreview] = useState(defaultTheme);
  const [colorSuccess, setColorSuccess] = useState(false);
  
  // Estados para serviços
  const [serviceDialogOpen, setServiceDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<number | null>(null);
  const [selectedService, setSelectedService] = useState<ServiceFormData | null>(null);
  const [serviceFormData, setServiceFormData] = useState<ServiceFormData>({
    name: '',
    description: '',
    duration_minutes: 30,
    price: 0,
  });
  const [serviceError, setServiceError] = useState('');
  const [serviceSuccess, setServiceSuccess] = useState(false);
  
  // Estados para informações do recibo
  const [storeInfo, setStoreInfo] = useState<StoreInfo>({
    name: 'Pet Shop Management',
    address: 'Rua Principal, 123, Centro, São Paulo - SP',
    phone: '(11) 98765-4321',
    email: '<EMAIL>',
    website: 'www.petshop.com.br',
    cnpj: '12.345.678/0001-90',
    additionalInfo: 'Obrigado pela preferência!',
  });
  const [storeInfoSuccess, setStoreInfoSuccess] = useState(false);
  
  // Estados para configurações de vendas pendentes (Fiado)
  const [pendingSalesSettings, setPendingSalesSettings] = useState<PendingSalesSettings>({
    enabled: true,
    thresholdDays: 30
  });
  const [pendingSalesSettingsSuccess, setPendingSalesSettingsSuccess] = useState(false);
  
  // Mensagem de sucesso global
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  
  // Timer ref for debouncing
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Debounced theme preview update
  const debouncedUpdateThemePreview = useCallback((primary: string, secondary: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(() => {
      const newTheme = createTheme({
        ...defaultTheme,
        palette: {
          ...defaultTheme.palette,
          primary: {
            main: primary,
            light: alpha(primary, 0.8),
            dark: alpha(primary, 0.7),
          },
          secondary: {
            main: secondary,
            light: alpha(secondary, 0.8),
            dark: alpha(secondary, 0.7),
          },
        },
      });
      
      setThemePreview(newTheme);
    }, 300); // 300ms debounce delay
  }, []);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);
  
  // Função para atualizar o tema de visualização
  const updateThemePreview = (primary: string, secondary: string) => {
    debouncedUpdateThemePreview(primary, secondary);
  };
  
  // Efeito para carregar as informações da loja do armazenamento local
  useEffect(() => {
    const savedStoreInfo = localStorage.getItem('storeInfo');
    if (savedStoreInfo) {
      setStoreInfo(JSON.parse(savedStoreInfo));
    }
    
    // Use the values from context instead of localStorage
    setPrimaryColor(currentPrimaryColor);
    setSecondaryColor(currentSecondaryColor);
    
    // Update the preview
    updateThemePreview(currentPrimaryColor, currentSecondaryColor);
    
    // Load pending sales notification settings
    const savedPendingSalesSettings = localStorage.getItem('pendingSalesSettings');
    if (savedPendingSalesSettings) {
      setPendingSalesSettings(JSON.parse(savedPendingSalesSettings));
    }
  }, [currentPrimaryColor, currentSecondaryColor, debouncedUpdateThemePreview]);
  
  // Effect to refresh PIN configuration status if the component mounts
  // or user/auth state changes elsewhere. This helps ensure `isPinConfigured` is fresh.
  useEffect(() => {
    checkPinConfigured();
  }, [checkPinConfigured]);
  
  // Manipuladores para a alteração de abas
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Manipuladores para alteração de senha
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError('');
    
    if (newPassword !== confirmPassword) {
      setPasswordError('As senhas não coincidem');
      return;
    }
    
    try {
      // Chamada da API para alterar a senha
      const response = await window.electronAPI.invoke('auth:changePassword', {
        userId: user?.id,
        currentPassword,
        newPassword,
      });
      
      if (response.success) {
        setPasswordSuccess(true);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
        showSuccessMessage('Senha alterada com sucesso!');
      } else {
        setPasswordError(response.error || 'Falha ao alterar a senha');
      }
    } catch (error) {
      setPasswordError('Erro ao alterar a senha. Verifique se a senha atual está correta.');
    }
  };
  
  // Manipuladores para alteração de PIN
  const handlePinSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setPinError('');
    setPinSuccess(false);
    setIsPinChanging(true);

    if (newPin.length !== 4 || !/^\d+$/.test(newPin)) {
      setPinError('O PIN deve ter 4 dígitos numéricos');
      setIsPinChanging(false);
      return;
    }

    if (newPin !== confirmPin) {
      setPinError('Os PINs não correspondem');
      setIsPinChanging(false);
      return;
    }

    try {
      // Verify current PIN if one is configured
      if (isPinConfigured) {
        if (!currentPin) {
          setPinError('Por favor, insira o PIN atual.');
          setIsPinChanging(false);
          return;
        }
        const verifyResponse = await window.electronAPI.invoke('auth:verifyPin', currentPin);
        if (!verifyResponse.success || !verifyResponse.matches) {
          setPinError('PIN atual incorreto');
          setIsPinChanging(false);
          return;
        }
      }

      // Update the PIN
      const setResult = await setPin(newPin);
      if (setResult.success) {
        setPinSuccess(true);
        setCurrentPin('');
        setNewPin('');
        setConfirmPin('');
        addNotification({ title: 'PIN Alterado', message: 'PIN alterado com sucesso!', type: 'success' });
        // Optionally, refresh isPinConfigured status though setPin in AuthContext should do it
        await checkPinConfigured();
      } else {
        setPinError(setResult.error || 'Falha ao alterar o PIN.');
      }
    } catch (error) {
      console.error('Error changing PIN:', error);
      setPinError('Erro ao alterar o PIN. Tente novamente.');
    }
    setIsPinChanging(false);
  };
  
  // Manipuladores para alteração de cor
  const handleColorSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Update the theme using the context
    updateTheme(primaryColor, secondaryColor);
    
    setColorSuccess(true);
    showSuccessMessage('Cores do tema atualizadas com sucesso! As mudanças foram aplicadas imediatamente.');
  };
  
  // Manipuladores para alteração de cor
  const handlePrimaryColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const color = e.target.value;
    setPrimaryColor(color);
    updateThemePreview(color, secondaryColor);
  };
  
  const handleSecondaryColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const color = e.target.value;
    setSecondaryColor(color);
    updateThemePreview(primaryColor, color);
  };
  
  // Manipuladores para serviços
  const handleOpenServiceDialog = (service?: any) => {
    if (service) {
      setSelectedService(service);
      setServiceFormData({
        id: service.id,
        name: service.name,
        description: service.description || '',
        duration_minutes: service.duration_minutes || 30,
        price: service.price || 0,
      });
    } else {
      setSelectedService(null);
      setServiceFormData({
        name: '',
        description: '',
        duration_minutes: 30,
        price: 0,
      });
    }
    setServiceDialogOpen(true);
  };
  
  const handleCloseServiceDialog = () => {
    setServiceDialogOpen(false);
    setServiceError('');
  };
  
  const handleServiceFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    setServiceFormData(prev => ({
      ...prev,
      [name]: name === 'duration_minutes' || name === 'price' ? parseFloat(value) : value,
    }));
  };
  
  const handleSaveService = async () => {
    try {
      setServiceError('');
      
      if (!serviceFormData.name.trim()) {
        setServiceError('O nome do serviço é obrigatório');
        return;
      }
      
      if (serviceFormData.price < 0) {
        setServiceError('O preço não pode ser negativo');
        return;
      }
      
      if (serviceFormData.duration_minutes <= 0) {
        setServiceError('A duração deve ser maior que zero');
        return;
      }
      
      if (selectedService) {
        // Atualizar serviço existente
        await updateService(selectedService.id!, serviceFormData);
      } else {
        // Criar novo serviço
        await createService(serviceFormData);
      }
      
      setServiceDialogOpen(false);
      setServiceSuccess(true);
      showSuccessMessage(selectedService ? 'Serviço atualizado com sucesso!' : 'Serviço criado com sucesso!');
    } catch (error) {
      setServiceError('Erro ao salvar o serviço');
    }
  };
  
  const handleDeleteService = (id: number) => {
    setServiceToDelete(id);
    setDeleteDialogOpen(true);
  };
  
  const confirmDeleteService = async () => {
    if (serviceToDelete !== null) {
      try {
        await softDeleteService(serviceToDelete);
        showSuccessMessage('Serviço excluído com sucesso!');
      } catch (error) {
        console.error('Erro ao excluir serviço:', error);
      }
    }
    setDeleteDialogOpen(false);
    setServiceToDelete(null);
  };
  
  // Manipuladores para informações do recibo
  const handleStoreInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    setStoreInfo(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  const handleStoreInfoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Salvar as informações da loja no armazenamento local
    localStorage.setItem('storeInfo', JSON.stringify(storeInfo));
    
    setStoreInfoSuccess(true);
    showSuccessMessage('Informações do recibo atualizadas com sucesso!');
  };
  
  // Manipuladores para configurações de vendas pendentes (Fiado)
  const handlePendingSalesSettingsChange = (property: keyof PendingSalesSettings, value: any) => {
    setPendingSalesSettings(prev => ({
      ...prev,
      [property]: value
    }));
  };

  const handlePendingSalesSettingsSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validar se o limite de dias é um número positivo
    if (pendingSalesSettings.thresholdDays <= 0) {
      showSuccessMessage('O limite de dias deve ser maior que zero.');
      return;
    }
    
    // Salvar as configurações no localStorage
    localStorage.setItem('pendingSalesSettings', JSON.stringify(pendingSalesSettings));
    
    setPendingSalesSettingsSuccess(true);
    showSuccessMessage('Configurações de vendas pendentes atualizadas com sucesso!');
    
    // Add demo notification
    if (pendingSalesSettings.enabled) {
      addNotification({
        title: 'Demonstração - Venda pendente em atraso',
        message: `Esta é uma demonstração de como serão as notificações de vendas pendentes após ${pendingSalesSettings.thresholdDays} dias.`,
        type: 'warning'
      });
    }
  };
  
  // Função para exibir mensagem de sucesso
  const showSuccessMessage = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarOpen(true);
  };
  
  // Função para fechar o snackbar
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };
  
  // Adiciono referência e handlers para exportação/importação do backup
  type FileInputRef = HTMLInputElement | null;
  const fileInputRef = useRef<FileInputRef>(null);

  const handleExportDatabase = async () => {
    try {
      const response = await window.electronAPI.invoke('db:exportDatabase');
      if (response.success) {
        showSuccessMessage('Backup exportado com sucesso!');
      } else {
        setSnackbarMessage(response.error || 'Falha ao exportar backup');
        setSnackbarOpen(true);
      }
    } catch (error) {
      setSnackbarMessage('Erro ao exportar backup');
      setSnackbarOpen(true);
    }
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const filePath = (e.target.files?.[0] as any)?.path;
    if (filePath) {
      try {
        const response = await window.electronAPI.invoke('db:importDatabase', filePath);
        if (response.success) {
          showSuccessMessage('Banco de dados importado com sucesso!');
        } else {
          setSnackbarMessage(response.error || 'Falha ao importar banco de dados');
          setSnackbarOpen(true);
        }
      } catch (error) {
        setSnackbarMessage('Erro ao importar banco de dados');
        setSnackbarOpen(true);
      }
    }
  };
  
  // Componente de visualização das cores do tema
  const ColorPreview: React.FC<{ color: string, label: string }> = ({ color, label }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      <Box 
        sx={{ 
          width: 24, 
          height: 24, 
          bgcolor: color, 
          borderRadius: 1, 
          mr: 1,
          border: '1px solid #ccc'
        }} 
      />
      <Typography variant="body2">{label}</Typography>
    </Box>
  );
  
  // Renderizar conteúdo com base na aba atual
  const renderTabContent = () => {
    switch (tabValue) {
      case 0: // Segurança
        return (
          <Grid container spacing={4}>
            {/* Alterar Senha */}
            {/*
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Alterar Senha
                </Typography>
                <form onSubmit={handlePasswordSubmit}>
                  {passwordError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {passwordError}
                    </Alert>
                  )}
                  {passwordSuccess && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      Senha alterada com sucesso!
                    </Alert>
                  )}
                  <TextField
                    label="Senha Atual"
                    type={showCurrentPassword ? "text" : "password"}
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    fullWidth
                    margin="normal"
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            edge="end"
                          >
                            {showCurrentPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                  <TextField
                    label="Nova Senha"
                    type={showNewPassword ? "text" : "password"}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    fullWidth
                    margin="normal"
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            edge="end"
                          >
                            {showNewPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                  <TextField
                    label="Confirmar Nova Senha"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    fullWidth
                    margin="normal"
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                          >
                            {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    sx={{ mt: 2 }}
                    startIcon={<SaveIcon />}
                  >
                    Salvar Nova Senha
                  </Button>
                </form>
              </Paper>
            </Grid>
            */}
            
            {/* Alterar PIN */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Alterar PIN de Bloqueio
                </Typography>
                <form onSubmit={handlePinSubmit}>
                  {pinError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {pinError}
                    </Alert>
                  )}
                  {pinSuccess && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      PIN alterado com sucesso!
                    </Alert>
                  )}
                  {isPinConfigured && (
                    <TextField
                      label="PIN Atual"
                      type="password"
                      value={currentPin}
                      onChange={(e) => {
                        const value = e.target.value.replace(/[^0-9]/g, '');
                        if (value.length <= 4) setCurrentPin(value);
                      }}
                      fullWidth
                      margin="normal"
                      required
                      inputProps={{ maxLength: 4, inputMode: 'numeric' }}
                    />
                  )}
                  <TextField
                    label="Novo PIN"
                    type="password"
                    value={newPin}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, '');
                      if (value.length <= 4) setNewPin(value);
                    }}
                    fullWidth
                    margin="normal"
                    required
                    inputProps={{ maxLength: 4, inputMode: 'numeric' }}
                    helperText="PIN deve ter 4 dígitos numéricos"
                  />
                  <TextField
                    label="Confirmar Novo PIN"
                    type="password"
                    value={confirmPin}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, '');
                      if (value.length <= 4) setConfirmPin(value);
                    }}
                    fullWidth
                    margin="normal"
                    required
                    inputProps={{ maxLength: 4, inputMode: 'numeric' }}
                  />
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    sx={{ mt: 2 }}
                    startIcon={isPinChanging ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                    disabled={isPinChanging || newPin.length !== 4 || confirmPin.length !== 4 || (isPinConfigured && !currentPin)}
                  >
                    {isPinChanging ? 'Salvando...' : 'Salvar Novo PIN'}
                  </Button>
                </form>
              </Paper>
            </Grid>
          </Grid>
        );
        
      case 1: // Aparência
        return (
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Cores do Tema
                </Typography>
                <form onSubmit={handleColorSubmit}>
                  {colorSuccess && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      Cores do tema atualizadas com sucesso! As mudanças foram aplicadas imediatamente.
                    </Alert>
                  )}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Cor Primária
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <HexColorPicker 
                        color={primaryColor} 
                        onChange={(color) => {
                          setPrimaryColor(color);
                          updateThemePreview(color, secondaryColor);
                        }}
                        style={{ width: '100%', height: 200, marginBottom: 12 }}
                      />
                      <TextField 
                        value={primaryColor}
                        onChange={handlePrimaryColorChange}
                        fullWidth
                        size="small"
                        label="Cor Primária (Hex)"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                    
                    <Typography variant="subtitle2" gutterBottom>
                      Cor Secundária
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <HexColorPicker 
                        color={secondaryColor} 
                        onChange={(color) => {
                          setSecondaryColor(color);
                          updateThemePreview(primaryColor, color);
                        }}
                        style={{ width: '100%', height: 200, marginBottom: 12 }}
                      />
                      <TextField 
                        value={secondaryColor}
                        onChange={handleSecondaryColorChange}
                        fullWidth
                        size="small"
                        label="Cor Secundária (Hex)"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                  </Box>
                  
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    sx={{ mt: 2 }}
                  >
                    Salvar Cores
                  </Button>
                </form>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Pré-visualização
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <ColorPreview color={primaryColor} label="Primária" />
                  <ColorPreview color={secondaryColor} label="Secundária" />
                </Box>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  Como ficará o aplicativo:
                </Typography>
                <ThemeProvider theme={themePreview}>
                  <Box sx={{ mt: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Button variant="contained" color="primary" sx={{ mr: 1, mb: 1 }}>
                      Botão Primário
                    </Button>
                    <Button variant="contained" color="secondary" sx={{ mr: 1, mb: 1 }}>
                      Botão Secundário
                    </Button>
                    <Box sx={{ mt: 2, mb: 2 }}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" color="primary" gutterBottom>
                            Título em Cor Primária
                          </Typography>
                          <Typography variant="body2">
                            Este é um exemplo de como suas cores irão aparecer em todo o aplicativo.
                          </Typography>
                        </CardContent>
                      </Card>
                    </Box>
                  </Box>
                </ThemeProvider>
              </Paper>
            </Grid>
          </Grid>
        );
        
      case 2: // Serviços
        return (
          <Box>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Serviços Disponíveis
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={() => handleOpenServiceDialog()}
                >
                  Novo Serviço
                </Button>
              </Box>
              
              <Divider sx={{ mb: 2 }} />
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : services.length > 0 ? (
                <List>
                  {services.map((service) => (
                    <React.Fragment key={service.id}>
                      <ListItem>
                        <ListItemText
                          primary={service.name}
                          secondary={
                            <Box component="span">
                              <Tooltip 
                                title={
                                  <Typography
                                    style={{
                                      whiteSpace: 'pre-line',
                                      fontSize: '0.875rem'
                                    }}
                                    component="span"
                                  >
                                    {service.description || "Sem descrição"}
                                  </Typography>
                                }
                                arrow
                                placement="bottom-start"
                              >
                                <Typography
                                  variant="body2"
                                  component="span"
                                  color="text.secondary"
                                  sx={{
                                    maxHeight: '4.5em',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 3,
                                    WebkitBoxOrient: 'vertical',
                                    whiteSpace: 'pre-line'
                                  }}
                                >
                                  {service.description || "Sem descrição"}
                                </Typography>
                              </Tooltip>
                              <Box component="span" sx={{ mt: 0.5 }}>
                                <Typography
                                  variant="body2"
                                  component="span"
                                  color="primary"
                                  sx={{ mr: 2 }}
                                >
                                  R$ {service.price?.toFixed(2)}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  component="span"
                                  color="text.secondary"
                                >
                                  {service.duration_minutes} min
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Tooltip title="Editar">
                            <IconButton edge="end" onClick={() => handleOpenServiceDialog(service)} sx={{ mr: 1 }}>
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Excluir">
                            <IconButton 
                              edge="end" 
                              color="error"
                              onClick={() => handleDeleteService(service.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography color="text.secondary">
                    Nenhum serviço cadastrado. Crie um novo serviço para começar.
                  </Typography>
                </Box>
              )}
            </Paper>
            
            {/* Diálogo para adicionar/editar serviço */}
            <Dialog
              open={serviceDialogOpen}
              onClose={handleCloseServiceDialog}
              aria-labelledby="service-dialog-title"
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle id="service-dialog-title">
                {selectedService ? 'Editar Serviço' : 'Novo Serviço'}
              </DialogTitle>
              <DialogContent>
                {serviceError && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {serviceError}
                  </Alert>
                )}
                <TextField
                  label="Nome do Serviço"
                  name="name"
                  value={serviceFormData.name}
                  onChange={handleServiceFormChange}
                  fullWidth
                  margin="normal"
                  required
                />
                <TextField
                  label="Descrição"
                  name="description"
                  value={serviceFormData.description}
                  onChange={handleServiceFormChange}
                  fullWidth
                  margin="normal"
                  multiline
                  rows={2}
                />
                <TextField
                  label="Duração (minutos)"
                  name="duration_minutes"
                  type="number"
                  value={serviceFormData.duration_minutes}
                  onChange={handleServiceFormChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{ inputProps: { min: 1 } }}
                />
                <TextField
                  label="Preço (R$)"
                  name="price"
                  type="number"
                  value={serviceFormData.price}
                  onChange={handleServiceFormChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                />
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseServiceDialog}>Cancelar</Button>
                <Button 
                  onClick={handleSaveService} 
                  variant="contained" 
                  color="primary"
                  startIcon={<SaveIcon />}
                >
                  Salvar
                </Button>
              </DialogActions>
            </Dialog>
            
            {/* Diálogo de confirmação para desativar serviço */}
            <Dialog
              open={deleteDialogOpen}
              onClose={() => setDeleteDialogOpen(false)}
              aria-labelledby="delete-dialog-title"
              aria-describedby="delete-dialog-description"
            >
              <DialogTitle id="delete-dialog-title">
                Confirmar Exclusão
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1">
                  Tem certeza que deseja excluir este serviço? 
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Esta ação é irreversível e o serviço não poderá ser recuperado.
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDeleteDialogOpen(false)}>Cancelar</Button>
                <Button 
                  onClick={confirmDeleteService} 
                  variant="contained" 
                  color="error"
                  startIcon={<DeleteIcon />}
                >
                  Excluir
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        );
        
      case 3: // Informações do Recibo
        return (
          <Box>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Informações para Recibo de Vendas
              </Typography>
              <form onSubmit={handleStoreInfoSubmit}>
                {storeInfoSuccess && (
                  <Alert severity="success" sx={{ mb: 2 }}>
                    Informações do recibo atualizadas com sucesso!
                  </Alert>
                )}
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      label="Nome da Loja"
                      name="name"
                      value={storeInfo.name}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Endereço"
                      name="address"
                      value={storeInfo.address}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Telefone"
                      name="phone"
                      value={storeInfo.phone}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="E-mail"
                      name="email"
                      type="email"
                      value={storeInfo.email}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Website"
                      name="website"
                      value={storeInfo.website}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="CNPJ"
                      name="cnpj"
                      value={storeInfo.cnpj}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Informações Adicionais"
                      name="additionalInfo"
                      value={storeInfo.additionalInfo}
                      onChange={handleStoreInfoChange}
                      fullWidth
                      margin="normal"
                      multiline
                      rows={2}
                      helperText="Mensagem que aparecerá no final do recibo"
                    />
                  </Grid>
                </Grid>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  sx={{ mt: 3 }}
                  startIcon={<SaveIcon />}
                >
                  Salvar Informações
                </Button>
              </form>
            </Paper>
          </Box>
        );
        
      case 4: // Vendas Pendentes (Fiado)
        return (
          <Box>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Configurações de Notificações para Vendas Pendentes (Fiado)
              </Typography>
              <form onSubmit={handlePendingSalesSettingsSubmit}>
                {pendingSalesSettingsSuccess && (
                  <Alert severity="success" sx={{ mb: 2 }}>
                    Configurações de notificações para vendas pendentes atualizadas com sucesso!
                  </Alert>
                )}
                
                <Box sx={{ mb: 4 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={pendingSalesSettings.enabled}
                        onChange={(e) => handlePendingSalesSettingsChange('enabled', e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Ativar notificações para vendas pendentes (Fiado)"
                  />
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 3 }}>
                    Quando ativado, você receberá notificações sobre vendas pendentes que ultrapassarem o limite de dias definido abaixo.
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography id="threshold-days-slider" gutterBottom>
                    Notificar após quantos dias?
                  </Typography>
                  
                  <Box sx={{ px: 1, py: 2 }}>
                    <Slider
                      value={pendingSalesSettings.thresholdDays}
                      onChange={(_, value) => handlePendingSalesSettingsChange('thresholdDays', value as number)}
                      aria-labelledby="threshold-days-slider"
                      valueLabelDisplay="auto"
                      step={1}
                      marks={[
                        { value: 7, label: '7 dias' },
                        { value: 15, label: '15 dias' },
                        { value: 30, label: '30 dias' },
                        { value: 60, label: '60 dias' },
                        { value: 90, label: '90 dias' },
                      ]}
                      min={1}
                      max={90}
                    />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    Você será notificado quando uma venda pendente (Fiado) completar o número de dias selecionado ({pendingSalesSettings.thresholdDays} dias) sem ser paga.
                  </Typography>
                </Box>
                
                <Box sx={{ mt: 2, p: 2, bgcolor: alpha(theme.palette.warning.light, 0.1), borderRadius: 1, border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}` }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Informação
                  </Typography>
                  <Typography variant="body2">
                    As configurações feitas aqui serão aplicadas para todas as novas verificações. Vendas pendentes que já receberam notificações anteriormente não serão afetadas por essas mudanças.
                  </Typography>
                </Box>
                
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  sx={{ mt: 3 }}
                  startIcon={<SaveIcon />}
                >
                  Salvar Configurações
                </Button>
              </form>
            </Paper>
          </Box>
        );
      case 5: // Backup
        return (
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Backup do Banco de Dados
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Clique em "Exportar Backup" para salvar uma cópia do banco de dados atual.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<CloudDownloadIcon />}
                  onClick={handleExportDatabase}
                >
                  Exportar Backup
                </Button>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Restaurar Banco de Dados
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Selecione um arquivo de backup (.sqlite) para importar o banco de dados.
                </Typography>
                <input
                  type="file"
                  accept=".sqlite"
                  style={{ display: 'none' }}
                  ref={fileInputRef}
                  onChange={handleFileInputChange}
                />
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<CloudUploadIcon />}
                  onClick={handleImportClick}
                >
                  Importar Backup
                </Button>
              </Paper>
            </Grid>
          </Grid>
        );
      default:
        return null;
    }
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SettingsIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 'bold', 
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 4,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 2,
              }
            }}
          >
            Configurações
          </Typography>
        </Box>
      </Box>
      
      <Paper>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          aria-label="configurações"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab 
            icon={<SecurityIcon />} 
            label="Segurança" 
            iconPosition="start"
          />
          <Tab 
            icon={<ColorLensIcon />} 
            label="Aparência" 
            iconPosition="start"
          />
          <Tab 
            icon={<HealingIcon />} 
            label="Serviços" 
            iconPosition="start"
          />
          <Tab 
            icon={<ReceiptIcon />} 
            label="Recibo" 
            iconPosition="start"
          />
          <Tab 
            icon={<PaymentsIcon />} 
            label="Fiado" 
            iconPosition="start"
          />
          <Tab 
            icon={<BackupIcon />} 
            label="Backup" 
            iconPosition="start"
          />
        </Tabs>
        <Box sx={{ p: 3 }}>
          {renderTabContent()}
        </Box>
      </Paper>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default Settings; 