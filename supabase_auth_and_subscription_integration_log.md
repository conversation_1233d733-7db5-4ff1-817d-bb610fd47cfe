## Supabase Authentication and Subscription Check Integration for Electron App

**Goal:** Update the application's login mechanism from a local SQLite database to Supabase authentication, including a check for active user subscriptions.

**Project Details (Supabase):**
*   Project ID: `amwkgykvqdrgqmhdsolj`
*   URL: `https://amwkgykvqdrgqmhdsolj.supabase.co`
*   Anon Public Key: (Provided by user, stored in `supabaseClient.ts`)

**Subscription Table Schema (`subscriptions`):**
*   `id`: `int8`
*   `user`: `uuid` (Foreign key to `auth.users.id`)
*   `created_at`: `timestamptz`
*   `end_at`: `timestamptz` (Used to validate subscription activity)

**Key Implementation Steps:**

1.  **Supabase Client Setup:**
    *   Created `src/main/supabaseClient.ts` to initialize and export the Supabase client using the project URL and anon key.

2.  **Backend Authentication Logic (`src/main/ipc/auth.ts`):**
    *   **Removed SQLite:** Eliminated the existing TypeORM user lookup and `bcryptjs` password comparison.
    *   **Supabase Login:** Implemented `supabase.auth.signInWithPassword({ email, password })` in the `auth:login` IPC handler. (Note: The frontend `username` field was treated as `email` for Supabase).
    *   **Subscription Check:**
        *   After successful Supabase login, a query is made to the `subscriptions` table:
            ```sql
            SELECT end_at FROM subscriptions WHERE "user" = <logged_in_user_id> ORDER BY end_at DESC LIMIT 1;
            ```
        *   The fetched `end_at` date is compared with the current server date.
        *   If `end_at` is in the past or no subscription record is found, an error is thrown (`'Subscription is inactive or expired.'`), preventing login.
    *   **Data Returned:** On successful login and active subscription, user details (ID, email, role, last login from Supabase, and `isSubscriptionActive: true`) are returned to the frontend.
    *   **Logout:** The `auth:logout` handler was updated to call `supabase.auth.signOut()`.
    *   **PIN Functionality:** Existing IPC handlers for screen lock PIN (`auth:setPinHash`, `auth:verifyPin`, `auth:isPinConfigured`) were preserved and remained untouched.

3.  **Frontend Authentication Context (`src/renderer/contexts/AuthContext.tsx`):**
    *   **User Interface:** The `User` interface was updated to reflect Supabase user data (e.g., `id` as string for UUID, `email` field) and to include the `isSubscriptionActive` boolean flag.
    *   **Login Function:**
        *   The `login` function was modified to accept `email` and `password`.
        *   It calls the updated `auth:login` IPC handler.
        *   It processes the response, updating the user state and `isSubscriptionActive` state.
    *   **Session Management:** Relies on Supabase for primary session management; `localStorage` for user object was mostly removed for session persistence, though state is managed within the context.

4.  **Login Page UI (`src/renderer/pages/Login.tsx`):**
    *   The username input field was changed to an email input field.
    *   The `login` function from `AuthContext` is called with `email` and `password`.
    *   Error messages from the backend (e.g., invalid credentials, expired subscription) are displayed.

**Troubleshooting & Refinements:**

*   **Initial Subscription Check Logic:** The first version of the subscription check correctly identified the status but didn't prevent login if inactive. This was fixed by throwing an error from the backend.
*   **Date Comparison Debugging:** When login failed even with a future `end_at` date, detailed debug logs were added to `src/main/ipc/auth.ts`.
*   **RLS Policy Discovery:** The debug logs revealed that the query to the `subscriptions` table returned no rows (`PGRST116` error), despite data being visible in Supabase Studio. This pointed to a Row Level Security (RLS) issue.
*   **RLS Policy Resolution:** The user was guided to add an RLS policy to the `subscriptions` table to allow authenticated users to read their own subscription records. The policy added was:
    ```sql
    CREATE POLICY "Users can read their own subscriptions"
    ON public.subscriptions
    FOR SELECT
    USING (auth.uid() = "user"); -- "user" being the column name for the user's UUID
    ```
    Adding this policy resolved the final issue, and the subscription check worked as intended.

**Outcome:** The application now uses Supabase for user authentication, and logins are gated by a valid, active subscription defined in the `subscriptions` table by comparing the `end_at` field with the current date. The PIN lock screen functionality remains operational.
