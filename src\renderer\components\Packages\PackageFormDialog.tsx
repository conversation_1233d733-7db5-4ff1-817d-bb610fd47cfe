import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';
import FormHelperText from '@mui/material/FormHelperText';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Checkbox from '@mui/material/Checkbox';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import InputBase from '@mui/material/InputBase';
import IconButton from '@mui/material/IconButton';
import { 
  LocalOffer as LocalOfferIcon,
  Description as DescriptionIcon,
  AttachMoney as AttachMoneyIcon,
  Today as TodayIcon,
  FormatListNumbered as FormatListNumberedIcon,
  Search as SearchIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { Package, PackageFormData } from '../../types/packages';
import { Service } from '../../types/sales';
import { SelectChangeEvent } from '@mui/material/Select';

interface PackageFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (packageData: PackageFormData) => void;
  packageItem?: Package;
  title: string;
  services: Service[];
}

export const PackageFormDialog: React.FC<PackageFormDialogProps> = ({
  open,
  onClose,
  onSave,
  packageItem,
  title,
  services
}) => {
  const initialFormData: PackageFormData = {
    name: '',
    description: '',
    price: 0,
    service_id: 0,
    total_occurrences: 1,
    frequency_type: 'weekly',
    frequency_interval: 1,
    is_active: true,
  };

  const [formData, setFormData] = useState<PackageFormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<Record<keyof PackageFormData, string>>>({});
  const [serviceSearchQuery, setServiceSearchQuery] = useState('');

  useEffect(() => {
    if (packageItem) {
      setFormData({
        name: packageItem.name,
        description: packageItem.description,
        price: packageItem.price,
        service_id: packageItem.service_id,
        total_occurrences: packageItem.total_occurrences,
        frequency_type: packageItem.frequency_type,
        frequency_interval: packageItem.frequency_interval,
        is_active: packageItem.is_active,
      });
    } else {
      setFormData(initialFormData);
    }
    setErrors({});
  }, [packageItem, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      if ([
        'price',
        'service_id',
        'total_occurrences',
        'frequency_interval'
      ].includes(name)) {
        setFormData(prev => ({ ...prev, [name]: Number(value) }));
      } else if (name === 'is_active') {
        setFormData(prev => ({ ...prev, is_active: (e.target as HTMLInputElement).checked }));
      } else {
        setFormData(prev => ({ ...prev, [name]: value }));
      }
      if (errors[name as keyof PackageFormData]) {
        setErrors(prev => ({ ...prev, [name]: undefined }));
      }
    }
  };

  const handleSelectChange = (event: SelectChangeEvent<any>) => {
    const { name, value } = event.target;
    setFormData(prev => ({ ...prev, [name!]: value }));
    if (errors[name as keyof PackageFormData]) {
      setErrors(prev => ({ ...prev, [name as keyof PackageFormData]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof PackageFormData, string>> = {};
    if (!formData.name.trim()) newErrors.name = 'Nome é obrigatório';
    if (!formData.service_id) newErrors.service_id = 'Selecione um serviço';
    if (formData.total_occurrences <= 0) newErrors.total_occurrences = 'Quantidade de ocorrências deve ser maior que zero';
    if (formData.price <= 0) newErrors.price = 'Preço deve ser maior que zero';
    if (!formData.frequency_type) newErrors.frequency_type = 'Tipo de frequência é obrigatório';
    if (formData.frequency_interval <= 0) newErrors.frequency_interval = 'Intervalo de frequência deve ser maior que zero';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  // Calculate regular price
  const regularPrice = services.find(s => s.id === formData.service_id)?.price || 0;

  // Calculate savings
  const savings = regularPrice - Number(formData.price);
  const savingsPercentage = regularPrice > 0 ? (savings / regularPrice) * 100 : 0;

  // Filter services based on search query
  const filteredServices = services.filter(service => 
    service.name.toLowerCase().includes(serviceSearchQuery.toLowerCase())
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h5" component="div">{title}</Typography>
        <Typography variant="subtitle2" component="p" color="text.secondary">
          Crie pacotes recorrentes para serviços automáticos
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        <Box sx={{ mt: 1 }}>
          <Grid container spacing={3}>
            {/* Nome e preço */}
            <Grid item xs={12} md={8}>
              <TextField
                name="name"
                label="Nome do Pacote"
                value={formData.name}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.name}
                helperText={errors.name}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="price"
                label="Preço do Pacote"
                type="number"
                value={formData.price}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.price}
                helperText={errors.price}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <AttachMoneyIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            {/* Serviço único */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required error={!!errors.service_id}>
                <InputLabel id="service-label">Serviço</InputLabel>
                <Select
                  labelId="service-label"
                  name="service_id"
                  value={formData.service_id}
                  label="Serviço"
                  onChange={handleSelectChange}
                >
                  <MenuItem value={0} disabled>Selecione um serviço</MenuItem>
                  {services.map(service => (
                    <MenuItem key={service.id} value={service.id}>{service.name}</MenuItem>
                  ))}
                </Select>
                <FormHelperText>{errors.service_id}</FormHelperText>
              </FormControl>
            </Grid>
            {/* Total de ocorrências */}
            <Grid item xs={12} md={6}>
              <TextField
                name="total_occurrences"
                label="Total de Ocorrências"
                type="number"
                value={formData.total_occurrences}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.total_occurrences}
                helperText={errors.total_occurrences || 'Quantas vezes o serviço será realizado no pacote'}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <FormatListNumberedIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            {/* Frequência */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required error={!!errors.frequency_type}>
                <InputLabel id="frequency-type-label">Tipo de Frequência</InputLabel>
                <Select
                  labelId="frequency-type-label"
                  name="frequency_type"
                  value={formData.frequency_type}
                  label="Tipo de Frequência"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="weekly">Semanal</MenuItem>
                  <MenuItem value="monthly">Mensal</MenuItem>
                  <MenuItem value="custom_days">Dias Personalizados</MenuItem>
                </Select>
                <FormHelperText>{errors.frequency_type}</FormHelperText>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="frequency_interval"
                label={
                  formData.frequency_type === 'weekly' ? 'A cada quantas semanas?' :
                  formData.frequency_type === 'monthly' ? 'A cada quantos meses?' :
                  'A cada quantos dias?'
                }
                type="number"
                value={formData.frequency_interval}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.frequency_interval}
                helperText={errors.frequency_interval || 'Intervalo entre as ocorrências'}
              />
            </Grid>
            {/* Ativo */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.is_active}
                    onChange={handleChange}
                    name="is_active"
                    color="primary"
                  />
                }
                label="Pacote Ativo (disponível para venda)"
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} variant="outlined">Cancelar</Button>
        <Button 
          onClick={handleSubmit} 
          color="primary" 
          variant="contained"
        >
          Salvar Pacote
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 