import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import {
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Check as CheckIcon
} from '@mui/icons-material';

interface PetBackflipCaptchaProps {
  onComplete: () => void;
}

const PetBackflipCaptcha: React.FC<PetBackflipCaptchaProps> = ({ onComplete }) => {
  const [rotation, setRotation] = useState(0);
  const [isSolved, setIsSolved] = useState(false);
  const [petIcon, setPetIcon] = useState('🐕');
  
  // Escolhe um ícone de animal aleatório quando o componente é montado
  useEffect(() => {
    const petIcons = ['🐕', '🐈', '🐇', '🦜'];
    const randomIndex = Math.floor(Math.random() * petIcons.length);
    setPetIcon(petIcons[randomIndex]);
  }, []);

  // Realiza a rotação para trás (360 graus)
  const rotateBackward = () => {
    if (isSolved) return;
    
    setRotation(prev => {
      const newRotation = prev + 90; // Rotacionar para a direita (trás do animal)
      // Verifica se o animal fez um backflip completo (360 graus)
      if (newRotation >= 360) {
        setIsSolved(true);
        onComplete();
        return 360; // Mantém na rotação final
      }
      return newRotation;
    });
  };

  // Realiza a rotação para frente
  const rotateForward = () => {
    if (isSolved) return;
    
    setRotation(prev => {
      const newRotation = prev - 90; // Rotacionar para a esquerda (frente do animal)
      return newRotation;
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 2 }}>
      <Typography variant="subtitle1" align="center" gutterBottom>
        Para confirmar a exclusão, faça o animal dar um backflip completo
      </Typography>
      
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2, gap: 2 }}>
        <IconButton 
          onClick={rotateForward}
          color="primary"
          disabled={isSolved}
        >
          <ArrowUpwardIcon />
        </IconButton>
        
        <Box 
          sx={{ 
            fontSize: '3rem', 
            transition: 'transform 0.5s ease',
            transform: `rotate(${rotation}deg)`,
            width: 70,
            height: 70,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: isSolved ? '2px solid #4caf50' : '1px dashed #ccc',
            borderRadius: '50%',
            backgroundColor: isSolved ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
          }}
        >
          {petIcon}
        </Box>
        
        <IconButton 
          onClick={rotateBackward}
          color="primary"
          disabled={isSolved}
          sx={{ 
            animation: isSolved ? 'none' : 'pulse 1.5s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 0.6 },
              '50%': { opacity: 1 },
              '100%': { opacity: 0.6 }
            }
          }}
        >
          <ArrowDownwardIcon />
        </IconButton>
      </Box>
      
      {isSolved && (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
          <CheckIcon sx={{ mr: 1 }} />
          <Typography variant="body2">Backflip completo! Agora você pode confirmar a exclusão.</Typography>
        </Box>
      )}
    </Box>
  );
};

export default PetBackflipCaptcha; 