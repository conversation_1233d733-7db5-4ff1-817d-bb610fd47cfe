import { useState, useEffect, useCallback } from 'react';
import { Pet } from '../../main/database/models/Pet';

interface UsePetsResult {
  pets: Pet[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getPetById: (id: number) => Promise<Pet | null>;
  getPetsByCustomerId: (customerId: number) => Promise<Pet[]>;
  createPet: (petData: Partial<Pet>) => Promise<Pet | null>;
  updatePet: (id: number, petData: Partial<Pet>) => Promise<Pet | null>;
  deletePet: (id: number) => Promise<boolean>;
  reactivatePet: (id: number) => Promise<Pet | null>;
}

export const usePets = (): UsePetsResult => {
  const [pets, setPets] = useState<Pet[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPets = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('pets:getAll');
      if (response.success) {
        setPets(response.data);
      } else {
        setError(response.error || 'Failed to fetch pets');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const getPetById = useCallback(async (id: number): Promise<Pet | null> => {
    try {
      const response = await window.electronAPI.invoke('pets:getById', id);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch pet with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const getPetsByCustomerId = useCallback(async (customerId: number): Promise<Pet[]> => {
    try {
      const response = await window.electronAPI.invoke('pets:getByCustomerId', customerId);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch pets for customer ID ${customerId}`);
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  const createPet = useCallback(async (petData: Partial<Pet>): Promise<Pet | null> => {
    try {
      const response = await window.electronAPI.invoke('pets:create', petData);
      if (response.success) {
        await fetchPets(); // Refresh the list
        return response.data;
      }
      setError(response.error || 'Failed to create pet');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchPets]);

  const updatePet = useCallback(async (id: number, petData: Partial<Pet>): Promise<Pet | null> => {
    try {
      const response = await window.electronAPI.invoke('pets:update', id, petData);
      if (response.success) {
        await fetchPets(); // Refresh the list
        return response.data;
      }
      setError(response.error || `Failed to update pet with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchPets]);

  const deletePet = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('pets:delete', id);
      if (response.success) {
        await fetchPets(); // Refresh the list
        return true;
      }
      setError(response.error || `Failed to delete pet with ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchPets]);

  const reactivatePet = useCallback(async (id: number): Promise<Pet | null> => {
    try {
      const response = await window.electronAPI.invoke('pets:reactivate', id);
      if (response.success) {
        await fetchPets();
        return response.data;
      }
      setError(response.error || `Failed to reactivate pet with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchPets]);

  useEffect(() => {
    fetchPets();
  }, [fetchPets]);

  return {
    pets,
    loading,
    error,
    refetch: fetchPets,
    getPetById,
    getPetsByCustomerId,
    createPet,
    updatePet,
    deletePet,
    reactivatePet
  };
}; 