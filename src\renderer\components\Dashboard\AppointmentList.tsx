import React, { useState } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import Tooltip from '@mui/material/Tooltip';
import Zoom from '@mui/material/Zoom';
import Fade from '@mui/material/Fade';
import {
  alpha,
  useTheme,
} from '@mui/material';
import {
  Pets as PetsIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CompletedIcon,
  HourglassEmpty as InProgressIcon,
  Schedule as ScheduledIcon,
  Cancel as CancelledIcon,
  PersonOff as NoShowIcon,
  AccessTime as TimeIcon,
  CalendarToday as CalendarIcon,
  CardGiftcard as CardGiftcardIcon,
} from '@mui/icons-material';
import { Appointment } from '../../types/dashboard';
import { differenceInCalendarDays, format, isToday, isTomorrow, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { getBrazilianNow, BRAZIL_TIMEZONE } from '../../utils/dateUtils';

interface AppointmentListProps {
  appointments: Appointment[];
  title: string;
  maxHeight?: string | number;
  onEdit?: (appointment: Appointment) => void;
  onDelete?: (appointmentId: number) => void;
  onStatusChange?: (appointmentId: number, status: string) => void;
}

export const AppointmentList: React.FC<AppointmentListProps> = ({ 
  appointments, 
  title,
  maxHeight = 400,
  onEdit,
  onDelete,
  onStatusChange
}) => {
  const theme = useTheme();
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | { el: HTMLElement, appointment: Appointment }>(null);
  const [statusMenuAnchorEl, setStatusMenuAnchorEl] = useState<null | { el: HTMLElement, appointment: Appointment }>(null);
  const [hoveredAppointment, setHoveredAppointment] = useState<number | null>(null);
  
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, appointment: Appointment) => {
    event.stopPropagation();
    setMenuAnchorEl({ el: event.currentTarget, appointment });
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };
  
  const handleStatusMenuOpen = (event: React.MouseEvent<HTMLElement>, appointment: Appointment) => {
    event.stopPropagation();
    setStatusMenuAnchorEl({ el: event.currentTarget, appointment });
  };
  
  const handleStatusMenuClose = () => {
    setStatusMenuAnchorEl(null);
  };
  
  const handleEdit = () => {
    if (menuAnchorEl && onEdit) {
      // console.log("Editing appointment from menu:", menuAnchorEl.appointment);
      onEdit(menuAnchorEl.appointment);
      handleMenuClose();
    }
  };
  
  const handleDelete = () => {
    if (menuAnchorEl && onDelete) {
      onDelete(menuAnchorEl.appointment.id);
      handleMenuClose();
    }
  };
  
  const handleStatusChange = (status: string) => {
    if (statusMenuAnchorEl && onStatusChange) {
      onStatusChange(statusMenuAnchorEl.appointment.id, status);
      handleStatusMenuClose();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': 
      case 'completed': return '#4caf50';
      case 'In Progress': 
      case 'in_progress': return '#ff9800';
      case 'Scheduled': 
      case 'scheduled': return '#2196f3';
      case 'Cancelled': 
      case 'cancelled': return '#f44336';
      case 'No Show':
      case 'no_show': return '#9e9e9e';
      default: return '#9e9e9e';
    }
  };
  
  const getStatusLabel = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'Concluído';
      case 'in_progress': return 'Em Andamento';
      case 'scheduled': return 'Agendado';
      case 'cancelled': return 'Cancelado';
      case 'no_show': return 'Faltou';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return <CompletedIcon fontSize="small" />;
      case 'in_progress': return <InProgressIcon fontSize="small" />;
      case 'scheduled': return <ScheduledIcon fontSize="small" />;
      case 'cancelled': return <CancelledIcon fontSize="small" />;
      case 'no_show': return <NoShowIcon fontSize="small" />;
      default: return <ScheduledIcon fontSize="small" />;
    }
  };

  const formatRelativeDate = (dateString: string) => {
    try {
      if (dateString.includes('-')) {
        // Parse date string (format: "YYYY-MM-DD HH:MM")
        const [datePart, timePart] = dateString.split(' ');
        const [year, month, day] = datePart.split('-').map(part => parseInt(part));
        
        // Get current date in Brazil timezone
        const brazilNow = getBrazilianNow();
        
        // Create date objects for comparison using local year/month/day values
        // Months are 0-indexed in JavaScript Date
        const appointmentDate = new Date(year, month - 1, day);
        
        // Get today and tomorrow dates based on Brazil timezone
        const today = new Date(
          brazilNow.getFullYear(),
          brazilNow.getMonth(),
          brazilNow.getDate()
        );
        
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        // Calculate difference in days between dates
        const daysDiff = Math.floor((appointmentDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        
        // console.log(`Date string: ${dateString}`);
        // console.log(`Appointment date: ${appointmentDate.toDateString()}`);
        // console.log(`Today's date: ${today.toDateString()}`);
        // console.log(`Days difference: ${daysDiff}`);
        
        // Format based on the difference
        if (appointmentDate.getTime() === today.getTime()) {
          return 'Hoje';
        } else if (appointmentDate.getTime() === tomorrow.getTime()) {
          return 'Amanhã';
        } else if (daysDiff > 0 && daysDiff < 7) {
          return `Em ${daysDiff} dias`;
        } else {
          return format(appointmentDate, "dd/MM", { locale: ptBR });
        }
      }
      return dateString;
    } catch (error) {
      console.error("Error formatting date:", error, dateString);
      return dateString;
    }
  };

  const hasDateInfo = (time: string) => time.includes('-');

  return (
    <Paper 
      sx={{ 
        p: 2, 
        height: '100%', 
        borderRadius: 2,
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
        transition: 'box-shadow 0.3s ease',
        '&:hover': {
          boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
        },
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Typography 
        variant="h6" 
        gutterBottom
        component="div"
        sx={{ 
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          pb: 1,
        }}
      >
        <ScheduledIcon color="primary" />
        <Box
          sx={{
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: '40px',
              height: '3px',
              backgroundColor: theme.palette.primary.main,
              borderRadius: '3px',
            }
          }}
        >
          {title}
        </Box>
      </Typography>
      <List 
        sx={{ 
          bgcolor: 'background.paper', 
          borderRadius: 1, 
          maxHeight: maxHeight, 
          overflow: 'auto',
          flexGrow: 1,
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: alpha(theme.palette.primary.main, 0.05),
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: alpha(theme.palette.primary.main, 0.2),
            borderRadius: '10px',
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.3),
            }
          },
        }}
      >
        {appointments.length === 0 ? (
          <Box 
            sx={{ 
              p: 3, 
              textAlign: 'center', 
              color: 'text.secondary', 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              minHeight: 100,
            }}
          >
            <ScheduledIcon sx={{ fontSize: 40, mb: 2, opacity: 0.5 }} />
            <Typography variant="subtitle1">Nenhum agendamento marcado</Typography>
            <Typography variant="body2">Não há agendamentos para o período selecionado</Typography>
          </Box>
        ) : (
          appointments.map((appointment, index) => (
            <Fade 
              in={true} 
              key={appointment.id}
              timeout={{ enter: 300 + index * 100, exit: 300 }}
              style={{ transitionDelay: `${index * 50}ms` }}
            >
              <Box>
                <ListItem
                  alignItems="flex-start"
                  secondaryAction={
                    onEdit || onDelete ? (
                      <IconButton 
                        edge="end" 
                        onClick={(e) => handleMenuOpen(e, appointment)}
                        sx={{
                          opacity: hoveredAppointment === appointment.id ? 1 : 0.5,
                          transition: 'opacity 0.2s ease',
                        }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    ) : null
                  }
                  onClick={() => onEdit && onEdit(appointment)}
                  onMouseEnter={() => setHoveredAppointment(appointment.id)}
                  onMouseLeave={() => setHoveredAppointment(null)}
                  sx={{
                    py: 1.5,
                    px: 2,
                    transition: 'all 0.2s ease-in-out',
                    borderLeft: `3px solid transparent`,
                    cursor: onEdit ? 'pointer' : 'default',
                    '&:hover': {
                      borderLeft: `3px solid ${getStatusColor(appointment.status)}`,
                      backgroundColor: alpha(getStatusColor(appointment.status), 0.05),
                    },
                    ...(hoveredAppointment === appointment.id 
                      ? { 
                          borderLeft: `3px solid ${getStatusColor(appointment.status)}`,
                          backgroundColor: alpha(getStatusColor(appointment.status), 0.05), 
                        } 
                      : {}
                    ),
                  }}
                >
                  <ListItemAvatar>
                    <Tooltip 
                      title={getStatusLabel(appointment.status)} 
                      arrow
                      placement="top"
                    >
                      <Avatar 
                        sx={{ 
                          bgcolor: alpha(getStatusColor(appointment.status), 0.7),
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            bgcolor: getStatusColor(appointment.status),
                            transform: 'scale(1.1)',
                          },
                        }}
                      >
                        <PetsIcon />
                      </Avatar>
                    </Tooltip>
                  </ListItemAvatar>
                  {appointment.is_package_appointment && (
                    <Tooltip title="Este agendamento faz parte de um pacote">
                      <CardGiftcardIcon color="secondary" sx={{ mr: 1 }} />
                    </Tooltip>
                  )}
                  <ListItemText
                    primaryTypographyProps={{ component: "div" }}
                    secondaryTypographyProps={{ component: "div" }}
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography 
                          component="span" 
                          variant="subtitle1" 
                          fontWeight="medium"
                          sx={{ 
                            transition: 'color 0.2s ease',
                            color: hoveredAppointment === appointment.id 
                              ? theme.palette.primary.main 
                              : 'text.primary'
                          }}
                        >
                          {appointment.petName}
                        </Typography>
                        <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          ({appointment.ownerName})
                        </Typography>
                        <Box sx={{ flex: 1 }} />
                        <Chip 
                          size="small" 
                          label={getStatusLabel(appointment.status)} 
                          icon={getStatusIcon(appointment.status)}
                          onClick={(e) => onStatusChange ? handleStatusMenuOpen(e, appointment) : undefined}
                          sx={{ 
                            mr: onDelete || onEdit ? 4 : 1,
                            bgcolor: alpha(getStatusColor(appointment.status), 0.1),
                            color: getStatusColor(appointment.status),
                            border: `1px solid ${alpha(getStatusColor(appointment.status), 0.2)}`,
                            fontSize: '0.7rem',
                            height: 24,
                            cursor: onStatusChange ? 'pointer' : 'default',
                            '&:hover': onStatusChange ? {
                              bgcolor: alpha(getStatusColor(appointment.status), 0.2),
                              boxShadow: `0 2px 4px ${alpha(getStatusColor(appointment.status), 0.2)}`,
                            } : {},
                            '& .MuiChip-icon': {
                              color: getStatusColor(appointment.status),
                            }
                          }} 
                        />
                      </Box>
                    }
                    secondary={
                      <Box 
                        sx={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          mt: 0.5
                        }}
                      >
                        {hasDateInfo(appointment.time) ? (
                          <CalendarIcon sx={{ color: 'text.secondary', fontSize: 16, mr: 0.5 }} />
                        ) : (
                          <TimeIcon sx={{ color: 'text.secondary', fontSize: 16, mr: 0.5 }} />
                        )}
                        <Typography component="div" variant="body2" color="text.secondary">
                          {hasDateInfo(appointment.time) ? (
                            <>
                              <Typography 
                                component="span" 
                                sx={{ 
                                  fontWeight: 'medium', 
                                  color: theme.palette.primary.main 
                                }}
                              >
                                {formatRelativeDate(appointment.time)}
                              </Typography>
                              <Typography component="span" sx={{ mx: 0.5 }}>•</Typography>
                              {appointment.time.split(' ')[1]}
                            </>
                          ) : (
                            appointment.time
                          )}
                          {' '} - {appointment.service}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
              </Box>
            </Fade>
          ))
        )}
      </List>
      
      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchorEl?.el}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        TransitionComponent={Zoom}
        PaperProps={{
          elevation: 3,
          sx: {
            minWidth: 180,
            borderRadius: 2,
            overflow: 'visible',
            mt: 1.5,
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
      >
        {onEdit && (
          <MenuItem onClick={handleEdit} sx={{ py: 1.5 }}>
            <ListItemIcon>
              <EditIcon fontSize="small" color="primary" />
            </ListItemIcon>
            Editar Agendamento
          </MenuItem>
        )}
        
        {onDelete && (
          <MenuItem 
            onClick={handleDelete}
            sx={{ 
              py: 1.5,
              color: 'error.main',
              '& .MuiListItemIcon-root': {
                color: 'error.main',
              }
            }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            Excluir Agendamento
          </MenuItem>
        )}
      </Menu>
      
      {/* Status Change Menu */}
      <Menu
        anchorEl={statusMenuAnchorEl?.el}
        open={Boolean(statusMenuAnchorEl)}
        onClose={handleStatusMenuClose}
        TransitionComponent={Zoom}
        PaperProps={{
          elevation: 3,
          sx: {
            minWidth: 180,
            borderRadius: 2,
            overflow: 'visible',
            mt: 1.5,
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
      >
        <MenuItem onClick={() => handleStatusChange('scheduled')} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <ScheduledIcon fontSize="small" sx={{ color: '#2196f3' }} />
          </ListItemIcon>
          Agendado
        </MenuItem>
        
        <MenuItem onClick={() => handleStatusChange('in_progress')} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <InProgressIcon fontSize="small" sx={{ color: '#ff9800' }} />
          </ListItemIcon>
          Em Andamento
        </MenuItem>
        
        <MenuItem onClick={() => handleStatusChange('completed')} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <CompletedIcon fontSize="small" sx={{ color: '#4caf50' }} />
          </ListItemIcon>
          Concluído
        </MenuItem>
        
        <MenuItem onClick={() => handleStatusChange('cancelled')} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <CancelledIcon fontSize="small" sx={{ color: '#f44336' }} />
          </ListItemIcon>
          Cancelado
        </MenuItem>
        
        <MenuItem onClick={() => handleStatusChange('no_show')} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <NoShowIcon fontSize="small" sx={{ color: '#9e9e9e' }} />
          </ListItemIcon>
          Faltou
        </MenuItem>
      </Menu>
    </Paper>
  );
}; 