import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatePackageUsageHistory1726000000000 implements MigrationInterface {
    name = 'CreatePackageUsageHistory1726000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "package_usage_history" (
                "id" INTEGER PRIMARY KEY AUTOINCREMENT,
                "customer_package_id" INTEGER NOT NULL,
                "service_id" INTEGER,
                "pet_id" INTEGER,
                "sale_id" INTEGER,
                "sale_item_id" INTEGER,
                "usage_date" DATETIME NOT NULL DEFAULT (datetime('now')),
                "notes" TEXT,
                "created_at" DATETIME NOT NULL DEFAULT (datetime('now')),
                FOREIGN KEY ("customer_package_id") REFERENCES "customer_packages" ("id") ON DELETE CASCADE,
                FOREIGN KEY ("service_id") REFERENCES "services" ("id") ON DELETE SET NULL,
                <PERSON><PERSON><PERSON><PERSON><PERSON> KEY ("pet_id") REFERENCES "pets" ("id") ON DELETE SET NULL,
                FOREIGN KEY ("sale_id") REFERENCES "sales" ("id") ON DELETE SET NULL,
                FOREIGN KEY ("sale_item_id") REFERENCES "sale_items" ("id") ON DELETE SET NULL
            )
        `);

        // Create indexes
        await queryRunner.query(`CREATE INDEX "idx_package_usage_customer_package_id" ON "package_usage_history" ("customer_package_id")`);
        await queryRunner.query(`CREATE INDEX "idx_package_usage_service_id" ON "package_usage_history" ("service_id")`);
        await queryRunner.query(`CREATE INDEX "idx_package_usage_pet_id" ON "package_usage_history" ("pet_id")`);
        await queryRunner.query(`CREATE INDEX "idx_package_usage_sale_id" ON "package_usage_history" ("sale_id")`);
        await queryRunner.query(`CREATE INDEX "idx_package_usage_usage_date" ON "package_usage_history" ("usage_date")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_package_usage_customer_package_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_package_usage_service_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_package_usage_pet_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_package_usage_sale_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_package_usage_usage_date"`);
        
        // Drop the table
        await queryRunner.query(`DROP TABLE IF EXISTS "package_usage_history"`);
    }
} 