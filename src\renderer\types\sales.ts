import { Customer } from './customers';
import { Product } from './inventory';

export interface Sale {
  id: number;
  customerId?: number;
  customer_id: number;
  total_amount: number;
  payment_method: PaymentMethod;
  status: SaleStatus;
  sale_date: string | Date;
  created_at: string | Date;
  updated_at: string | Date;
  customer?: Customer;
  items: SaleItem[];
}

export interface SaleItem {
  id: number;
  saleId?: number;
  productId?: number | null;
  serviceId?: number | null;
  sale_id: number;
  product_id: number | null;
  service_id: number | null;
  quantity: number;
  price_per_unit: number;
  product?: Product;
  service?: Service;
  customer_package_id?: number | null;
  is_package_service?: boolean;
}

export interface Service {
  id: number;
  name: string;
  description?: string | null;
  price: number;
  duration_minutes: number;
  is_active?: boolean;
}

export interface SaleFormData {
  customer_id: number;
  payment_method: PaymentMethod;
  status: SaleStatus;
  items: SaleItemFormData[];
}

export interface SaleItemFormData {
  type: 'product' | 'service' | 'package';
  id: number;
  quantity: number;
  price_per_unit: number;
  name: string;
  petId?: number | null;
  customer_package_id?: number | null;
  is_package_service?: boolean;
}

export type PaymentMethod = 'cash' | 'credit_card' | 'debit_card' | 'online' | 'other';

export type SaleStatus = 'paid' | 'pending';

export const paymentMethodLabels: Record<PaymentMethod, string> = {
  cash: 'Dinheiro',
  credit_card: 'Cartão de Crédito',
  debit_card: 'Cartão de Débito',
  online: 'Pagamento Online',
  other: 'Outro'
};

export const saleStatusLabels: Record<SaleStatus, string> = {
  paid: 'Pago',
  pending: 'Pendente'
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('pt-BR', { 
    style: 'currency', 
    currency: 'BRL' 
  }).format(amount);
};

export const formatDate = (dateString: string): string => {
  if (!dateString) return 'Data inválida';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'Data inválida';
  return dateFormatterInstance.format(date);
};

// Create a single instance of DateTimeFormat for reuse
// This improves performance by reusing the same formatter
const dateFormatterInstance = new Intl.DateTimeFormat('pt-BR', {
  year: 'numeric',
  month: 'short',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
}); 