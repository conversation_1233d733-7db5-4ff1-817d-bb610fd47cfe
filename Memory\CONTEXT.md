# Contexto Atual do Projeto (petAI-Pacotes)

## 1. Objetivo
Implementar um sistema automatizado de pacotes recorrentes para serviços de pet shop, substituindo o modelo antigo de vouchers por um modelo de agendamento automático, controle de recorrência, tratamento de exceções (no-show) e histórico detalhado de uso.

## 2. Estado Atual
- **Modelos TypeORM**: Atualizados para suportar o novo sistema (`Package`, `CustomerPackage`, `Appointment`, `PackageUsageHistory`).
- **Serviços**:
  - `PackageService.ts`: CRUD e listagem de pacotes ativos.
  - `CustomerPackageService.ts`: Atribuição, ativação, agendamento inicial, listagem de pacotes do cliente, listagem de agendamentos de pacote e pacotes em hold. Implementado `handlePackageAppointmentNoShow` para pausar pacotes e registrar no histórico.
  - `AppointmentService.ts`: Implementado `catchUpNoShows` para marcar automaticamente agendamentos como "no_show" após um período de tolerância. Corrigido bug crítico de comparação de timezone (agora usa UTC para a lógica de backend), garantindo que apenas agendamentos realmente atrasados sejam marcados.
- **Banco de Dados**: Resetado para refletir o novo schema. Configuração de logging do TypeORM ajustada para `['error']` para reduzir spam no console.
- **IPC/Backend**:
  - Novos handlers IPC para todos os métodos dos serviços de pacotes, removendo completamente handlers e campos legados.
  - Adicionado handler IPC `appointments:catchUpNoShows` para disparo manual da lógica de no-show.
  - Lógica de `catchUpNoShows` é disparada no startup do app e no foco da janela principal.
  - Whitelist de canais atualizada no preload.
- **Frontend**:
  - Hooks `usePackages` e `useCustomerPackages` refatorados para usar apenas o novo sistema e canais IPC.
  - Página `Packages.tsx` já utiliza o novo hook.
  - Página `Customers.tsx` totalmente limpa de lógica legada de pacotes, agora delegando toda a lógica de pacotes do cliente ao `CustomerPackagesDialog`.
  - Componente `CustomerPackagesDialog` refatorado para usar o novo hook e modelo, sem dependências do sistema antigo.
  - Todos os fluxos de compra, ativação, visualização e histórico de pacotes agora passam pelo novo sistema.
  - Componentes e props legados removidos (ex: `BuyPackageDialog`, estados e funções antigas de pacote no Customers).
  - Removida lógica legada de auto no-show do frontend (`AppointmentCard.tsx`, `NotificationContext.tsx`) para centralizar no backend.
- **Documentação**:
  - Justificativa para criação dos novos serviços registrada em `development_notes/changes_with_justification.md`.
  - Memory bank atualizado continuamente.
  - `NO_SHOW PLAN.md` concluído, incluindo a correção do bug de timezone.

## 3. Próximos Passos
- Finalizar integração visual e UX dos fluxos de pacotes recorrentes.
- Testar todos os fluxos de compra, ativação, agendamento, uso e no-show de pacotes com dados reais.
- Ajustar mensagens, labels e feedbacks para PT-BR e contexto brasileiro.
- Garantir cobertura de testes e estabilidade do novo sistema.

---
Última atualização: Lógica de no-show implementada e corrigida (timezone), logs de debug removidos. Nenhuma dependência do sistema antigo permanece ativa. Frontend limpo de lógica de no-show legada.
