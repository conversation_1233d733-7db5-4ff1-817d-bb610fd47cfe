import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPackageInfoToSaleItems1711121000000 implements MigrationInterface {
    name = 'AddPackageInfoToSaleItems1711121000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add customer_package_id column
        await queryRunner.query(`ALTER TABLE "sale_items" ADD COLUMN "customer_package_id" INTEGER NULL`);
        
        // Add is_package_service column with default value of false
        await queryRunner.query(`ALTER TABLE "sale_items" ADD COLUMN "is_package_service" BOOLEAN NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove columns in reverse order
        await queryRunner.query(`ALTER TABLE "sale_items" DROP COLUMN "is_package_service"`);
        await queryRunner.query(`ALTER TABLE "sale_items" DROP COLUMN "customer_package_id"`);
    }
} 