import type { ForgeConfig } from '@electron-forge/shared-types';
import { MakerSquirrel } from '@electron-forge/maker-squirrel';
import { MakerZIP } from '@electron-forge/maker-zip';
import { MakerDeb } from '@electron-forge/maker-deb';
import { MakerRpm } from '@electron-forge/maker-rpm';
import { WebpackPlugin } from '@electron-forge/plugin-webpack';
import { mainConfig } from './webpack.main.config';
import { rendererConfig } from './webpack.renderer.config';
import path from 'path';

const config: ForgeConfig = {
  packagerConfig: {
    name: 'PetShop',
    asar: true,
    extraResource: [
      './public'
    ],
    icon: './src/assets/icons/win/icon',
    executableName: 'PetShop'
  },
  rebuildConfig: {},
  makers: [
    new MakerSquirrel({
      setupIcon: './src/assets/icons/win/icon.ico',
      authors: 'unknown',
      description: 'App de desktop para Pet Shop',
      noMsi: false,
      name: "PetShop",
      setupMsi: 'Pet-Shop-Setup.msi',
    }),
    new MakerZIP({}, ['darwin']),
    new MakerRpm({}),
    new MakerDeb({}),
  ],
  publishers: [
    {
      name: '@electron-forge/publisher-github',
      config: {
        repository: {
          owner: 'unknown7987',
          name: 'spacepet-releases',
        },
        prerelease: false,
        draft: false,
      },
    },
  ],
  plugins: [
    new WebpackPlugin({
      mainConfig,
      renderer: {
        config: rendererConfig,
        entryPoints: [
          {
            html: './src/renderer/index.html',
            js: './src/renderer/index.tsx',
            name: 'main_window',
            preload: {
              js: './src/preload.ts',
            },
          },
        ],
      },
      port: 3000,
      loggerPort: 9000,
    }),
  ],
};

export default config; 