import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import { useAuth } from '../contexts/AuthContext';

const SetupPin: React.FC = () => {
  const { setPin, needsPinSetup } = useAuth();
  const [localPin, setLocalPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setIsLoading(true);

    if (localPin.length !== 4) {
      setError('O PIN deve ter 4 dígitos');
      setIsLoading(false);
      return;
    }
    if (localPin !== confirmPin) {
      setError('Os PINs não correspondem');
      setIsLoading(false);
      return;
    }
    try {
      const result = await setPin(localPin);
      if (result.success) {
        // No need to set success message here, as the dialog will close automatically
        // because needsPinSetup will become false after PIN is set.
        // setSuccess('PIN configurado com sucesso!');
        // The dialog should close automatically as `needsPinSetup` becomes false.
        // If it doesn't, we might need a slight delay or a different mechanism.
      } else {
        setError(result.error || 'Falha ao configurar o PIN.');
      }
    } catch (apiError) {
      setError('Ocorreu um erro ao configurar o PIN.');
      console.error("PIN setup error:", apiError);
    }
    setIsLoading(false);
  };

  if (!needsPinSetup) {
    return null;
  }

  return (
    <Dialog open={true} disableEscapeKeyDown>
      <DialogTitle>Configurar PIN de Bloqueio</DialogTitle>
      <DialogContent>
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            minWidth: '300px',
            mt: 2,
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Por favor, configure um PIN de 4 dígitos para a tela de bloqueio
          </Typography>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}
          <TextField
            label="Digite o PIN"
            type="password"
            value={localPin}
            onChange={(e) => {
              const value = e.target.value.replace(/[^0-9]/g, '');
              if (value.length <= 4) {
                setLocalPin(value);
                if (error) setError('');
              }
            }}
            inputProps={{
              maxLength: 4,
              pattern: '[0-9]*',
              inputMode: 'numeric',
            }}
            autoFocus
          />
          <TextField
            label="Confirmar PIN"
            type="password"
            value={confirmPin}
            onChange={(e) => {
              const value = e.target.value.replace(/[^0-9]/g, '');
              if (value.length <= 4) {
                setConfirmPin(value);
                if (error) setError('');
              }
            }}
            inputProps={{
              maxLength: 4,
              pattern: '[0-9]*',
              inputMode: 'numeric',
            }}
            error={!!error && confirmPin.length === 4 && localPin !== confirmPin}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={isLoading || localPin.length !== 4 || confirmPin.length !== 4}
        >
          {isLoading ? 'Salvando...' : 'Definir PIN'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SetupPin; 