import React from 'react';
import { Box, CircularProgress, Typography, Paper } from '@mui/material';
import { useDatabaseStatus } from '../../contexts/DatabaseContext';

interface DatabaseLoaderProps {
  children: React.ReactNode;
}

const DatabaseLoader: React.FC<DatabaseLoaderProps> = ({ children }) => {
  const { isDatabaseReady } = useDatabaseStatus();

  if (isDatabaseReady) {
    return <>{children}</>;
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        width: '100vw',
        bgcolor: (theme) => theme.palette.background.default,
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          maxWidth: 400,
        }}
      >
        <CircularProgress size={60} thickness={4} sx={{ mb: 3 }} />
        <Typography variant="h5" gutterBottom align="center">
          Inicializando o sistema
        </Typography>
        <Typography variant="body1" color="text.secondary" align="center">
          Preparando o banco de dados, por favor aguarde...
        </Typography>
      </Paper>
    </Box>
  );
};

export default DatabaseLoader; 