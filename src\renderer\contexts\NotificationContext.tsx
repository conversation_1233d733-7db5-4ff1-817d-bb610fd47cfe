import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { Appointment } from '../types/appointments';
import { Sale } from '../types/sales';
import { useAppointments } from '../hooks/useAppointments';
import { useSales } from '../hooks/useSales';
import { formatBrazilianDate } from '../utils/dateUtils';
import { v4 as uuidv4 } from 'uuid';

// Constants
const NOTIFICATION_STORAGE_KEY = 'petai_notifications';
const NOTIFICATION_EXPIRY_DAYS = 7; // Notifications older than 7 days will be purged

// Define notification types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: Date;
  read: boolean;
  action?: () => void;
  link?: string;
  relatedId?: number;
}

// Split interfaces for different contexts
interface NotificationDataContextType {
  notifications: Notification[];
  unreadCount: number;
}

interface NotificationActionsContextType {
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
}

// Create separate contexts
const NotificationDataContext = createContext<NotificationDataContextType | undefined>(undefined);
const NotificationActionsContext = createContext<NotificationActionsContextType | undefined>(undefined);

// Simple notification sound generator - creates a short beep sound
const playNotificationSound = (type: 'info' | 'warning' | 'error' | 'success') => {
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    // Configure based on notification type
    switch (type) {
      case 'warning':
        oscillator.type = 'triangle';
        oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4
        oscillator.frequency.setValueAtTime(392, audioContext.currentTime + 0.2); // G4
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.5);
        break;
        
      case 'error':
        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4
        oscillator.frequency.setValueAtTime(220, audioContext.currentTime + 0.2); // A3
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.6);
        break;
        
      case 'success':
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
        oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.3);
        break;
        
      default: // info
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.2);
        break;
    }
  } catch (err) {
    console.error("Unable to play notification sound:", err);
  }
};

// Helper to prepare notifications for storage
const prepareNotificationsForStorage = (notifications: Notification[]): string => {
  // Convert Date objects to ISO strings for storage
  const preparedNotifications = notifications.map(notification => ({
    ...notification,
    timestamp: notification.timestamp instanceof Date 
      ? notification.timestamp.toISOString() 
      : notification.timestamp
  }));
  
  // Remove action functions as they can't be serialized
  const notificationsWithoutFunctions = preparedNotifications.map(({ action, ...rest }) => rest);
  
  return JSON.stringify(notificationsWithoutFunctions);
};

// Helper to prepare notifications from storage
const prepareNotificationsFromStorage = (storageData: string): Notification[] => {
  try {
    const parsedData = JSON.parse(storageData);
    
    // Convert ISO strings back to Date objects
    return parsedData.map((notification: any) => ({
      ...notification,
      timestamp: new Date(notification.timestamp)
    }));
  } catch (error) {
    console.error('Error parsing saved notifications:', error);
    return [];
  }
};

// Helper to remove expired notifications
const filterExpiredNotifications = (notifications: Notification[]): Notification[] => {
  const now = new Date();
  const expiryTime = now.getTime() - (NOTIFICATION_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
  
  return notifications.filter(notification => {
    const notificationTime = notification.timestamp.getTime();
    return notificationTime > expiryTime;
  });
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const { getUpcomingAppointments, updateAppointmentStatus } = useAppointments();
  const { getSalesByDateRange } = useSales();

  // Calculate unread count
  const unreadCount = useMemo(() => 
    notifications.filter(n => !n.read).length,
    [notifications]
  );

  // Refined state tracking with separate sets for different notification types
  const [notifiedNoShowEvents, setNotifiedNoShowEvents] = useState<Set<number>>(new Set());
  const [notifiedUpcomingReminders, setNotifiedUpcomingReminders] = useState<Set<number>>(new Set());
  const [notifiedExpirationWarnings, setNotifiedExpirationWarnings] = useState<Set<number>>(new Set());
  // Add state for tracking notified pending sales
  const [notifiedPendingSales, setNotifiedPendingSales] = useState<Set<number>>(new Set());

  // Cached appointments to reduce data fetching
  const [cachedAppointments, setCachedAppointments] = useState<Appointment[]>([]);
  // Add cached sales data
  const [cachedSales, setCachedSales] = useState<Sale[]>([]);
  const [lastFetchTime, setLastFetchTime] = useState<Date>(new Date(0));
  const [lastSalesFetchTime, setLastSalesFetchTime] = useState<Date>(new Date(0));

  // Function to save notifications to localStorage
  const saveNotificationsToStorage = useCallback((notificationsToSave: Notification[]) => {
    try {
      const storageData = prepareNotificationsForStorage(notificationsToSave);
      localStorage.setItem(NOTIFICATION_STORAGE_KEY, storageData);
      // console.log('[STORAGE] Saved notifications to localStorage:', notificationsToSave.length);
    } catch (error) {
      console.error('[STORAGE] Failed to save notifications to localStorage:', error);
    }
  }, []);

  // Load notifications from localStorage on initialization
  useEffect(() => {
    try {
      const savedNotifications = localStorage.getItem(NOTIFICATION_STORAGE_KEY);
      
      if (savedNotifications) {
        // Parse saved notifications and convert timestamps back to Date objects
        const parsedNotifications = prepareNotificationsFromStorage(savedNotifications);
        
        // Filter out expired notifications
        const activeNotifications = filterExpiredNotifications(parsedNotifications);
        
        // console.log('[STORAGE] Loaded saved notifications:', activeNotifications.length);
        
        // Only set if we have valid notifications
        if (activeNotifications.length > 0) {
          setNotifications(activeNotifications);
          
          // Also load notified IDs sets to prevent re-notifying
          const notifiedIds = {
            noShow: new Set<number>(),
            upcoming: new Set<number>(),
            expiration: new Set<number>(),
            pendingSales: new Set<number>()
          };
          
          // Reconstruct notification sets based on loaded notifications
          activeNotifications.forEach(notification => {
            if (notification.relatedId) {
              if (notification.title.includes('não comparecido')) {
                notifiedIds.noShow.add(notification.relatedId);
              } else if (notification.title.includes('em breve')) {
                notifiedIds.upcoming.add(notification.relatedId);
              } else if (notification.title.includes('expirará')) {
                notifiedIds.expiration.add(notification.relatedId);
              } else if (notification.title.includes('pendente')) {
                notifiedIds.pendingSales.add(notification.relatedId);
              }
            }
          });
          
          // Update notification tracking sets
          setNotifiedNoShowEvents(notifiedIds.noShow);
          setNotifiedUpcomingReminders(notifiedIds.upcoming);
          setNotifiedExpirationWarnings(notifiedIds.expiration);
          setNotifiedPendingSales(notifiedIds.pendingSales);
        }
      }
    } catch (error) {
      console.error('[STORAGE] Error loading saved notifications:', error);
    }
  }, []);

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (notifications.length > 0) {
      saveNotificationsToStorage(notifications);
    } else if (notifications.length === 0) {
      // Remove from localStorage if we have no notifications
      localStorage.removeItem(NOTIFICATION_STORAGE_KEY);
    }
  }, [notifications, saveNotificationsToStorage]);

  // Memoize action handlers
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => {
      const updated = prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      );
      return updated;
    });
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => {
      const allRead = prev.map(notification => ({ ...notification, read: true }));
      return allRead;
    });
  }, []);

  const removeNotification = useCallback((id: string) => {
    // console.log(`[DEBUG] Removing notification with ID: ${id}`);
    
    setNotifications(prev => {
      const filtered = prev.filter(notification => notification.id !== id);
      // console.log(`[DEBUG] Notifications before remove: ${prev.length}, after: ${filtered.length}`);
      return filtered;
    });
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
    // Also clear from localStorage
    localStorage.removeItem(NOTIFICATION_STORAGE_KEY);
    // Reset notification tracking
    setNotifiedNoShowEvents(new Set());
    setNotifiedUpcomingReminders(new Set());
    setNotifiedExpirationWarnings(new Set());
    setNotifiedPendingSales(new Set());
  }, []);

  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    // console.log('[DEBUG] addNotification called with:', notification);
    
    // Generate a stable ID for related notifications to prevent duplicates across runs
    const id = notification.relatedId
      ? `app-${notification.relatedId}-${notification.type}`
      : `gen-${uuidv4()}`;
    
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
      read: false,
    };
    
    // console.log('[DEBUG] New notification created with ID:', newNotification.id);
    
    // Check if a similar notification already exists to avoid duplicates
    const hasSimilar = notifications.some(n => n.id === id);
    
    // console.log('[DEBUG] Has duplicate notification ID already:', hasSimilar);
    
    if (!hasSimilar) {
      // console.log('[DEBUG] Adding notification to state');
      setNotifications(prev => {
        const newState = [newNotification, ...prev];
        // console.log('[DEBUG] New notifications state length:', newState.length);
        
        // Immediately play notification sound to ensure it happens
        // console.log('[DEBUG] Playing notification sound for type:', notification.type);
        playNotificationSound(notification.type);
        
        return newState;
      });
    } else {
      // console.log('[DEBUG] Skipping duplicate notification');
    }
    
    // Always return the notification so we can chain operations if needed
    return newNotification;
  }, [notifications]);

  // Periodically clean up expired notifications
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      setNotifications(prev => {
        const filtered = filterExpiredNotifications(prev);
        
        // Only update if we actually removed some notifications
        if (filtered.length < prev.length) {
          // console.log(`[CLEANUP] Removed ${prev.length - filtered.length} expired notifications`);
          return filtered;
        }
        
        return prev;
      });
    }, 24 * 60 * 60 * 1000); // Run once daily
    
    return () => clearInterval(cleanupInterval);
  }, []);

  // Optimized data fetching with caching
  const getAppointmentsWithCaching = useCallback(async (forceFresh = false) => {
    const now = new Date();
    const cacheAgeMinutes = (now.getTime() - lastFetchTime.getTime()) / (1000 * 60);
    
    // If cache is valid (less than 5 minutes old) and not forced to refresh
    if (cachedAppointments.length > 0 && cacheAgeMinutes < 5 && !forceFresh) {
      // console.log('[DATA FETCH] Using cached appointments data, age: ', cacheAgeMinutes.toFixed(1), 'minutes');
      return cachedAppointments;
    }
    
    try {
      // console.log('[DATA FETCH] Fetching fresh appointments data');
      const freshAppointments = await getUpcomingAppointments();
      // Use type assertion here to ensure we're using the same Appointment type
      setCachedAppointments(freshAppointments as unknown as Appointment[]);
      setLastFetchTime(now);
      return freshAppointments;
    } catch (error) {
      console.error('[DATA FETCH] Error fetching appointments:', error);
      // If we have cached data, return it as fallback in case of error
      if (cachedAppointments.length > 0) {
        console.log('[DATA FETCH] Falling back to cached data due to fetch error');
        return cachedAppointments;
      }
      throw error; // Re-throw if we have no fallback
    }
  }, [getUpcomingAppointments, cachedAppointments, lastFetchTime]);

  // Sales data fetching with caching
  const getSalesWithCaching = useCallback(async (forceFresh = false) => {
    const now = new Date();
    const cacheAgeMinutes = (now.getTime() - lastSalesFetchTime.getTime()) / (1000 * 60);
    
    // If cache is valid (less than 5 minutes old) and not forced to refresh
    if (cachedSales.length > 0 && cacheAgeMinutes < 5 && !forceFresh) {
      // console.log('[DATA FETCH] Using cached sales data, age: ', cacheAgeMinutes.toFixed(1), 'minutes');
      return cachedSales;
    }
    
    try {
      // console.log('[DATA FETCH] Fetching fresh sales data');
      // Get all sales from last 2 years
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 2);
      const freshSales = await getSalesByDateRange(startDate, now);
      
      // Use type assertion to ensure we're using the same Sale type
      setCachedSales(freshSales as unknown as Sale[]);
      setLastSalesFetchTime(now);
      return freshSales;
    } catch (error) {
      console.error('[DATA FETCH] Error fetching sales:', error);
      // If we have cached data, return it as fallback in case of error
      if (cachedSales.length > 0) {
        // console.log('[DATA FETCH] Falling back to cached data due to fetch error');
        return cachedSales;
      }
      throw error; // Re-throw if we have no fallback
    }
  }, [getSalesByDateRange, cachedSales, lastSalesFetchTime]);

  // Refactored notification check functions
  const checkForNoShowNotifications = useCallback((
    appointments: Appointment[], 
    now: Date, 
    notifiedIds: Set<number>
  ) => {
    let newNotifiedIds = false;
    const updatedNotifiedIds = new Set(notifiedIds);
    
    // Find appointments that are marked as no_show but haven't been notified
    for (const appointment of appointments) {
      if (appointment.status === 'no_show' && !notifiedIds.has(appointment.id)) {
        const appointmentDate = new Date(appointment.appointment_date);
        const formattedDate = formatBrazilianDate(appointmentDate, true);
        
        // console.log(`[NO-SHOW CHECK] Found no_show appointment ${appointment.id} that hasn't received notification yet`);
        
        // Add notification for this appointment
        addNotification({
          title: 'Agendamento não comparecido',
          message: `O agendamento para ${appointment.pet?.name || 'um pet'} marcado para ${formattedDate} foi registrado como não comparecido. Expirará amanhã ao meio-dia.`,
          type: 'warning',
          relatedId: appointment.id
        });
        
        // Mark as processed
        updatedNotifiedIds.add(appointment.id);
        newNotifiedIds = true;
        
        // console.log(`[NO-SHOW CHECK] Sent notification for no_show appointment ${appointment.id}`);
      }
    }
    
    return { updatedIds: updatedNotifiedIds, hasChanges: newNotifiedIds };
  }, [addNotification]);

  const checkForExpirationWarnings = useCallback((
    appointments: Appointment[], 
    now: Date, 
    notifiedIds: Set<number>
  ) => {
    let newNotifiedIds = false;
    const updatedNotifiedIds = new Set(notifiedIds);
    
    // Check no_show appointments for approaching expiration
    for (const appointment of appointments) {
      if (appointment.status === 'no_show') {
        const appointmentDate = new Date(appointment.appointment_date);
        const formattedDate = formatBrazilianDate(appointmentDate, true);
        
        // Calculate expiration date (noon the next day)
        const expirationDate = new Date(appointmentDate);
        expirationDate.setDate(expirationDate.getDate() + 1);
        expirationDate.setHours(12, 0, 0, 0);
        
        // Calculate minutes until expiration
        const minutesTillExpiration = (expirationDate.getTime() - now.getTime()) / (1000 * 60);
        
        // For debugging
        if (minutesTillExpiration <= 60) {
          // console.log(`[EXPIRATION CHECK] No-show appointment ${appointment.id} expiration details:
          //   Appointment time: ${appointmentDate.toLocaleString()}
          //   Expiration time: ${expirationDate.toLocaleString()}
          //   Minutes until expiration: ${minutesTillExpiration.toFixed(1)}
          //   Already notified: ${notifiedIds.has(appointment.id)}
          // `);
        }
        
        // If it's 30-25 minutes before full expiration, send a notification (only once)
        if (minutesTillExpiration <= 30 && 
            minutesTillExpiration >= 25 && 
            !notifiedIds.has(appointment.id)) {
          
          // console.log(`[EXPIRATION CHECK] Sending expiration warning for appointment ${appointment.id}, expiring in ${minutesTillExpiration.toFixed(1)} minutes`);
          
          // Send notification about imminent expiration
          addNotification({
            title: 'Agendamento expirará em breve',
            message: `O agendamento para ${appointment.pet?.name || 'um pet'} marcado para ${formattedDate} expirará hoje ao meio-dia.`,
            type: 'warning',
            relatedId: appointment.id
          });
          
          // Record that we've notified for this appointment's expiration
          updatedNotifiedIds.add(appointment.id);
          newNotifiedIds = true;
          
          // console.log(`[EXPIRATION CHECK] Expiration notification sent for appointment ${appointment.id}`);
        }
        
        // If appointment has fully expired (past noon the next day)
        if (now >= expirationDate && !notifiedIds.has(appointment.id)) {
          // Add to notified set to prevent duplicate notifications
          updatedNotifiedIds.add(appointment.id);
          newNotifiedIds = true;
          
          // Notify about fully expired appointment
          addNotification({
            title: 'Agendamento completamente expirado',
            message: `O agendamento para ${appointment.pet?.name || 'um pet'} marcado para ${formattedDate} expirou completamente.`,
            type: 'error',
            relatedId: appointment.id
          });
          
          // console.log(`[EXPIRATION CHECK] Full expiration notification sent for appointment ${appointment.id}`);
        }
      }
    }
    
    return { updatedIds: updatedNotifiedIds, hasChanges: newNotifiedIds };
  }, [addNotification]);

  const checkForUpcomingAppointments = useCallback((
    appointments: Appointment[], 
    now: Date, 
    notifiedIds: Set<number>
  ) => {
    let newNotifiedIds = false;
    const updatedNotifiedIds = new Set(notifiedIds);
    
    // Check for upcoming appointments in the next 30 minutes
    for (const appointment of appointments) {
      if (appointment.status === 'scheduled') {
        const appointmentDate = new Date(appointment.appointment_date);
        const formattedDate = formatBrazilianDate(appointmentDate, true);
        
        const thirtyMinutesFromNow = new Date(now);
        thirtyMinutesFromNow.setMinutes(thirtyMinutesFromNow.getMinutes() + 30);
        
        // Check if appointment is starting soon
        const isUpcoming = appointmentDate <= thirtyMinutesFromNow && appointmentDate > now;
        
        if (isUpcoming && !notifiedIds.has(appointment.id)) {
          // Calculate minutes until appointment
          const minutesTillAppointment = Math.round((appointmentDate.getTime() - now.getTime()) / (1000 * 60));
          
          // console.log(`[UPCOMING CHECK] Upcoming appointment ${appointment.id} details:
          //   Appointment time: ${appointmentDate.toLocaleString()}
          //   Current time: ${now.toLocaleString()}
          //   Minutes until appointment: ${minutesTillAppointment}
          //   Already processed: ${notifiedIds.has(appointment.id)}
          // `);
          
          // Mark as processed to prevent duplicate notifications
          updatedNotifiedIds.add(appointment.id);
          newNotifiedIds = true;
          
          const timeMessage = minutesTillAppointment <= 1 
            ? 'agora' 
            : `em aproximadamente ${minutesTillAppointment} minutos`;
          
          // Notify about upcoming appointment
          addNotification({
            title: 'Agendamento em breve',
            message: `O agendamento para ${appointment.pet?.name || 'um pet'} marcado para ${formattedDate} começará ${timeMessage}.`,
            type: 'info',
            relatedId: appointment.id
          });
          
          // console.log(`[UPCOMING CHECK] Upcoming appointment notification sent for appointment ${appointment.id}`);
        }
      }
    }
    
    return { updatedIds: updatedNotifiedIds, hasChanges: newNotifiedIds };
  }, [addNotification]);

  // Check for pending sales that are overdue (30+ days old)
  const checkForPendingSalesNotifications = useCallback((
    sales: Sale[], 
    now: Date, 
    notifiedIds: Set<number>
  ) => {
    let newNotifiedIds = false;
    const updatedNotifiedIds = new Set(notifiedIds);
    
    // Get settings from localStorage
    let enabled = true; // Default to enabled
    let thresholdDays = 30; // Default to 30 days
    
    try {
      const savedSettings = localStorage.getItem('pendingSalesSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        enabled = settings.enabled;
        thresholdDays = settings.thresholdDays;
      }
    } catch (error) {
      console.error('[PENDING SALE CHECK] Error loading settings from localStorage:', error);
    }
    
    // Skip if notifications are disabled
    if (!enabled) {
      // console.log('[PENDING SALE CHECK] Pending sales notifications are disabled in settings');
      return { updatedIds: updatedNotifiedIds, hasChanges: false };
    }
    
    // console.log(`[PENDING SALE CHECK] Checking for pending sales older than ${thresholdDays} days`);
    
    // Find pending sales that haven't been notified and are older than the threshold
    for (const sale of sales) {
      if (sale.status === 'pending' && !notifiedIds.has(sale.id)) {
        // Parse the sale date
        const saleDate = new Date(sale.sale_date);
        
        // Calculate days since sale
        const daysSinceSale = Math.floor((now.getTime() - saleDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Check if sale is overdue based on our threshold
        if (daysSinceSale >= thresholdDays) {
          // console.log(`[PENDING SALE CHECK] Found pending sale ${sale.id} from ${saleDate.toLocaleDateString()} (${daysSinceSale} days ago)`);
          
          const formattedDate = formatBrazilianDate(saleDate, false);
          const customerName = sale.customer?.name || 'Cliente';
          
          // Add notification for this overdue pending sale
          addNotification({
            title: 'Venda pendente em atraso',
            message: `A venda #${sale.id} para ${customerName} feita em ${formattedDate} (${daysSinceSale} dias atrás) continua pendente.`,
            type: 'warning',
            relatedId: sale.id
          });
          
          // Mark as processed
          updatedNotifiedIds.add(sale.id);
          newNotifiedIds = true;
          
          // console.log(`[PENDING SALE CHECK] Sent notification for pending sale ${sale.id}`);
        }
      }
    }
    
    return { updatedIds: updatedNotifiedIds, hasChanges: newNotifiedIds };
  }, [addNotification]);

  // Improved main check function with error handling
  const runNotificationChecks = useCallback(async (forceFresh = false) => {
    let fetchError = null;
    let appointments: Appointment[] = [];
    let sales: Sale[] = [];
    
    try {
      // console.log('[NOTIFICATION CHECK] Starting notification check process...');
      
      try {
        // Fetch appointments
        appointments = await getAppointmentsWithCaching(forceFresh) as unknown as Appointment[];
        // console.log(`[NOTIFICATION CHECK] Retrieved ${appointments.length} appointments for notification check`);
        
        // Fetch sales
        sales = await getSalesWithCaching(forceFresh) as unknown as Sale[];
        // console.log(`[NOTIFICATION CHECK] Retrieved ${sales.length} sales for notification check`);
      } catch (error) {
        fetchError = error;
        console.error('[NOTIFICATION CHECK] Error fetching data:', error);
        // Add a system notification about the error
        addNotification({
          title: 'Erro ao verificar dados',
          message: 'Não foi possível atualizar os dados. Algumas notificações podem estar atrasadas.',
          type: 'error'
        });
        return; // Exit early if we can't get data
      }
      
      const now = new Date();
      
      // Check for different appointment notification types using the refactored functions
      const noShowResult = checkForNoShowNotifications(
        appointments, 
        now, 
        notifiedNoShowEvents
      );
      
      const expirationResult = checkForExpirationWarnings(
        appointments, 
        now,
        notifiedExpirationWarnings
      );
      
      const upcomingResult = checkForUpcomingAppointments(
        appointments,
        now,
        notifiedUpcomingReminders
      );
      
      // Check for pending sales notifications
      const pendingSalesResult = checkForPendingSalesNotifications(
        sales,
        now,
        notifiedPendingSales
      );
      
      // Update state if needed
      if (noShowResult.hasChanges) {
        setNotifiedNoShowEvents(noShowResult.updatedIds);
      }
      
      if (expirationResult.hasChanges) {
        setNotifiedExpirationWarnings(expirationResult.updatedIds);
      }
      
      if (upcomingResult.hasChanges) {
        setNotifiedUpcomingReminders(upcomingResult.updatedIds);
      }
      
      if (pendingSalesResult.hasChanges) {
        setNotifiedPendingSales(pendingSalesResult.updatedIds);
      }
      
      // console.log('[NOTIFICATION CHECK] Finished checking for notifications');
      
    } catch (error) {
      console.error('[NOTIFICATION CHECK] Unexpected error in notification check:', error);
      addNotification({
        title: 'Erro inesperado',
        message: 'Ocorreu um erro ao processar notificações.',
        type: 'error'
      });
    }
  }, [
    getAppointmentsWithCaching,
    getSalesWithCaching,
    addNotification,
    checkForNoShowNotifications,
    checkForExpirationWarnings,
    checkForUpcomingAppointments,
    checkForPendingSalesNotifications,
    notifiedNoShowEvents,
    notifiedExpirationWarnings,
    notifiedUpcomingReminders,
    notifiedPendingSales
  ]);

  // Check for appointments and sales that need notification
  useEffect(() => {
    // Run the check immediately when component mounts with fresh data
    runNotificationChecks(true);
    
    // Set interval to check every minute
    const intervalId = setInterval(() => runNotificationChecks(false), 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, [runNotificationChecks]);

  // Improved event handling for window focus and custom events
  useEffect(() => {
    // To prevent multiple handlers from creating duplicate notifications
    let isProcessingAutoMarkEvent = false;
    // Define the event handler for window focus
    const handleWindowFocus = () => {
      // console.log('[FOCUS] Window regained focus - checking appointments');
      // Force fresh data on window focus to catch any changes
      runNotificationChecks(true);
    };
    // Handler for manual status changes
    const handleStatusChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      // console.log('[STATUS CHANGE] Received appointment-status-changed event:', customEvent.detail);
      const { appointmentId, newStatus, manualChange } = customEvent.detail;
      if (newStatus === 'no_show' && manualChange) {
        // Mark as processed to prevent duplicate notifications from the interval check
        setNotifiedNoShowEvents(prev => {
          const newSet = new Set(prev);
          newSet.add(appointmentId);
          return newSet;
        });
      }
    };
    // Attach the event listeners
    window.addEventListener('focus', handleWindowFocus);
    window.addEventListener('appointment-status-changed', handleStatusChange);
    // Clean up when component unmounts
    return () => {
      window.removeEventListener('focus', handleWindowFocus);
      window.removeEventListener('appointment-status-changed', handleStatusChange);
    };
  }, [
    runNotificationChecks, 
    getAppointmentsWithCaching, 
    addNotification, 
    notifiedNoShowEvents
  ]);

  // Log initialization
  useEffect(() => {
    // console.log('[INIT] NotificationProvider initialized with notification persistence');
  }, []);

  // Memoize context values
  const dataContextValue = useMemo(() => ({
    notifications,
    unreadCount,
  }), [notifications, unreadCount]);

  const actionsContextValue = useMemo(() => ({
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
  }), [addNotification, markAsRead, markAllAsRead, removeNotification, clearAll]);

  return (
    <NotificationDataContext.Provider value={dataContextValue}>
      <NotificationActionsContext.Provider value={actionsContextValue}>
        {children}
      </NotificationActionsContext.Provider>
    </NotificationDataContext.Provider>
  );
};

// Custom hooks for accessing specific parts of the context
export const useNotificationData = () => {
  const context = useContext(NotificationDataContext);
  if (context === undefined) {
    throw new Error('useNotificationData must be used within a NotificationProvider');
  }
  return context;
};

export const useNotificationActions = () => {
  const context = useContext(NotificationActionsContext);
  if (context === undefined) {
    throw new Error('useNotificationActions must be used within a NotificationProvider');
  }
  return context;
};

// Selector hooks for specific pieces of data
export const useNotificationCount = () => {
  const { unreadCount } = useNotificationData();
  return unreadCount;
};

export const useNotificationList = () => {
  const { notifications } = useNotificationData();
  return notifications;
};

// Backwards compatibility hook
export const useNotifications = () => {
  const data = useNotificationData();
  const actions = useNotificationActions();
  return { ...data, ...actions };
}; 