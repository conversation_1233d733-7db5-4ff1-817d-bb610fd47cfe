import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import useTheme from '@mui/material/styles/useTheme';
import { 
  Add as AddIcon, 
  Search as SearchIcon,
  Event as EventIcon,
  ViewList as ViewListIcon,
  Today as TodayIcon,
  CalendarMonth as CalendarMonthIcon,
  ShoppingCart as ShoppingCartIcon
} from '@mui/icons-material';
import { AppointmentCard } from '../components/Appointments/AppointmentCard';
import { AppointmentFormDialog } from '../components/Appointments/AppointmentFormDialog';
import { AppointmentCalendar } from '../components/Appointments/AppointmentCalendar';
import { 
  Appointment, 
  AppointmentFormData, 
  AppointmentStatus,
  Customer,
  Pet,
  Service 
} from '../types/appointments';
import { useAppointments } from '../hooks/useAppointments';
import { useCustomers } from '../hooks/useCustomers';
import { usePets } from '../hooks/usePets';
import { useServices } from '../hooks/useServices';
import { usePackageCompletionNotification } from '../utils/packageNotifications';
import { useCustomerPackages } from '../hooks/useCustomerPackages';
// Import React Virtualized components
import {
  WindowScroller,
  AutoSizer,
  List,
  CellMeasurer,
  CellMeasurerCache
} from 'react-virtualized';

// Helper function to adapt database appointment model to frontend type
const adaptAppointment = (dbAppointment: any): Appointment => ({
  id: dbAppointment.id,
  customer_id: dbAppointment.customer?.id || 0,
  pet_id: dbAppointment.pet?.id || 0,
  service_id: dbAppointment.service?.id || 0,
  appointment_date: dbAppointment.appointment_date,
  status: dbAppointment.status,
  notes: dbAppointment.notes,
  created_at: dbAppointment.created_at,
  updated_at: dbAppointment.updated_at,
  customer: dbAppointment.customer ? {
    id: dbAppointment.customer.id,
    name: dbAppointment.customer.name
  } : undefined,
  pet: dbAppointment.pet ? {
    id: dbAppointment.pet.id,
    name: dbAppointment.pet.name,
    type: dbAppointment.pet.type,
    customer_id: dbAppointment.customer?.id || 0
  } : undefined,
  service: dbAppointment.service ? {
    id: dbAppointment.service.id,
    name: dbAppointment.service.name,
    duration_minutes: dbAppointment.service.duration_minutes,
    price: dbAppointment.service.price
  } : undefined,
  is_package_appointment: dbAppointment.is_package_appointment || false,
  source_customer_package_id: dbAppointment.source_customer_package_id || null
});

// Helper function to adapt database customer model to frontend type
const adaptCustomer = (dbCustomer: any): Customer => ({
  id: dbCustomer.id,
  name: dbCustomer.name
});

// Helper function to adapt database pet model to frontend type
const adaptPet = (dbPet: any): Pet => ({
  id: dbPet.id,
  name: dbPet.name,
  type: dbPet.type,
  customer_id: dbPet.customer?.id || 0
});

// Helper function to adapt database service model to frontend type
const adaptService = (dbService: any): Service => ({
  id: dbService.id,
  name: dbService.name,
  duration_minutes: dbService.duration_minutes,
  price: dbService.price
});

const Appointments: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  // Use hooks for real data
  const { 
    appointments: dbAppointments, 
    loading: appointmentsLoading, 
    error: appointmentsError,
    createAppointment,
    updateAppointment,
    updateAppointmentStatus: updateStatusInDb,
    deleteAppointment: deleteAppointmentFromDb,
    refetch: refetchAppointments
  } = useAppointments();
  
  const {
    customers: dbCustomers,
    loading: customersLoading,
    error: customersError
  } = useCustomers();

  const {
    pets: dbPets,
    loading: petsLoading,
    error: petsError
  } = usePets();

  const {
    services: dbServices,
    loading: servicesLoading,
    error: servicesError
  } = useServices();

  const { notifyPackageCompletion } = usePackageCompletionNotification();
  const { addUsageHistory } = useCustomerPackages();

  // Convert database models to frontend types
  const appointments = useMemo(() => dbAppointments ? dbAppointments.map(adaptAppointment) : [], [dbAppointments]);
  const customers = useMemo(() => dbCustomers ? dbCustomers.map(adaptCustomer) : [], [dbCustomers]);
  const pets = useMemo(() => dbPets ? dbPets.map(adaptPet) : [], [dbPets]);
  const services = useMemo(() => dbServices ? dbServices.map(adaptService) : [], [dbServices]);

  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');
  
  const [formOpen, setFormOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | undefined>(undefined);
  
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [appointmentToDelete, setAppointmentToDelete] = useState<number | null>(null);
  const [createSaleDialogOpen, setCreateSaleDialogOpen] = useState(false);
  const [completedAppointment, setCompletedAppointment] = useState<Appointment | null>(null);

  // Memoize sorted appointments for better performance
  const sortedAppointments = useMemo(() => {
    // Initial sort when appointments change
    return [...appointments].sort((a, b) => {
      const dateA = new Date(a.appointment_date).getTime();
      const dateB = new Date(b.appointment_date).getTime();
      return dateB - dateA; // Descending order (newest first)
    });
  }, [appointments]);

  // Memoize filtered appointments based on search term
  const filteredAppointments = useMemo(() => {
    if (!searchTerm.trim()) {
      return sortedAppointments; // Return already sorted list
    }
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    return sortedAppointments.filter(appointment => 
      (appointment.customer?.name && appointment.customer.name.toLowerCase().includes(lowerSearchTerm)) ||
      (appointment.pet?.name && appointment.pet.name.toLowerCase().includes(lowerSearchTerm)) ||
      (appointment.service?.name && appointment.service.name.toLowerCase().includes(lowerSearchTerm)) ||
      (appointment.notes && appointment.notes.toLowerCase().includes(lowerSearchTerm))
    );
    // No need to re-sort here as we started with sortedAppointments
  }, [searchTerm, sortedAppointments]);

  // CellMeasurerCache setup
  const cache = useMemo(() => new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 300, // Approximate height of an appointment card
    minHeight: 250,
  }), []);
  
  // Reset cache when filtered appointments change
  useEffect(() => {
    if (filteredAppointments.length > 0) {
      cache.clearAll();
    }
  }, [filteredAppointments, cache]);

  // Calculate number of columns based on screen width
  const getColumnCount = (width: number) => {
    if (width < 600) return 1; // Mobile view
    if (width < 960) return 2; // Tablet view
    return 3; // Desktop view - 3 columns
  };

  // Calculate row count based on items and columns
  const getRowCount = (itemCount: number, columnCount: number) => {
    return Math.ceil(itemCount / columnCount);
  };

  // Get item at index accounting for column layout
  const getItemAtIndex = (items: Appointment[], rowIndex: number, columnIndex: number, columnCount: number) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    return itemIndex < items.length ? items[itemIndex] : null;
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleViewModeChange = (_event: React.SyntheticEvent, newValue: 'list' | 'calendar') => {
    setViewMode(newValue);
  };

  const handleAddAppointment = useCallback(() => {
    setSelectedAppointment(undefined);
    setFormOpen(true);
  }, []);

  const handleEditAppointment = useCallback((appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setFormOpen(true);
  }, []);

  const handleSaveAppointment = useCallback(async (appointmentData: AppointmentFormData) => {
    try {
      if (selectedAppointment) {
        // Update existing appointment
        await updateAppointment(selectedAppointment.id, {
          customer_id: appointmentData.customer_id,
          pet_id: appointmentData.pet_id,
          service_id: appointmentData.service_id,
          appointment_date: appointmentData.appointment_date,
          status: appointmentData.status,
          notes: appointmentData.notes || null
        } as any);
      } else {
        // Add new appointment
        await createAppointment({
          customer_id: appointmentData.customer_id,
          pet_id: appointmentData.pet_id,
          service_id: appointmentData.service_id,
          appointment_date: appointmentData.appointment_date,
          status: appointmentData.status,
          notes: appointmentData.notes || null
        } as any);
      }
      
      // Refresh the appointment list
      await refetchAppointments();
      setFormOpen(false);
    } catch (error) {
      console.error('Error saving appointment:', error);
    }
  }, [selectedAppointment, createAppointment, updateAppointment, refetchAppointments]);

  const handleDeleteAppointment = useCallback((appointmentId: number) => {
    setAppointmentToDelete(appointmentId);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeleteAppointment = useCallback(async () => {
    if (appointmentToDelete) {
      try {
        await deleteAppointmentFromDb(appointmentToDelete);
        await refetchAppointments();
        setDeleteDialogOpen(false);
        setAppointmentToDelete(null);
      } catch (error) {
        console.error('Error deleting appointment:', error);
      }
    }
  }, [appointmentToDelete, deleteAppointmentFromDb, refetchAppointments]);

  const handleStatusChange = useCallback(async (appointmentId: number, newStatus: AppointmentStatus) => {
    try {
      // First update the appointment status in the database
      await updateStatusInDb(appointmentId, newStatus);
      // If changed to completed (concluído), prompt to create a sale
      if (newStatus === 'completed') {
        // Find the appointment with this ID to get service and customer info
        const appointment = appointments.find(a => a.id === appointmentId);
        if (appointment && appointment.service) {
          // Log usage if this is a package appointment
          if (appointment.is_package_appointment && appointment.source_customer_package_id) {
            await addUsageHistory(
              appointment.source_customer_package_id,
              appointment.id,
              appointment.appointment_date
            );
          }
          // Notify about package completion/next schedule
          notifyPackageCompletion(appointment);
          // Only show the sale dialog if NOT a package appointment
          if (!appointment.is_package_appointment) {
          setCompletedAppointment(appointment);
          setCreateSaleDialogOpen(true);
          }
        }
      }
      // If changed to no_show, trigger a notification check
      else if (newStatus === 'no_show') {
        // console.log(`Status changed to no_show for appointment ${appointmentId}, triggering notification check`);
        
        // Create a custom event with additional data to signal this was a manual change
        const event = new CustomEvent('appointment-status-changed', {
          detail: {
            appointmentId,
            newStatus,
            manualChange: true
          }
        });
        window.dispatchEvent(event);
        
        // Also trigger regular focus event as fallback
        window.dispatchEvent(new Event('focus'));
      }
      
      await refetchAppointments();
    } catch (error) {
      console.error('Error updating appointment status:', error);
    }
  }, [appointments, updateStatusInDb, refetchAppointments, notifyPackageCompletion, addUsageHistory]);

  // Handle sale creation after completion
  const handleCreateSale = useCallback(() => {
    if (completedAppointment && completedAppointment.service) {
      // Navigate to sales page with appointment data
      navigate('/sales', { 
        state: { 
          createFromAppointment: true,
          appointmentData: {
            customerId: completedAppointment.customer_id,
            serviceId: completedAppointment.service_id,
            serviceName: completedAppointment.service.name,
            servicePrice: completedAppointment.service.price,
            petId: completedAppointment.pet_id
          }
        } 
      });
    }
    setCreateSaleDialogOpen(false);
    setCompletedAppointment(null);
  }, [completedAppointment, navigate]);

  const handleCancelCreateSale = useCallback(() => {
    setCreateSaleDialogOpen(false);
    setCompletedAppointment(null);
  }, []);

  // Handle loading states
  const loading = appointmentsLoading || customersLoading || petsLoading || servicesLoading;
  const error = appointmentsError || customersError || petsError || servicesError;

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar dados: {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CalendarMonthIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 'bold', 
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 4,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 2,
              }
            }}
          >
            Gerenciamento de Consultas
          </Typography>
        </Box>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
          onClick={handleAddAppointment}
        >
          Adicionar Consulta
        </Button>
      </Box>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Buscar por cliente, pet, serviço ou anotações"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={viewMode}
          onChange={handleViewModeChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab 
            label="Visualização em Lista" 
            value="list" 
            icon={<ViewListIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Visualização em Calendário" 
            value="calendar" 
            icon={<EventIcon />} 
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      {viewMode === 'list' ? (
        <>
          {filteredAppointments.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Typography variant="h6" color="text.secondary">
                Nenhuma consulta encontrada. Adicione uma nova consulta para começar.
              </Typography>
            </Box>
          ) : (
            <Box sx={{ height: 'calc(100vh - 280px)', width: '100%' }}>
              <WindowScroller>
                {({ height, isScrolling, onChildScroll, scrollTop }) => (
                  <AutoSizer disableHeight>
                    {({ width }) => {
                      const columnCount = getColumnCount(width);
                      const rowCount = getRowCount(filteredAppointments.length, columnCount);
                      
                      return (
                        <List
                          autoHeight
                          height={height || 500}
                          isScrolling={isScrolling}
                          onScroll={onChildScroll}
                          rowCount={rowCount}
                          rowHeight={cache.rowHeight}
                          scrollTop={scrollTop}
                          width={width}
                          overscanRowCount={3}
                          deferredMeasurementCache={cache}
                          style={{
                            outline: 'none',
                            paddingBottom: '20px',
                            overflowX: 'hidden'
                          }}
                          rowRenderer={({ index, key, style, parent }) => {
                            return (
                              <CellMeasurer
                                cache={cache}
                                columnIndex={0}
                                key={key}
                                parent={parent}
                                rowIndex={index}
                              >
                                {({ measure }) => (
                                  <div 
                                    style={{ 
                                      ...style, 
                                      padding: '8px',
                                      boxSizing: 'border-box',
                                      display: 'flex'
                                    }}
                                  >
                                    {/* Create a row with potentially multiple columns */}
                                    {Array.from({ length: columnCount }).map((_, colIndex) => {
                                      const appointment = getItemAtIndex(filteredAppointments, index, colIndex, columnCount);
                                      if (!appointment) return <div key={colIndex} style={{ flex: 1 }} />;
                                      
                                      return (
                                        <div 
                                          key={colIndex} 
                                          style={{ 
                                            flex: 1, 
                                            padding: '0 8px',
                                            boxSizing: 'border-box'
                                          }}
                                        >
                                          <AppointmentCard 
                                            appointment={appointment}
                                            onEdit={() => handleEditAppointment(appointment)}
                                            onDelete={() => handleDeleteAppointment(appointment.id)}
                                            onStatusChange={handleStatusChange}
                                          />
                                        </div>
                                      );
                                    })}
                                  </div>
                                )}
                              </CellMeasurer>
                            );
                          }}
                        />
                      );
                    }}
                  </AutoSizer>
                )}
              </WindowScroller>
            </Box>
          )}
        </>
      ) : (
        <AppointmentCalendar 
          appointments={filteredAppointments}
          onAppointmentClick={handleEditAppointment}
        />
      )}

      {/* Appointment form dialog */}
      <AppointmentFormDialog 
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSave={handleSaveAppointment}
        appointment={selectedAppointment}
        title={selectedAppointment ? 'Editar Consulta' : 'Adicionar Nova Consulta'}
        customers={customers}
        pets={pets}
        services={services}
      />

      {/* Confirm delete dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Tem certeza que deseja excluir esta consulta? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancelar</Button>
          <Button onClick={confirmDeleteAppointment} color="error">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create sale dialog */}
      <Dialog
        open={createSaleDialogOpen}
        onClose={handleCancelCreateSale}
      >
        <DialogTitle>Criar Venda</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Deseja criar uma venda para esta consulta?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateSale} color="primary">
            Sim
          </Button>
          <Button onClick={handleCancelCreateSale} color="error">
            Não
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Appointments; 