import { contextBridge, ipc<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron';

type IpcCallback = (...args: unknown[]) => void;

// Whitelist of valid IPC channels
const validInvokeChannels = [
  // Backup de banco de dados
  'db:exportDatabase',
  'db:importDatabase',
  // Auth channels (Only PIN related remain)
  'auth:setPinHash',
  'auth:verifyPin',
  'auth:isPinConfigured',

  // Customer channels
  'customers:getAll',
  'customers:getById',
  'customers:search',
  'customers:create',
  'customers:update',
  'customers:delete',
  'customers:getInactive',
  'customers:reactivate',
  'customerPackages:getByPurchaseDateRange',

  // Pet channels
  'pets:getAll',
  'pets:getById',
  'pets:getByCustomerId',
  'pets:getByCustomerIdIncludingHidden',
  'pets:create',
  'pets:update',
  'pets:delete',
  'pets:reactivate',

  // Pet Services channels
  'petServices:getAll',
  'petServices:getById',
  'petServices:getByPetId',
  'petServices:create',
  'petServices:update',
  'petServices:delete',

  // Product channels
  'products:getAll',
  'products:getById',
  'products:getByCategory',
  'products:getLowStock',
  'products:create',
  'products:update',
  'products:updateStock',
  'products:delete',

  // Service channels
  'services:getAll',
  'services:getById',
  'services:create',
  'services:update',
  'services:delete',

  // Sale channels
  'sales:getPendingByCustomer',
  'sales:getAll',
  'sales:getById',
  'sales:getByCustomerId',
  'sales:getByDateRange',
  'sales:getSaleItems',
  'sales:create',
  'sales:delete',
  'sales:updateStatus',

  // Appointment channels
  'appointments:getAll',
  'appointments:getById',
  'appointments:getByCustomerId',
  'appointments:getByPetId',
  'appointments:getUpcoming',
  'appointments:getByDateRange',
  'appointments:create',
  'appointments:update',
  'appointments:updateStatus',
  'appointments:delete',

  // Package channels
  'packages:getAll',
  'packages:getById',
  'packages:create',
  'packages:update',
  'packages:delete',
  'packages:getAllActive',

  // Customer Package channels
  'customerPackages:assign',
  'customerPackages:activate',
  'customerPackages:updatePrice',
  'customerPackages:markAsPaid',
  'customerPackages:getPaidByDateRange',
  'customerPackages:migratePaymentStatus',
  'customerPackages:getForCustomer',
  'customerPackages:getUpcomingAppointments',
  'customerPackages:getOnHold',
  'customerPackages:addUsageHistory',
  'customerPackages:getUsageHistory',
  'customerPackages:resolveNoShowReschedule',
  'customerPackages:resolveNoShowCancel',
  'customerPackages:cancelByUser',
  'customerPackages:getAllActiveIds',

  // Package Usage History channels
  'packageUsageHistory:getByPackageId',

  // Database status channel
  'get-database-status'
];

const validOnChannels = [
  'database-ready'
];

const validSendChannels = [
  'main-window-ready'
];

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'electron',
  {
    invoke: async (channel: string, ...args: unknown[]) => {
      if (validInvokeChannels.includes(channel)) {
        try {
          return await ipcRenderer.invoke(channel, ...args);
        } catch (error) {
          console.error(`Error in IPC channel ${channel}:`, error);
          throw error;
        }
      }

      throw new Error(`Unauthorized IPC channel: ${channel}`);
    },

    on: (channel: string, callback: IpcCallback) => {
      if (validOnChannels.includes(channel)) {
        ipcRenderer.on(channel, (_event: IpcRendererEvent, ...args: unknown[]) => callback(...args));
      }
    },

    removeListener: (channel: string, callback: IpcCallback) => {
      if (validOnChannels.includes(channel)) {
        ipcRenderer.removeListener(channel, callback);
      }
    },

    removeAllListeners: (channel: string) => {
      if (validOnChannels.includes(channel)) {
        ipcRenderer.removeAllListeners(channel);
      }
    },

    // Helper methods for database status
    getDatabaseStatus: () => ipcRenderer.invoke('get-database-status'),
    onDatabaseReady: (callback: IpcCallback) => ipcRenderer.on('database-ready', (_event: IpcRendererEvent, ...args: unknown[]) => callback(...args)),

    // Method to notify when main window UI is ready
    sendMainWindowReady: () => {
      if (validSendChannels.includes('main-window-ready')) {
        ipcRenderer.send('main-window-ready');
      }
    }
  }
);

// For backward compatibility
contextBridge.exposeInMainWorld(
  'electronAPI',
  {
    invoke: async (channel: string, ...args: unknown[]) => {
      if (validInvokeChannels.includes(channel)) {
        try {
          return await ipcRenderer.invoke(channel, ...args);
        } catch (error) {
          console.error(`Error in IPC channel ${channel}:`, error);
          throw error;
        }
      }

      throw new Error(`Unauthorized IPC channel: ${channel}`);
    },

    on: (channel: string, callback: IpcCallback) => {
      if (validOnChannels.includes(channel)) {
        ipcRenderer.on(channel, (_event: IpcRendererEvent, ...args: unknown[]) => callback(...args));
      }
    },

    removeListener: (channel: string, callback: IpcCallback) => {
      if (validOnChannels.includes(channel)) {
        ipcRenderer.removeListener(channel, callback);
      }
    }
  }
);