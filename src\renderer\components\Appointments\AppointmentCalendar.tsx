import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Grid from '@mui/material/Grid';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { Appointment, AppointmentStatusColors } from '../../types/appointments';
import { formatBrazilianDate, toBrazilianTimezone, BRAZIL_TIMEZONE } from '../../utils/dateUtils';

interface AppointmentCalendarProps {
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
}

interface DayProps {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
}

const Day: React.FC<DayProps> = ({ date, isCurrentMonth, isToday, appointments, onAppointmentClick }) => {
  const dayAppointments = appointments.filter(appointment => {
    const appointmentDate = toBrazilianTimezone(appointment.appointment_date);
    return (
      appointmentDate.getDate() === date.getDate() &&
      appointmentDate.getMonth() === date.getMonth() &&
      appointmentDate.getFullYear() === date.getFullYear()
    );
  });

  const formatTime = (dateString: string) => {
    const date = toBrazilianTimezone(dateString);
    return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit', timeZone: BRAZIL_TIMEZONE });
  };

  // Check if appointment is fully expired (past noon the day after)
  const isFullyExpired = (appointment: Appointment) => {
    const now = new Date();
    const appointmentDate = toBrazilianTimezone(appointment.appointment_date);
    
    // Calculate the expiration time (noon the next day)
    const expirationDate = new Date(appointmentDate);
    expirationDate.setDate(expirationDate.getDate() + 1);
    expirationDate.setHours(12, 0, 0, 0);
    
    // Only show red outline if appointment is no_show AND past expiration time
    return appointment.status === 'no_show' && now >= expirationDate;
  };
  
  // Check if appointment is "no_show" but not fully expired yet
  const isPendingExpiration = (appointment: Appointment) => {
    const now = new Date();
    const appointmentDate = toBrazilianTimezone(appointment.appointment_date);
    
    // Calculate the expiration time (noon the next day)
    const expirationDate = new Date(appointmentDate);
    expirationDate.setDate(expirationDate.getDate() + 1);
    expirationDate.setHours(12, 0, 0, 0);
    
    // Show orange status if appointment is no_show but not yet past expiration time
    return appointment.status === 'no_show' && now < expirationDate;
  };

  return (
    <Box
      sx={{
        height: '100%',
        width: '100%',
        border: '1px solid #e0e0e0',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: isToday ? 'rgba(63, 81, 181, 0.08)' : '#fff',
        position: 'relative',
        opacity: isCurrentMonth ? 1 : 0.5,
      }}
    >
      <Box 
        sx={{ 
          p: 1, 
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <Typography
          variant="body1"
          sx={{
            fontWeight: isToday ? 'bold' : 'normal',
            color: isToday ? '#3f51b5' : 'inherit',
          }}
        >
          {date.getDate()}
        </Typography>
      </Box>
      
      <Box 
        sx={{ 
          p: 1, 
          flexGrow: 1, 
          display: 'flex',
          flexDirection: 'column',
          justifyContent: dayAppointments.length ? 'flex-start' : 'center',
          alignItems: dayAppointments.length ? 'stretch' : 'center',
          maxHeight: '120px',
          overflowY: 'auto',
          willChange: 'transform',
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#bdbdbd',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#9e9e9e',
          },
        }}
      >
        {dayAppointments.length > 0 ? (
          dayAppointments.map((appointment) => {
            const fullyExpired = isFullyExpired(appointment);
            const pendingExpiration = isPendingExpiration(appointment);
            const hasNoShowStatus = appointment.status === 'no_show';
            
            const backgroundColor = hasNoShowStatus 
              ? '#f5f5f5' 
              : `${AppointmentStatusColors[appointment.status]}22`;
            
            const borderColor = fullyExpired
              ? '#f44336'  // Red for fully expired
              : pendingExpiration
                ? '#ff9800'  // Orange for pending expiration
                : hasNoShowStatus 
                  ? '#9e9e9e' 
                  : AppointmentStatusColors[appointment.status];
            
            return (
              <Box
                key={appointment.id}
                sx={{
                  p: 0.5,
                  mb: 0.5,
                  backgroundColor,
                  border: `1px solid ${borderColor}`,
                  borderLeft: `3px solid ${borderColor}`,
                  cursor: 'pointer',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  transform: 'translateZ(0)',
                }}
                onClick={() => onAppointmentClick(appointment)}
              >
                {hasNoShowStatus && (
                  <Box 
                    sx={{ 
                      position: 'absolute', 
                      top: 0, 
                      left: 0, 
                      width: '100%', 
                      height: '100%',
                      backgroundColor: 'rgba(0, 0, 0, 0.05)',
                      zIndex: 1
                    }} 
                  />
                )}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', zIndex: 2 }}>
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      fontWeight: 'bold',
                      textDecoration: hasNoShowStatus ? 'line-through' : 'none'
                    }}
                  >
                    {formatTime(appointment.appointment_date)}
                  </Typography>
                  {appointment.is_package_appointment && (
                    <InventoryIcon sx={{ fontSize: '0.8rem', color: 'text.secondary', ml: 0.5 }} />
                  )}
                  <Box 
                    sx={{
                      width: 10, 
                      height: 10, 
                      bgcolor: borderColor,
                      borderRadius: '50%',
                      ml: appointment.is_package_appointment ? 0.2 : 0.5,
                    }}
                  />
                </Box>
                <Typography 
                  variant="caption" 
                  noWrap
                  sx={{ 
                    zIndex: 2,
                    textDecoration: hasNoShowStatus ? 'line-through' : 'none',
                    color: hasNoShowStatus ? 'text.secondary' : 'text.primary'
                  }}
                >
                  {appointment.pet?.name || 'Pet Desconhecido'}
                </Typography>
              </Box>
            );
          })
        ) : (
          <Typography 
            variant="body2" 
            color="text.secondary"
            align="center"
          >
            Sem consultas
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export const AppointmentCalendar: React.FC<AppointmentCalendarProps> = ({ appointments, onAppointmentClick }) => {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };
  
  const getPreviousMonthDays = (year: number, month: number): Date[] => {
    const firstDay = getFirstDayOfMonth(year, month);
    const days: Date[] = [];
    
    if (firstDay > 0) {
      const daysInPreviousMonth = getDaysInMonth(year, month - 1);
      for (let i = firstDay - 1; i >= 0; i--) {
        days.push(new Date(year, month - 1, daysInPreviousMonth - i));
      }
    }
    
    return days;
  };
  
  const getCurrentMonthDays = (year: number, month: number): Date[] => {
    const daysInMonth = getDaysInMonth(year, month);
    const days: Date[] = [];
    
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i));
    }
    
    return days;
  };
  
  const getNextMonthDays = (year: number, month: number): Date[] => {
    const firstDay = getFirstDayOfMonth(year, month);
    const daysInMonth = getDaysInMonth(year, month);
    const days: Date[] = [];
    
    const remainingCells = 42 - (firstDay + daysInMonth);
    
    for (let i = 1; i <= remainingCells; i++) {
      days.push(new Date(year, month + 1, i));
    }
    
    return days;
  };
  
  const handlePreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };
  
  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };
  
  const handleToday = () => {
    setCurrentDate(new Date());
  };
  
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  
  const previousMonthDays = getPreviousMonthDays(year, month);
  const currentMonthDays = getCurrentMonthDays(year, month);
  const nextMonthDays = getNextMonthDays(year, month);
  
  const allDays = [...previousMonthDays, ...currentMonthDays, ...nextMonthDays];
  
  const today = new Date();
  
  const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
  
  return (
    <Paper sx={{ p: 0, boxShadow: 1, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Calendar Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        p: 2,
        borderBottom: '1px solid #e0e0e0'
      }}>
        <IconButton onClick={handlePreviousMonth} size="small">
          <ChevronLeftIcon />
        </IconButton>
        
        <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
          {currentDate.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleNextMonth} size="small">
            <ChevronRightIcon />
          </IconButton>
          <Button 
            variant="outlined" 
            size="small" 
            onClick={handleToday} 
            sx={{ 
              ml: 1, 
              borderRadius: 2,
              textTransform: 'uppercase',
              fontSize: '0.75rem',
              px: 2
            }}
          >
            Hoje
          </Button>
        </Box>
      </Box>
      
      {/* Calendar Grid */}
      <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
        {/* Weekday Headers */}
        <Box 
          sx={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(7, 1fr)', 
            borderBottom: '1px solid #e0e0e0'
          }}
        >
          {weekDays.map((day) => (
            <Box key={day} sx={{ textAlign: 'center', py: 1 }}>
              <Typography
                variant="subtitle2"
                sx={{ 
                  fontWeight: 'bold',
                  color: day === 'Dom' || day === 'Sáb' ? 'text.secondary' : 'text.primary'
                }}
              >
                {day}
              </Typography>
            </Box>
          ))}
        </Box>
        
        {/* Calendar Days */}
        <Box 
          sx={{ 
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            gridTemplateRows: 'repeat(6, 1fr)',
            flexGrow: 1
          }}
        >
          {allDays.map((date, index) => (
            <Box 
              key={index} 
              sx={{ 
                height: 0,
                paddingBottom: '100%',
                position: 'relative',
              }}
            >
              <Box sx={{ position: 'absolute', top: 0, right: 0, bottom: 0, left: 0 }}>
                <Day
                  date={date}
                  isCurrentMonth={date.getMonth() === month}
                  isToday={
                    date.getDate() === today.getDate() &&
                    date.getMonth() === today.getMonth() &&
                    date.getFullYear() === today.getFullYear()
                  }
                  appointments={appointments}
                  onAppointmentClick={onAppointmentClick}
                />
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Paper>
  );
}; 