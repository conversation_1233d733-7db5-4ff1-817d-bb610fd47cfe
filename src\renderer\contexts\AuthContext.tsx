import React, { createContext, useContext, useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import LockScreen from '../components/LockScreen';

interface User {
  id: string;
  username: string;
  email?: string;
  role: string;
}

interface AuthContextType {
  user: User;
  isAuthenticated: boolean;
  setPin: (pin: string) => Promise<{success: boolean, error?: string}>;
  needsPinSetup: boolean;
  isPinConfigured: boolean;
  checkPinConfigured: () => Promise<void>;
  isCheckingPinConfig: boolean;
}

const INACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user] = useState<User>({
    id: 'default-user',
    username: 'admin',
    email: 'admin@localhost',
    role: 'admin',
  });

  const [isLocked, setIsLocked] = useState(false);
  const [isPinConfigured, setIsPinConfigured] = useState<boolean>(false);
  const [isCheckingPinConfig, setIsCheckingPinConfig] = useState<boolean>(false);

  const inactivityTimer = useRef<NodeJS.Timeout>();

  const checkPinConfigured = useCallback(async () => {
    setIsCheckingPinConfig(true);
    if (window.electronAPI) {
      try {
        const response = await window.electronAPI.invoke('auth:isPinConfigured');
        if (response.success) {
          setIsPinConfigured(response.isConfigured);
        } else {
          console.error("Failed to check PIN configuration:", response.error);
          setIsPinConfigured(false);
        }
      } catch (error) {
        console.error("Error invoking auth:isPinConfigured:", error);
        setIsPinConfigured(false);
      } finally {
        setIsCheckingPinConfig(false);
      }
    } else {
      setIsPinConfigured(false);
      setIsCheckingPinConfig(false);
    }
  }, []);

  useEffect(() => {
    checkPinConfigured();
  }, [checkPinConfigured]);

  const resetInactivityTimer = useCallback(() => {
    if (inactivityTimer.current) {
      clearTimeout(inactivityTimer.current);
    }
    if (isPinConfigured && !isCheckingPinConfig) {
      inactivityTimer.current = setTimeout(() => {
        setIsLocked(true);
      }, INACTIVITY_TIMEOUT);
    }
  }, [isPinConfigured, isCheckingPinConfig]);

  useEffect(() => {
    const activityEvents = ['mousedown', 'keydown', 'touchstart', 'mousemove'];
    const handleActivity = () => {
      resetInactivityTimer();
    };

    if (isPinConfigured && !isCheckingPinConfig) {
      activityEvents.forEach(event => {
        window.addEventListener(event, handleActivity);
      });
      resetInactivityTimer();
    }

    return () => {
      if (inactivityTimer.current) {
        clearTimeout(inactivityTimer.current);
      }
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleActivity);
      });
    };
  }, [isPinConfigured, isCheckingPinConfig, resetInactivityTimer]);

  const setPin = async (pin: string): Promise<{success: boolean, error?: string}> => {
    if (window.electronAPI) {
      try {
        const response = await window.electronAPI.invoke('auth:setPinHash', pin);
        if (response.success) {
          await checkPinConfigured();
          resetInactivityTimer();
          return { success: true };
        }
        return { success: false, error: response.error || 'Failed to set PIN' };
      } catch (error) {
        console.error("Error invoking auth:setPinHash:", error);
        return { success: false, error: (error as Error).message };
      }
    }
    return { success: false, error: 'IPC not available' };
  };

  const handleUnlock = async (pin: string): Promise<boolean> => {
    if (window.electronAPI) {
      try {
        const response = await window.electronAPI.invoke('auth:verifyPin', pin);
        if (response.success && response.matches) {
          setIsLocked(false);
          resetInactivityTimer();
          return true;
        }
        return false;
      } catch (error) {
        console.error("Error invoking auth:verifyPin:", error);
        return false;
      }
    }
    return false;
  };

  const value = useMemo(() => ({
    user,
    isAuthenticated: true,
    setPin,
    needsPinSetup: !isPinConfigured && !isCheckingPinConfig,
    isPinConfigured,
    checkPinConfigured,
    isCheckingPinConfig,
  }), [user, isPinConfigured, isCheckingPinConfig, checkPinConfigured, setPin, setIsLocked, resetInactivityTimer]);

  return (
    <AuthContext.Provider value={value}>
      {isLocked ? <LockScreen isLocked={isLocked} onUnlock={handleUnlock} /> : children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 