import { AppDataSource } from '../connection';
import { Package, FrequencyType } from '../models/Package';
import { Service } from '../models/Service';
import { Repository } from 'typeorm';

export class PackageService {
  private repository: Repository<Package>;

  constructor() {
    this.repository = AppDataSource.getRepository(Package);
  }

  async findAll(): Promise<Package[]> {
    return this.repository.find({
      relations: ['service'],
      order: { name: 'ASC' }
    });
  }

  async findById(id: number): Promise<Package | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['service']
    });
  }

  async create(pkgData: Partial<Package>): Promise<Package> {
    const pkg = this.repository.create(pkgData);
    return this.repository.save(pkg);
  }

  async update(id: number, pkgData: Partial<Package>): Promise<Package | null> {
    await this.repository.update(id, pkgData);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.repository.update(id, { is_active: false, updated_at: new Date() });
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }

  async getAllActivePackages(): Promise<Package[]> {
    return this.repository.find({
      where: { is_active: true },
      relations: ['service'],
      order: { name: 'ASC' }
    });
  }
} 