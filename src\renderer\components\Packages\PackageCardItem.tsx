import React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Divider from '@mui/material/Divider';
import Chip from '@mui/material/Chip';
import { useTheme } from '@mui/material/styles';
import {
  CheckCircle as CheckCircleIcon,
  LocalOffer as LocalOfferIcon
} from '@mui/icons-material';
import { Package } from '../../types/packages';

interface PackageCardItemProps {
  pkg: Package;
  isSelected: boolean;
  regularPrice: number;
  savings: { amount: number; percentage: number };
  serviceNames: string[];
  onClick: (id: number) => void;
}

const PackageCardItem: React.FC<PackageCardItemProps> = React.memo(({
  pkg,
  isSelected,
  regularPrice,
  savings,
  serviceNames,
  onClick
}) => {
  const theme = useTheme();
  
  return (
    <Card 
      variant="outlined" 
      sx={{ 
        cursor: 'pointer', 
        borderColor: isSelected ? 'primary.main' : 'divider',
        borderWidth: isSelected ? 2 : 1,
        transition: 'all 0.2s',
        position: 'relative',
        '&:hover': {
          boxShadow: 3,
          transform: 'translateY(-4px)'
        }
      }}
      onClick={() => onClick(pkg.id)}
    >
      {isSelected && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: 0,
            height: 0,
            borderStyle: 'solid',
            borderWidth: '0 40px 40px 0',
            borderColor: `transparent ${theme.palette.primary.main} transparent transparent`,
            zIndex: 1
          }}
        />
      )}
      
      {isSelected && (
        <CheckCircleIcon
          sx={{
            position: 'absolute',
            top: 5,
            right: 5,
            color: 'white',
            zIndex: 2,
            fontSize: 16
          }}
        />
      )}
      
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography variant="h6" color="primary" gutterBottom>
            {pkg.name}
          </Typography>
          <Chip 
            label={`${savings.percentage.toFixed(0)}% OFF`} 
            color="success" 
            size="small"
            icon={<LocalOfferIcon />}
          />
        </Box>
        
        {pkg.description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {pkg.description}
          </Typography>
        )}
        
        <Divider sx={{ my: 1 }} />
        
        <Grid container spacing={1} sx={{ my: 1 }}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Quantidade:
            </Typography>
            <Typography variant="body1" fontWeight="bold">
              {pkg.total_occurrences} serviços
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Validade:
            </Typography>
            <Typography variant="body1" fontWeight="bold">
              {'Sem validade'}
            </Typography>
          </Grid>
        </Grid>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 0.5 }}>
          Serviços Incluídos:
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {serviceNames.map((name, index) => (
            <Chip 
              key={index} 
              label={name} 
              size="small" 
              variant="outlined" 
            />
          ))}
        </Box>
        
        <Box 
          sx={{ 
            mt: 2, 
            p: 1, 
            bgcolor: 'background.default', 
            borderRadius: 1 
          }}
        >
          <Grid container>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Preço Regular:
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through' }}>
                R$ {regularPrice.toFixed(2)}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Preço do Pacote:
              </Typography>
              <Typography variant="h6" color="primary" fontWeight="bold">
                R$ {pkg.price.toFixed(2)}
              </Typography>
            </Grid>
          </Grid>
          <Typography variant="body2" color="success.main" fontWeight="medium" sx={{ mt: 1 }}>
            Economia de R$ {savings.amount.toFixed(2)}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
});

export default PackageCardItem; 