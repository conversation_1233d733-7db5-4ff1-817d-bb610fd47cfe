import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsDeletedToProducts1723000000000 implements MigrationInterface {
    name = 'AddIsDeletedToProducts1723000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the column already exists
        const table = await queryRunner.getTable("products");
        const isDeletedColumn = table?.findColumnByName("is_deleted");
        
        if (!isDeletedColumn) {
            await queryRunner.query(`ALTER TABLE "products" ADD COLUMN "is_deleted" BOOLEAN NOT NULL DEFAULT 0`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("products");
        const isDeletedColumn = table?.findColumnByName("is_deleted");
        
        if (isDeletedColumn) {
            await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "is_deleted"`);
        }
    }
} 