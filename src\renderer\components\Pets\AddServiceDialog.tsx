import React, { useState, useCallback } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Grid from '@mui/material/Grid';
import FormHelperText from '@mui/material/FormHelperText';
import { SelectChangeEvent } from '@mui/material/Select';

interface ServiceFormData {
  service_name: string;
  service_date: string;
  service_time: string;
  notes: string;
}

interface AddServiceDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (petId: number, serviceData: ServiceFormData) => void;
  petId: number;
  petName: string;
  availableServices: { id: number; name: string }[];
}

export const AddServiceDialog = React.memo(({
  open,
  onClose,
  onSave,
  petId,
  petName,
  availableServices
}: AddServiceDialogProps) => {
  // Obter a data e hora atuais no Brasil (Brasília, UTC-3)
  const getFormattedBrazilianDateTime = () => {
    // Formatar a data e hora diretamente usando a timezone do Brasil
    const brazilianDate = new Intl.DateTimeFormat('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(new Date());
    
    const brazilianTime = new Intl.DateTimeFormat('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(new Date());
    
    return {
      date: brazilianDate, // Formato DD/MM/YYYY
      time: brazilianTime  // Formato HH:MM
    };
  };
  
  const { date: brazilDate, time: brazilTime } = getFormattedBrazilianDateTime();
  
  const defaultFormData: ServiceFormData = {
    service_name: '',
    service_date: brazilDate,
    service_time: brazilTime,
    notes: ''
  };

  const [formData, setFormData] = useState<ServiceFormData>(defaultFormData);
  const [errors, setErrors] = useState<Partial<Record<keyof ServiceFormData, string>>>({});

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<Record<keyof ServiceFormData, string>> = {};
    
    if (!formData.service_name.trim()) {
      newErrors.service_name = 'Tipo de serviço é obrigatório';
    }
    
    // Validate date format (DD/MM/YYYY)
    if (!formData.service_date.trim()) {
      newErrors.service_date = 'Data do serviço é obrigatória';
    } else if (!/^\d{2}\/\d{2}\/\d{4}$/.test(formData.service_date)) {
      newErrors.service_date = 'Data deve estar no formato DD/MM/YYYY';
    } else {
      // Further validation to check if date is valid
      const [day, month, year] = formData.service_date.split('/').map(Number);
      const dateObj = new Date(year, month - 1, day);
      if (
        dateObj.getDate() !== day ||
        dateObj.getMonth() !== month - 1 ||
        dateObj.getFullYear() !== year
      ) {
        newErrors.service_date = 'Data inválida';
      }
    }
    
    // Validate time format (HH:MM)
    if (!formData.service_time.trim()) {
      newErrors.service_time = 'Hora do serviço é obrigatória';
    } else if (!/^([01]\d|2[0-3]):([0-5]\d)$/.test(formData.service_time)) {
      newErrors.service_time = 'Hora deve estar no formato 24h (HH:MM)';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(() => {
    if (validateForm()) {
      // Converter data DD/MM/YYYY para YYYY-MM-DD para formato ISO
      const [day, month, year] = formData.service_date.split('/');
      const isoDate = `${year}-${month}-${day}`;
      
      // Combinar data e hora para formato ISO com fuso horário do Brasil (UTC-3)
      const combinedDateTime = `${isoDate}T${formData.service_time}:00-03:00`;
      
      onSave(petId, {
        ...formData,
        service_date: combinedDateTime
      });
    }
  }, [validateForm, onSave, petId, formData]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Apply masks for date and time fields
    if (name === 'service_date') {
      // Apply mask for date (DD/MM/YYYY)
      let formattedValue = value.replace(/\D/g, ''); // Remove non-digits
      if (formattedValue.length > 8) formattedValue = formattedValue.substring(0, 8);
      
      // Apply DD/MM/YYYY format
      if (formattedValue.length > 4) {
        formattedValue = `${formattedValue.substring(0, 2)}/${formattedValue.substring(2, 4)}/${formattedValue.substring(4)}`;
      } else if (formattedValue.length > 2) {
        formattedValue = `${formattedValue.substring(0, 2)}/${formattedValue.substring(2)}`;
      }
      
      setFormData(prev => ({ ...prev, [name]: formattedValue }));
    } else if (name === 'service_time') {
      // Apply mask for time (HH:MM)
      let formattedValue = value.replace(/\D/g, ''); // Remove non-digits
      if (formattedValue.length > 4) formattedValue = formattedValue.substring(0, 4);
      
      // Apply HH:MM format
      if (formattedValue.length > 2) {
        formattedValue = `${formattedValue.substring(0, 2)}:${formattedValue.substring(2)}`;
      }
      
      setFormData(prev => ({ ...prev, [name]: formattedValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    if (errors[name as keyof ServiceFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);

  const handleSelectChange = useCallback((e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof ServiceFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);

  const handleClose = useCallback(() => {
    setFormData(defaultFormData);
    setErrors({});
    onClose();
  }, [defaultFormData, onClose]);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Adicionar Serviço para {petName}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth required error={!!errors.service_name}>
              <InputLabel>Tipo de Serviço</InputLabel>
              <Select
                name="service_name"
                value={formData.service_name}
                onChange={handleSelectChange}
                label="Tipo de Serviço"
              >
                {availableServices.map((service) => (
                  <MenuItem key={service.id} value={service.name}>
                    {service.name}
                  </MenuItem>
                ))}
              </Select>
              {errors.service_name && <FormHelperText>{errors.service_name}</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="service_date"
              label="Data do Serviço"
              value={formData.service_date}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.service_date}
              helperText={errors.service_date}
              placeholder="DD/MM/AAAA"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="service_time"
              label="Hora do Serviço"
              value={formData.service_time}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.service_time}
              helperText={errors.service_time}
              placeholder="HH:MM"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              name="notes"
              label="Observações"
              value={formData.notes}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={4}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancelar</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Adicionar Serviço
        </Button>
      </DialogActions>
    </Dialog>
  );
}); 