import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  // ... existing channels ...

  // Customer channels
  invoke: (channel: string, ...args: any[]) => {
    const validChannels = [
      'customers:getAll',
      'customers:getById',
      'customers:create',
      'customers:update',
      'customers:delete',
      'customers:search',
      'customers:getInactive',
      'customers:reactivate',
      'customerPackages:getByPurchaseDateRange',
      // ... other existing channels ...
    ];
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, ...args);
    }
    throw new Error(`Unauthorized IPC Channel: ${channel}`);
  },

  // ... rest of the code ...
}); 