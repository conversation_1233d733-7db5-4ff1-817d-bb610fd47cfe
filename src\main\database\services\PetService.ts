import { AppDataSource } from '../connection';
import { Pet } from '../models/Pet';

export class PetService {
  private repository = AppDataSource.getRepository(Pet);

  async findAll(): Promise<Pet[]> {
    try {
      const result = await this.repository.find({
        relations: ['customer'],
        where: { 
          customer: { status: 'active' },
          is_hidden: false,
          status: 'active'
        },
        order: { name: 'ASC' }
      });
      return result;
    } catch (error) {
      console.error('Error finding all pets:', error);
      throw error;
    }
  }

  async findById(id: number): Promise<Pet | null> {
    return this.repository.findOne({
      where: { id, status: 'active' },
      relations: ['customer']
    });
  }

  async findByCustomerId(customerId: number): Promise<Pet[]> {
    try {
      const result = await this.repository.find({
        where: { 
          customer: { 
            id: customerId,
            status: 'active'
          },
          is_hidden: false,
          status: 'active'
        },
        relations: ['customer'],
        order: { name: '<PERSON><PERSON>' }
      });
      return result;
    } catch (error) {
      console.error('Error finding pets by customer ID:', error);
      throw error;
    }
  }

  async create(petData: Partial<Pet>): Promise<Pet> {
    const pet = this.repository.create(petData);
    return this.repository.save(pet);
  }

  async update(id: number, petData: Partial<Pet>): Promise<Pet | null> {
    await this.repository.update(id, petData);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    // Soft delete the pet
    const result = await this.repository.update(id, { 
      status: 'inactive',
      is_hidden: true
    });
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }

  async reactivate(id: number): Promise<Pet | null> {
    const result = await this.repository.update(id, { 
      status: 'active',
      is_hidden: false
    });
    if (result.affected && result.affected > 0) {
      return this.findById(id);
    }
    return null;
  }

  async findAllIncludingHidden(): Promise<Pet[]> {
    try {
      const result = await this.repository.find({
        relations: ['customer'],
        where: { 
          customer: { status: 'active' },
          status: 'active'
        },
        order: { name: 'ASC' }
      });
      return result;
    } catch (error) {
      console.error('Error finding all pets including hidden:', error);
      throw error;
    }
  }

  async findByCustomerIdIncludingHidden(customerId: number): Promise<Pet[]> {
    try {
      const result = await this.repository.find({
        where: { 
          customer: { 
            id: customerId
          }
        },
        relations: ['customer'],
        order: { name: 'ASC' }
      });
      return result;
    } catch (error) {
      console.error('Error finding pets by customer ID including hidden:', error);
      throw error;
    }
  }
} 