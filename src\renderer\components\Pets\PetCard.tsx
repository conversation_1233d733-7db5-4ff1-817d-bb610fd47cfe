import React, { useState, useCallback, useMemo } from "react";
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import CardActions from '@mui/material/CardActions';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import Zoom from '@mui/material/Zoom';
import Link from '@mui/material/Link';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Pets as PetsIcon,
  EventNote as EventNoteIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Person as PersonIcon,
} from "@mui/icons-material";
import { Pet } from "../../types/pets";
import { Customer } from "../../types/customers";
import { CustomerCard } from "../Customers/CustomerCard";

interface PetCardProps {
  pet: Pet;
  onEdit: (pet: Pet) => void;
  onDelete: (petId: number) => void;
  onViewServices: (pet: Pet) => void;
}

export const PetCard = React.memo(({
  pet,
  onEdit,
  onDelete,
  onViewServices,
}: PetCardProps) => {
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);

  // Get pet type icon with memoization
  const getPetTypeIcon = useCallback((type: string) => {
    switch (type?.toLowerCase()) {
      case "cachorro":
      case "dog":
        return "🐕";
      case "gato":
      case "cat":
        return "🐈";
      case "pássaro":
      case "bird":
      case "ave":
        return "🦜";
      case "coelho":
      case "rabbit":
        return "🐇";
      case "hamster":
      case "pequeno roedor":
        return "🐹";
      case "peixe":
      case "fish":
        return "🐠";
      case "réptil":
      case "reptil":
        return "🦎";
      default:
        return "🐾";
    }
  }, []);

  const getPetAvatarColor = useCallback((type: string) => {
    switch (type.toLowerCase()) {
      case "cachorro":
      case "dog":
        return "#9c27b0";
      case "gato":
      case "cat":
        return "#2196f3";
      case "ave":
      case "bird":
      case "pássaro":
        return "#ff9800";
      case "peixe":
      case "fish":
        return "#4caf50";
      case "réptil":
      case "reptil":
        return "#f44336";
      case "coelho":
      case "rabbit":
        return "#795548";
      case "hamster":
      case "pequeno roedor":
        return "#ff5722";
      default:
        return "#9e9e9e";
    }
  }, []);

  const getAgeString = useCallback((age: number | null) => {
    if (age === null) return "Idade desconhecida";
    return age === 1 ? "1 ano de idade" : `${age} anos de idade`;
  }, []);

  const handleOpenPhotoDialog = useCallback(() => {
    if (pet.photo_url) {
      setPhotoDialogOpen(true);
    }
  }, [pet.photo_url]);

  const handleClosePhotoDialog = useCallback(() => {
    setPhotoDialogOpen(false);
  }, []);

  const handleOpenCustomerDialog = useCallback(() => {
    if (pet.customer) {
      setCustomerDialogOpen(true);
    }
  }, [pet.customer]);

  const handleCloseCustomerDialog = useCallback(() => {
    setCustomerDialogOpen(false);
  }, []);

  // Adapta o objeto customer do Pet para o formato esperado pelo CustomerCard
  const adaptCustomerForDisplay = useCallback((petCustomer: any): Customer => {
    return {
      id: petCustomer.id,
      name: petCustomer.name,
      email: petCustomer.email || undefined,
      phone: petCustomer.phone || undefined,
      // Campos adicionais podem ser undefined
      address: undefined,
      additional_notes: undefined,
      created_at: undefined,
      updated_at: undefined,
      hasPendingPayments: false,
    };
  }, []);

  // Empty functions to pass to CustomerCard as props
  const noOp = useCallback(() => {}, []);
  
  // Memoize pet icon and color to prevent recalculations
  const petIcon = useMemo(() => getPetTypeIcon(pet.type), [pet.type, getPetTypeIcon]);
  const petColor = useMemo(() => getPetAvatarColor(pet.type), [pet.type, getPetAvatarColor]);
  
  // Memoize customer data conversion if customer exists
  const adaptedCustomer = useMemo(() => 
    pet.customer ? adaptCustomerForDisplay(pet.customer) : adaptCustomerForDisplay({
      id: 0,
      name: '',
      email: null,
      phone: null
    }), 
    [pet.customer, adaptCustomerForDisplay]
  );

  const handleEdit = useCallback(() => {
    onEdit(pet);
  }, [pet, onEdit]);

  const handleDelete = useCallback(() => {
    onDelete(pet.id);
  }, [pet.id, onDelete]);

  const handleViewServices = useCallback(() => {
    onViewServices(pet);
  }, [pet, onViewServices]);

  return (
    <Card
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        minHeight: "280px",
      }}
    >
      <Box sx={{ display: "flex", p: 2, alignItems: "center" }}>
        {pet.photo_url ? (
          <Box
            onClick={handleOpenPhotoDialog}
            sx={{
              width: 56,
              height: 56,
              mr: 2,
              overflow: "hidden",
              borderRadius: 1,
              position: "relative",
              cursor: "pointer",
              "&::before": {
                content: '""',
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: "rgba(0,0,0,0)",
                transition: "background-color 0.3s ease",
                zIndex: 1,
              },
              "&:hover": {
                "&::before": {
                  backgroundColor: "rgba(0,0,0,0.1)",
                },
                transform: "scale(1.05)",
                boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
              },
              transition: "transform 0.3s ease, box-shadow 0.3s ease",
              boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
            }}
          >
            <Avatar
              src={pet.photo_url}
              sx={{
                width: "100%",
                height: "100%",
                borderRadius: 1,
              }}
              variant="rounded"
            />
          </Box>
        ) : (
          <Avatar
            sx={{
              bgcolor: petColor,
              width: 56,
              height: 56,
              mr: 2,
              fontSize: "1.8rem",
            }}
          >
            {petIcon}
          </Avatar>
        )}
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h6" component="div">
            {pet.name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {pet.breed || pet.type}
          </Typography>
        </Box>
      </Box>
      <Divider />
      <CardContent sx={{ pt: 1, pb: 1, flexGrow: 1 }}>
        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 1 }}>
          <Chip
            label={pet.type}
            size="small"
            color="primary"
            sx={{ bgcolor: petColor }}
          />
          {pet.size && (
            <Chip label={pet.size} size="small" variant="outlined" />
          )}
          {pet.gender && (
            <Chip
              label={pet.gender}
              size="small"
              variant="outlined"
              color={pet.gender === "Macho" ? "info" : "secondary"}
              sx={{
                backgroundColor:
                  pet.gender === "Macho"
                    ? "rgba(33, 150, 243, 0.1)"
                    : "rgba(255, 192, 203, 0.2)",
                borderColor: pet.gender === "Macho" ? undefined : "#FFC0CB",
                color: pet.gender === "Macho" ? undefined : "#F06292",
              }}
            />
          )}
          {pet.fur_type && (
            <Chip
              label={
                pet.fur_type === "Curto"
                  ? "Pelo Curto"
                  : pet.fur_type === "Médio"
                    ? "Pelo Médio"
                    : "Pelo Longo"
              }
              size="small"
              variant="outlined"
              color="default"
              sx={{
                backgroundColor: "rgba(121, 85, 72, 0.1)",
                borderColor: "#795548",
                color: "#5D4037",
              }}
            />
          )}
        </Box>
        <Typography variant="body2" sx={{ mb: 1 }}>
          {getAgeString(pet.age)}
        </Typography>
        {pet.customer && (
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography variant="body2" color="text.secondary" component="span">
              Proprietário:
            </Typography>
            <Link
              component="button"
              variant="body2"
              color="primary"
              onClick={handleOpenCustomerDialog}
              sx={{
                ml: 0.5,
                cursor: "pointer",
                textDecoration: "none",
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              {pet.customer.name}
            </Link>
          </Box>
        )}
        {pet.additional_notes && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="caption"
              color="primary"
              sx={{
                display: "block",
                mb: 0.5,
                fontWeight: "medium",
              }}
            >
              Observações
            </Typography>
            <Box
              sx={{
                maxHeight: "85px",
                overflow: "auto",
                bgcolor: "background.paper",
                p: 1.5,
                borderRadius: 1,
                borderLeft: "3px solid",
                borderColor: "primary.light",
                boxShadow: "inset 0 0 6px rgba(0,0,0,0.05)",
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "rgba(0,0,0,0.1)",
                  borderRadius: "4px",
                },
              }}
            >
              <Typography
                variant="body2"
                color="text.primary"
                sx={{
                  whiteSpace: "pre-line",
                  fontSize: "0.875rem",
                  lineHeight: 1.5,
                }}
              >
                {pet.additional_notes}
              </Typography>
            </Box>
          </Box>
        )}
      </CardContent>
      <CardActions
        sx={{
          display: "flex",
          justifyContent: "space-between",
          p: 1,
          mt: "auto",
          borderTop: "1px solid",
          borderColor: "divider",
        }}
      >
        <Box>
          <Tooltip title="Ver Serviços">
            <Button
              onClick={handleViewServices}
              size="small"
              color="secondary"
              startIcon={<EventNoteIcon />}
            >
              Serviços
            </Button>
          </Tooltip>
        </Box>
        <Box>
          <Tooltip title="Editar Pet">
            <IconButton
              onClick={handleEdit}
              size="small"
              color="primary"
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Excluir Pet">
            <IconButton
              onClick={handleDelete}
              size="small"
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </CardActions>

      {/* Dialog para visualização ampliada da foto */}
      <Dialog
        open={photoDialogOpen}
        onClose={handleClosePhotoDialog}
        maxWidth="md"
        PaperProps={{
          sx: {
            bgcolor: "background.paper",
            borderRadius: 2,
            position: "relative",
            overflow: "hidden",
          },
        }}
      >
        <DialogContent sx={{ p: 0, overflow: "hidden", position: "relative" }}>
          <IconButton
            onClick={handleClosePhotoDialog}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
              bgcolor: "rgba(255,255,255,0.8)",
              "&:hover": {
                bgcolor: "rgba(255,255,255,0.9)",
              },
              zIndex: 1,
            }}
            size="small"
          >
            <CloseIcon />
          </IconButton>
          <Zoom in={photoDialogOpen}>
            <Box
              component="img"
              src={pet.photo_url || ""}
              alt={`Foto de ${pet.name}`}
              sx={{
                width: "100%",
                maxHeight: "80vh",
                objectFit: "contain",
                display: "block",
              }}
            />
          </Zoom>
        </DialogContent>
      </Dialog>

      {/* Dialog para visualização do proprietário */}
      <Dialog
        open={customerDialogOpen}
        onClose={handleCloseCustomerDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            overflow: "hidden",
          },
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 1,
            borderBottom: "1px solid",
            borderColor: "divider",
            pb: 2,
          }}
        >
          <PersonIcon color="primary" />
          <Typography variant="h6" component="span">
            Informações do Proprietário
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleCloseCustomerDialog}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
            size="small"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 2, pb: 1, px: 2 }}>
          {pet.customer && (
            <Box sx={{ mx: -1 }}>
              <CustomerCard
                customer={adaptedCustomer}
                onEdit={noOp}
                onDelete={noOp}
                onViewPets={noOp}
                onViewPackages={noOp}
                onReactivate={noOp}
                isInactive={false}
                hideActions={true}
              />
            </Box>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
});
