import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import CssBaseline from '@mui/material/CssBaseline';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListSubheader from '@mui/material/ListSubheader';
import Avatar from '@mui/material/Avatar';
import Badge from '@mui/material/Badge';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import InputBase from '@mui/material/InputBase';
import Tooltip from '@mui/material/Tooltip';
import Chip from '@mui/material/Chip';
import Button from '@mui/material/Button';
import Zoom from '@mui/material/Zoom';
import Slide from '@mui/material/Slide';
import useMediaQuery from '@mui/material/useMediaQuery';
import Stack from '@mui/material/Stack';
import LinearProgress from '@mui/material/LinearProgress';
import {
  alpha,
  keyframes,
  styled,
  useTheme,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  Pets as PetsIcon,
  ShoppingCart as SalesIcon,
  Event as AppointmentsIcon,
  Assessment as ReportsIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Search as SearchIcon,
  Help as HelpIcon,
  Close as CloseIcon,
  ChevronRight as ChevronRightIcon,
  CardGiftcard as CardGiftcardIcon,
} from '@mui/icons-material';
import { 
  useNotificationCount, 
  useNotificationList, 
  useNotificationActions 
} from '../../contexts/NotificationContext';
import { useTheme as useMuiTheme } from '@mui/material/styles';
// Import logo using require to ensure compatibility with Electron
const logoImage = require('../../../assets/logo.png');

const drawerWidth = 260;

// Custom styled search input
const Search = styled('div')(({ theme }) => ({
  position: 'relative',
  borderRadius: theme.shape.borderRadius,
  backgroundColor: alpha(theme.palette.common.white, 0.1),
  '&:hover': {
    backgroundColor: alpha(theme.palette.common.white, 0.15),
  },
  marginRight: theme.spacing(2),
  marginLeft: 0,
  width: '100%',
  [theme.breakpoints.up('sm')]: {
    marginLeft: theme.spacing(3),
    width: 'auto',
  },
}));

const SearchIconWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: '100%',
  position: 'absolute',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create('width'),
    width: '100%',
    [theme.breakpoints.up('md')]: {
      width: '20ch',
    },
  },
}));

const menuItems = [
  { 
    category: 'Principal',
    items: [
      { 
        text: 'Dashboard', 
        icon: <DashboardIcon />, 
        path: '/',
        tooltip: 'Visualize dados e estatísticas importantes'
      },
    ]
  },
  {
    category: 'Gerenciamento',
    items: [
      { 
        text: 'Estoque', 
        icon: <InventoryIcon />, 
        path: '/inventory',
        tooltip: 'Gerencie produtos e estoque'
      },
      { 
        text: 'Clientes', 
        icon: <PeopleIcon />, 
        path: '/customers',
        tooltip: 'Gerencie informações de clientes'
      },
      { 
        text: 'Pets', 
        icon: <PetsIcon />, 
        path: '/pets',
        tooltip: 'Gerencie informações de pets'
      },
      { 
        text: 'Vendas', 
        icon: <SalesIcon />, 
        path: '/sales',
        tooltip: 'Registre e gerencie vendas'
      },
      { 
        text: 'Pacotes', 
        icon: <CardGiftcardIcon />, 
        path: '/packages',
        tooltip: 'Gerencie pacotes de serviços recorrentes'
      },
      { 
        text: 'Agendamentos', 
        icon: <AppointmentsIcon />, 
        path: '/appointments',
        tooltip: 'Gerencie agendamentos e consultas'
      },
    ]
  },
  {
    category: 'Análises',
    items: [
      { 
        text: 'Relatórios', 
        icon: <ReportsIcon />, 
        path: '/reports',
        tooltip: 'Visualize relatórios e análises de dados'
      },
      { 
        text: 'Configurações', 
        icon: <SettingsIcon />, 
        path: '/settings',
        tooltip: 'Configure o sistema'
      },
    ]
  }
];

// Optimized pulse animation - reduced complexity and lengthened duration to reduce CPU usage
const pulseAnimation = keyframes`
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.03);
    opacity: 1;
  }
`;

interface MainLayoutProps {
  children: React.ReactNode;
}

// Types for our new components
interface NotificationsMenuProps {
  anchorEl: HTMLElement | null;
  notifications: any[];
  onClose: () => void;
  onMarkAllAsRead: () => void;
  onMarkAsRead: (id: string) => void;
  onRemoveNotification: (id: string, event: React.MouseEvent) => void;
  onNotificationClick: (notification: any) => void;
}

interface DrawerContentProps {
  collapsedMenu: boolean;
  isSmallScreen: boolean;
  location: { pathname: string };
  navigate: (path: string) => void;
  onToggleMenuCollapse: () => void;
  onLogout: () => void;
  hoveredItem: string | null;
  onHoverItem: (item: string | null) => void;
}

// Memoized NotificationsMenu component
const NotificationsMenu = React.memo(({
  anchorEl,
  notifications,
  onClose,
  onMarkAllAsRead,
  onMarkAsRead,
  onRemoveNotification,
  onNotificationClick
}: NotificationsMenuProps) => {
  const theme = useMuiTheme();
  
  return (
    <Menu
      anchorEl={anchorEl}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      id="notifications-menu"
      keepMounted
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      open={Boolean(anchorEl)}
      onClose={onClose}
      PaperProps={{
        style: { 
          maxHeight: 500,
          width: 350,
          maxWidth: '100%',
          overflow: 'auto',
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 2, py: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold">Notificações</Typography>
        {notifications.length > 0 && (
          <Button size="small" onClick={onMarkAllAsRead}>
            Marcar todas como lidas
          </Button>
        )}
      </Box>
      <Divider />
      
      {notifications.length === 0 ? (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Nenhuma notificação
          </Typography>
        </Box>
      ) : (
        notifications.map((notification) => (
          <React.Fragment key={notification.id}>
            <MenuItem 
              onClick={() => onNotificationClick(notification)}
              sx={{ 
                backgroundColor: notification.read ? 'inherit' : alpha(theme.palette.primary.light, 0.1),
                position: 'relative',
                py: 1.5,
                width: '100%',
                whiteSpace: 'normal',
              }}
            >
              <Box sx={{ width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 0.5 }}>
                  <Typography 
                    variant="subtitle2" 
                    color="primary"
                    sx={{ 
                      fontWeight: notification.read ? 'normal' : 'bold',
                      wordBreak: 'break-word',
                      maxWidth: '80%'
                    }}
                  >
                    {notification.title}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', flexShrink: 0 }}>
                    <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                      {new Date(notification.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Typography>
                    <IconButton 
                      size="small" 
                      onClick={(e) => onRemoveNotification(notification.id, e)}
                      sx={{ p: 0.5 }}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    mt: 0.5,
                    wordBreak: 'break-word',
                    whiteSpace: 'normal',
                    width: '100%'
                  }}
                >
                  {notification.message}
                </Typography>
                {!notification.read && (
                  <Box sx={{ position: 'absolute', left: 0, top: 0, height: '100%', width: 4, bgcolor: theme.palette.primary.main }} />
                )}
                {notification.type === 'warning' && (
                  <Chip 
                    label="Atenção" 
                    size="small" 
                    color="warning" 
                    sx={{ mt: 1, height: 24 }}
                  />
                )}
              </Box>
            </MenuItem>
            <Divider />
          </React.Fragment>
        ))
      )}
    </Menu>
  );
});

// Memoized DrawerContent component
const DrawerContent = React.memo(({
  collapsedMenu,
  isSmallScreen,
  location,
  navigate,
  onToggleMenuCollapse,
  onLogout,
  hoveredItem,
  onHoverItem
}: DrawerContentProps) => {
  const theme = useMuiTheme();

  return (
    <Box 
      sx={{ 
        overflowX: 'hidden', 
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Logo/Branding area */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsedMenu ? 'center' : 'flex-start',
          padding: theme.spacing(2),
          backgroundColor: alpha(theme.palette.primary.dark, 0.4),
          position: 'relative',
          overflow: 'hidden',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '2px',
            background: `linear-gradient(90deg, 
              ${alpha(theme.palette.secondary.main, 0)} 0%, 
              ${alpha(theme.palette.secondary.main, 0.8)} 50%, 
              ${alpha(theme.palette.secondary.main, 0)} 100%)`,
          }
        }}
      >
        <Zoom in={true} style={{ transitionDelay: '300ms' }}>
          <Avatar 
            src={logoImage}
            alt="Space Pet"
            sx={{ 
              width: 40, 
              height: 40, 
              marginRight: collapsedMenu ? 0 : 2, 
              backgroundColor: 'transparent',
              boxShadow: `0 0 10px ${alpha(theme.palette.secondary.main, 0.7)}`,
              transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                transform: 'scale(1.1) rotate(5deg)',
              }
            }}
          />
        </Zoom>
        
        {!collapsedMenu && (
          <Slide direction="right" in={true} mountOnEnter unmountOnExit>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                color: '#fff',
                letterSpacing: '0.5px',
                textShadow: `0 2px 4px ${alpha('#000', 0.3)}`,
              }}
            >
              Space Pet
            </Typography>
          </Slide>
        )}
        
        {/* Collapse toggle button */}
        <IconButton 
          sx={{ 
            position: 'absolute', 
            right: 8, 
            color: 'white', 
            opacity: 0.7,
            '&:hover': { opacity: 1 },
            display: isSmallScreen ? 'none' : 'flex',
          }}
          onClick={onToggleMenuCollapse}
          size="small"
        >
          <ChevronRightIcon 
            sx={{ 
              transform: collapsedMenu ? 'rotate(0deg)' : 'rotate(180deg)',
              transition: 'transform 0.3s ease',
            }} 
          />
        </IconButton>
      </Box>
      
      {/* Menu items */}
      <Box 
        sx={{ 
          overflow: 'auto', 
          mt: 1, 
          overflowX: 'hidden',
          flexGrow: 1,
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: alpha('#fff', 0.2),
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: alpha('#fff', 0.3),
          },
        }}
      >
        {menuItems.map((category) => (
          <React.Fragment key={category.category}>
            <List
              subheader={
                !collapsedMenu && (
                  <ListSubheader
                    component="div"
                    disableSticky
                    sx={{
                      backgroundColor: 'transparent',
                      color: alpha('#fff', 0.7),
                      fontSize: '0.75rem',
                      letterSpacing: '1px',
                      textTransform: 'uppercase',
                      fontWeight: 'bold',
                      lineHeight: '32px',
                      padding: '0 16px',
                      margin: 0,
                    }}
                  >
                    {category.category}
                  </ListSubheader>
                )
              }
              sx={{ pb: 0, mt: collapsedMenu ? 2 : 0 }}
            >
              {category.items.map((item) => {
                const isSelected = location.pathname === item.path;
                const isHovered = hoveredItem === item.text;
                
                return (
                  <Tooltip 
                    title={collapsedMenu ? item.text + (item.tooltip ? ` - ${item.tooltip}` : '') : item.tooltip || ''}
                    placement="right"
                    arrow
                    key={item.text}
                  >
                    <ListItem
                      button
                      selected={isSelected}
                      onClick={() => navigate(item.path)}
                      onMouseEnter={() => onHoverItem(item.text)}
                      onMouseLeave={() => onHoverItem(null)}
                      sx={{
                        margin: '4px 8px',
                        padding: collapsedMenu ? '16px 0' : undefined,
                        borderRadius: '12px',
                        justifyContent: collapsedMenu ? 'center' : 'flex-start',
                        position: 'relative',
                        overflow: 'hidden',
                        height: collapsedMenu ? '56px' : 'auto',
                        willChange: isSelected || isHovered ? 'transform, background-color' : 'auto',
                        '&.Mui-selected': {
                          backgroundColor: alpha(theme.palette.secondary.main, 0.15),
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.secondary.main, 0.25),
                          },
                          '& .MuiListItemIcon-root': {
                            color: theme.palette.secondary.light,
                          },
                          '& .MuiListItemText-primary': {
                            color: '#fff',
                            fontWeight: 'bold',
                          },
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            left: 0,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: '4px',
                            height: '60%',
                            backgroundColor: theme.palette.secondary.main,
                            borderRadius: '0 4px 4px 0',
                          },
                        },
                        '&:hover': {
                          backgroundColor: alpha('#fff', 0.08),
                          transform: 'translateX(4px)',
                          '& .MuiListItemIcon-root': {
                            color: alpha(theme.palette.secondary.light, 0.9),
                          },
                        },
                        transition: 'transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&::after': isSelected ? {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: `radial-gradient(circle at center, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 70%)`,
                          pointerEvents: 'none',
                          zIndex: 0,
                        } : {},
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          color: isSelected
                            ? theme.palette.secondary.light
                            : alpha('#fff', isHovered ? 0.9 : 0.7),
                          minWidth: collapsedMenu ? 24 : 36,
                          display: 'flex',
                          justifyContent: 'center',
                          animation: isSelected ? `${pulseAnimation} 3s ease-in-out infinite` : 'none',
                          transition: 'color 0.3s ease',
                          zIndex: 1,
                          '& svg': {
                            fontSize: isSelected || isHovered ? 24 : 20,
                            transition: 'font-size 0.2s ease',
                          },
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>
                      
                      {!collapsedMenu && (
                        <ListItemText 
                          primary={item.text}
                          primaryTypographyProps={{
                            fontSize: '0.9rem',
                            whiteSpace: 'nowrap',
                            fontWeight: isSelected ? 600 : 400,
                          }}
                          sx={{ zIndex: 1 }}
                        />
                      )}
                    </ListItem>
                  </Tooltip>
                );
              })}
            </List>
            {!collapsedMenu && (
              <Divider sx={{ backgroundColor: alpha('#fff', 0.1), margin: '4px 8px' }} />
            )}
          </React.Fragment>
        ))}
      </Box>
    </Box>
  );
});

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const theme = useMuiTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchorEl, setNotificationAnchorEl] = useState<null | HTMLElement>(null);
  
  // Add offline subscription days left state
  const [daysLeft, setDaysLeft] = useState<number | null>(null);

  useEffect(() => {
    const fetchOfflineAuthData = async () => {
      try {
        const data = await window.electronAPI.invoke('auth:getOfflineAuthData');
        if (data && data.subscriptionEndDate) {
          const endDate = new Date(data.subscriptionEndDate);
          const now = new Date();
          const diffMs = endDate.getTime() - now.getTime();
          const days = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
          setDaysLeft(days > 0 ? days : 0);
        }
      } catch (error) {
        console.error('[MainLayout] Error fetching offlineAuthData:', error);
      }
    };
    fetchOfflineAuthData();
  }, []);

  // Add state for transition
  const [isTransitioning, setIsTransitioning] = useState(false);
  const drawerRef = useRef<HTMLDivElement>(null);
  
  // Use optimized hooks
  const notifications = useNotificationList();
  const unreadCount = useNotificationCount();
  const { markAsRead, markAllAsRead, removeNotification } = useNotificationActions();

  const isMenuOpen = Boolean(anchorEl);
  const isNotificationsOpen = Boolean(notificationAnchorEl);

  // Simplified state variables
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [collapsedMenu, setCollapsedMenu] = useState(false);
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));

  // Add effect for transition handling
  useEffect(() => {
    const drawer = drawerRef.current;
    if (!drawer) return;

    const handleTransitionEnd = (e: TransitionEvent) => {
      // Only handle transition end for width property
      if (e.propertyName === 'width') {
        setIsTransitioning(false);
      }
    };

    drawer.addEventListener('transitionend', handleTransitionEnd);
    return () => {
      drawer.removeEventListener('transitionend', handleTransitionEnd);
    };
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchorEl(event.currentTarget);
    if (unreadCount > 0) {
      markAllAsRead();
    }
  };

  const handleNotificationsClose = () => {
    setNotificationAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    localStorage.removeItem('authToken');
    navigate('/login');
  };

  const handleProfileClick = () => {
    handleMenuClose();
    navigate('/profile');
  };

  const handleSettingsClick = () => {
    handleMenuClose();
    navigate('/settings');
  };

  const handleMarkAsRead = (id: string) => {
    markAsRead(id);
  };

  const handleRemoveNotification = (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    removeNotification(id);
  };

  const handleNotificationClick = (notification: any) => {
    // Navigate to related content if available
    if (notification.link) {
      navigate(notification.link);
    } else if (notification.relatedId && notification.title.includes('Agendamento')) {
      // For appointment notifications, navigate to the appointments page
      navigate('/appointments');
    }
    
    // Mark as read
    markAsRead(notification.id);
    
    // Close the menu
    handleNotificationsClose();
    
    // Execute custom action if available
    if (notification.action) {
      notification.action();
    }
  };

  // Optimize toggle menu collapse for better performance
  const handleToggleMenuCollapse = () => {
    if (!isSmallScreen) {
      setIsTransitioning(true);
      setCollapsedMenu(prev => !prev);
    }
  };

  // Memoize notification menu handlers
  const notificationHandlers = useMemo(() => ({
    onMarkAllAsRead: markAllAsRead,
    onMarkAsRead: markAsRead,
    onRemoveNotification: (id: string, event: React.MouseEvent) => {
      event.stopPropagation();
      removeNotification(id);
    },
    onNotificationClick: (notification: any) => {
      if (notification.link) {
        navigate(notification.link);
      } else if (notification.relatedId && notification.title.includes('Agendamento')) {
        navigate('/appointments');
      }
      markAsRead(notification.id);
      handleNotificationsClose();
      if (notification.action) {
        notification.action();
      }
    }
  }), [markAllAsRead, markAsRead, removeNotification, navigate]);

  // Memoize drawer handlers
  const drawerHandlers = useMemo(() => ({
    onToggleMenuCollapse: () => {
      if (!isSmallScreen) {
        setIsTransitioning(true);
        setCollapsedMenu(prev => !prev);
      }
    },
    onHoverItem: setHoveredItem,
    onLogout: handleLogout
  }), [isSmallScreen]);

  // Update drawer and main content for optimized transitions
  return (
    <Box 
      sx={{ 
        display: 'flex',
        pointerEvents: isTransitioning ? 'none' : 'auto',
      }}
    >
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{ 
          width: { sm: `calc(100% - ${collapsedMenu ? 80 : drawerWidth}px)` }, 
          ml: { sm: `${collapsedMenu ? 80 : drawerWidth}px` },
          boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
          backgroundColor: '#fff',
          color: theme.palette.text.primary,
          transition: 'width 0.3s ease, margin-left 0.3s ease',
          willChange: isTransitioning ? 'width, margin-left' : 'auto',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ display: { xs: 'none', md: 'block' }, fontWeight: 500 }}>
            Gerenciamento Space Pet
          </Typography>
          
          <Box sx={{ flexGrow: 1 }} />
          
          {/* App bar icons */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {daysLeft !== null && (
              <Tooltip title="Sua assinatura">
                <Typography variant="body2" sx={{ mr: 2 }}>
                  {daysLeft} dias restantes
                </Typography>
              </Tooltip>
            )}
            <Tooltip title="Ajuda">
              <IconButton
                size="large"
                color="primary"
                sx={{ mr: 1 }}
                onClick={() => {
                  // Dispatch custom event to trigger help dialog
                  const helpEvent = new Event('open-help-dialog');
                  window.dispatchEvent(helpEvent);
                }}
              >
                <HelpIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Notificações">
              <IconButton
                size="large"
                color="primary"
                onClick={handleNotificationsOpen}
                sx={{ mr: 1 }}
              >
                <Badge badgeContent={unreadCount} color="secondary">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      
      <NotificationsMenu
        anchorEl={notificationAnchorEl}
        notifications={notifications}
        onClose={handleNotificationsClose}
        {...notificationHandlers}
      />
      
      <Box
        component="nav"
        ref={drawerRef}
        sx={{ 
          width: { sm: collapsedMenu ? 80 : drawerWidth }, 
          flexShrink: { sm: 0 },
          transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          willChange: isTransitioning ? 'width' : 'auto',
        }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              backgroundColor: theme.palette.primary.main,
              backgroundImage: `linear-gradient(135deg, 
                ${theme.palette.primary.dark} 0%, 
                ${theme.palette.primary.main} 50%,
                ${alpha(theme.palette.primary.main, 0.8)} 100%)`,
              color: '#fff',
              overflowX: 'hidden',
              boxShadow: `4px 0 24px ${alpha('#000', 0.2)}`,
            },
          }}
        >
          <DrawerContent
            collapsedMenu={collapsedMenu}
            isSmallScreen={isSmallScreen}
            location={location}
            navigate={navigate}
            hoveredItem={hoveredItem}
            {...drawerHandlers}
          />
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: collapsedMenu ? 80 : drawerWidth,
              backgroundColor: theme.palette.primary.main,
              backgroundImage: `linear-gradient(135deg, 
                ${theme.palette.primary.dark} 0%, 
                ${theme.palette.primary.main} 50%,
                ${alpha(theme.palette.primary.main, 0.8)} 100%)`,
              color: '#fff',
              borderRight: 'none',
              overflowX: 'hidden',
              boxShadow: `4px 0 24px ${alpha('#000', 0.2)}`,
              transition: 'width 0.3s ease',
              willChange: isTransitioning ? 'width' : 'auto',
            },
          }}
          open
        >
          <DrawerContent
            collapsedMenu={collapsedMenu}
            isSmallScreen={isSmallScreen}
            location={location}
            navigate={navigate}
            hoveredItem={hoveredItem}
            {...drawerHandlers}
          />
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { 
            sm: `calc(100% - ${collapsedMenu ? 80 : drawerWidth}px)` 
          },
          backgroundColor: theme.palette.background.default,
          minHeight: '100vh',
          transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          willChange: isTransitioning ? 'width' : 'auto',
          pointerEvents: isTransitioning ? 'none' : 'auto',
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

export default MainLayout; 