import { AppDataSource } from '../connection';
import { Product } from '../models/Product';

export class ProductService {
  private repository = AppDataSource.getRepository(Product);

  async findAll(): Promise<Product[]> {
    return this.repository.find({ 
      where: { is_deleted: false },
      order: { name: 'ASC' } 
    });
  }

  async findById(id: number): Promise<Product | null> {
    return this.repository.findOneBy({ id, is_deleted: false });
  }

  async findByCategory(category: string): Promise<Product[]> {
    return this.repository.find({
      where: { category, is_deleted: false },
      order: { name: 'ASC' }
    });
  }

  async findLowStock(): Promise<Product[]> {
    return this.repository
      .createQueryBuilder('product')
      .where('product.stock_quantity <= product.min_stock_level')
      .andWhere('product.is_deleted = :isDeleted', { isDeleted: false })
      .getMany();
  }

  async create(productData: Partial<Product>): Promise<Product> {
    const product = this.repository.create(productData);
    return this.repository.save(product);
  }

  async update(id: number, productData: Partial<Product>): Promise<Product | null> {
    await this.repository.update(id, productData);
    return this.findById(id);
  }

  async updateStock(id: number, quantity: number): Promise<Product | null> {
    const product = await this.findById(id);
    if (!product) return null;
    
    // Update stock
    product.stock_quantity += quantity;
    return this.repository.save(product);
  }

  async delete(id: number): Promise<boolean> {
    // Instead of deleting, mark as deleted
    const result = await this.repository.update(id, { is_deleted: true });
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }

  // Add a hard delete method if needed for admin purposes
  async hardDelete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }
} 