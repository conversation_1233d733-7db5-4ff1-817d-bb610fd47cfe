import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import useTheme from '@mui/material/styles/useTheme';
import Badge from '@mui/material/Badge';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import RefreshIcon from '@mui/icons-material/Refresh';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import PaymentIcon from '@mui/icons-material/Payment';
import WarningIcon from '@mui/icons-material/Warning';
import { useNavigate } from 'react-router-dom';
import { CustomerCard } from '../components/Customers/CustomerCard';
import { CustomerFormDialog } from '../components/Customers/CustomerFormDialog';
import { CustomerPetsDialog } from '../components/Customers/CustomerPetsDialog';
import { CustomerPackagesDialog } from '../components/Customers/CustomerPackagesDialog';
import { Customer, CustomerFormData, PendingSale } from '../types/customers';
import { Pet } from '../types/pets';
import { PaymentMethod } from '../types/sales';
import { CustomerPackage } from '../types/packages';
import { useCustomers } from '../hooks/useCustomers';
import { usePets } from '../hooks/usePets';
import { useServices } from '../hooks/useServices';
import { usePackages } from '../hooks/usePackages';
import { useCustomerPackages } from '../hooks/useCustomerPackages';
import { PendingSalesDialog } from '../components/Customers/PendingSalesDialog';
// Import React Virtualized components
import {
  WindowScroller,
  AutoSizer,
  List,
  CellMeasurer,
  CellMeasurerCache
} from 'react-virtualized';

// Helper function to adapt database model to frontend type
const adaptCustomer = (dbCustomer: any): Customer => ({
  id: dbCustomer.id,
  name: dbCustomer.name,
  email: dbCustomer.email,
  phone: dbCustomer.phone,
  address: dbCustomer.address,
  additional_notes: dbCustomer.additional_notes,
  created_at: dbCustomer.created_at,
  updated_at: dbCustomer.updated_at,
  status: dbCustomer.status || 'active',
  pets: dbCustomer.pets ? dbCustomer.pets.map(adaptPet) : [],
  pendingSales: dbCustomer.pendingSales || [],
  hasPendingPayments: dbCustomer.hasPendingPayments || false
});

// Helper function to adapt database pet model to frontend type
const adaptPet = (dbPet: any): Pet => ({
  id: dbPet.id,
  customer_id: dbPet.customer?.id || 0,
  name: dbPet.name,
  type: dbPet.type,
  breed: dbPet.breed,
  age: dbPet.age,
  size: dbPet.size,
  additional_notes: dbPet.additional_notes,
  is_hidden: dbPet.is_hidden || false,
  status: dbPet.status || 'active',
  created_at: dbPet.created_at,
  updated_at: dbPet.updated_at,
  gender: dbPet.gender || '',
  fur_type: dbPet.fur_type || '',
  photo_url: dbPet.photo_url || null
});

// Custom hook for debouncing values
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

const Customers: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  
  // Use the hooks to fetch real data
  const { 
    customers: dbCustomers, 
    inactiveCustomers: dbInactiveCustomers,
    loading: customersLoading, 
    error: customersError, 
    createCustomer, 
    updateCustomer, 
    deleteCustomer: deleteCustomerFromDb,
    refetch: refetchCustomers,
    reactivateCustomer,
    getPendingSalesByCustomerId,
    markSaleAsPaid,
    fetchInactiveCustomers,
    inactiveCustomersLoaded
  } = useCustomers();

  const {
    loading: petsLoading,
    error: petsError,
    getPetsByCustomerId,
    refetch: refetchPets
  } = usePets();

  const { services } = useServices();
  const { 
    packages,
    loading: packagesLoading,
    error: packagesError,
  } = usePackages();

  const { getCustomerPackages: fetchCustomerPackages } = useCustomerPackages();

  // Memoize adapted customers to prevent creating new array references on every render
  const customers = useMemo(() => 
    dbCustomers ? dbCustomers.map(adaptCustomer) : []
  , [dbCustomers]);
  
  const inactiveCustomers = useMemo(() => 
    dbInactiveCustomers ? dbInactiveCustomers.map(adaptCustomer) : []
  , [dbInactiveCustomers]);

  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 150); // Debounce search term with 150ms delay
  
  const [formOpen, setFormOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | undefined>(undefined);
  
  const [petsDialogOpen, setPetsDialogOpen] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number>(0);
  const [selectedCustomerName, setSelectedCustomerName] = useState<string>('');
  const [customerPets, setCustomerPets] = useState<Pet[]>([]);
  
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<number | null>(null);
  const [customerToReactivate, setCustomerToReactivate] = useState<number | null>(null);
  const [reactivateDialogOpen, setReactivateDialogOpen] = useState<boolean>(false);
  const [showInactive, setShowInactive] = useState<boolean>(false);
  
  // Add new state variables for similar inactive customer dialog
  const [similarInactiveCustomer, setSimilarInactiveCustomer] = useState<Customer | null>(null);
  const [similarCustomerDialogOpen, setSimilarCustomerDialogOpen] = useState(false);
  const [pendingCustomerData, setPendingCustomerData] = useState<CustomerFormData | null>(null);

  const [packagesDialogOpen, setPackagesDialogOpen] = useState(false);
  
  // Add new state for pending sales
  const [pendingSalesDialogOpen, setPendingSalesDialogOpen] = useState(false);
  const [selectedCustomerPendingSales, setSelectedCustomerPendingSales] = useState<PendingSale[]>([]);
  const [pendingSalesLoading, setPendingSalesLoading] = useState(false);
  const [pendingSalesError, setPendingSalesError] = useState<string | null>(null);
  const [showOnlyWithPendingSales, setShowOnlyWithPendingSales] = useState(false);
  
  // State to store which customers have no-show packages
  const [customersWithNoShow, setCustomersWithNoShow] = useState<Record<number, boolean>>({});

  // Calculate pending payments customers count
  const pendingPaymentsCount = useMemo(() => 
    customers.filter(customer => customer.hasPendingPayments === true).length
  , [customers]);
  
  // Add CellMeasurerCache for variable height rows
  const cache = useMemo(() => new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 300, // Approximate height of a customer card
    minHeight: 250,
  }), []);
  
  // Reset cache when filtered customers change
  useEffect(() => {
    if (filteredCustomers && filteredCustomers.length > 0) {
      cache.clearAll();
    }
  }, [filteredCustomers, cache]);

  // Calculate number of columns based on screen width
  const getColumnCount = (width: number) => {
    if (width < 600) return 1; // Mobile view
    if (width < 960) return 2; // Tablet view
    return 3; // Desktop view - 3 columns
  };

  // Calculate row count based on items and columns
  const getRowCount = (itemCount: number, columnCount: number) => {
    return Math.ceil(itemCount / columnCount);
  };

  // Get item at index accounting for column layout
  const getItemAtIndex = (items: Customer[], rowIndex: number, columnIndex: number, columnCount: number) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    return itemIndex < items.length ? items[itemIndex] : null;
  };
  
  // Combined filter logic for customers
  useEffect(() => {
    // If showing inactive but inactive customers haven't been loaded yet,
    // return an empty array to prevent showing active customers as inactive
    if (showInactive && !inactiveCustomersLoaded) {
      setFilteredCustomers([]);
      return;
    }
    
    let result = showInactive ? inactiveCustomers : customers;
    
    // Apply search term filter
    if (debouncedSearchTerm.trim()) {
      const lowerSearchTerm = debouncedSearchTerm.toLowerCase();
      result = result.filter(customer => 
        customer.name.toLowerCase().includes(lowerSearchTerm) ||
        (customer.email && customer.email.toLowerCase().includes(lowerSearchTerm)) ||
        (customer.phone && customer.phone.toLowerCase().includes(lowerSearchTerm)) ||
        (customer.address && customer.address.toLowerCase().includes(lowerSearchTerm))
      );
    }
    
    // Apply pending sales filter if enabled
    if (showOnlyWithPendingSales && !showInactive) {
      result = result.filter(customer => customer.hasPendingPayments === true);
    }
    
    setFilteredCustomers(result);
  }, [
    customers, 
    inactiveCustomers, 
    debouncedSearchTerm,
    showInactive, 
    showOnlyWithPendingSales,
    inactiveCustomersLoaded
  ]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleAddCustomer = useCallback(() => {
    setSelectedCustomer(undefined);
    setFormOpen(true);
  }, []);

  const handleEditCustomer = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setFormOpen(true);
  }, []);

  const handleSaveCustomer = useCallback(async (customerData: CustomerFormData) => {
    try {
      if (selectedCustomer) {
        // Update existing customer
        const result = await updateCustomer(selectedCustomer.id, customerData);
        if (result) {
          await refetchCustomers();
          setFormOpen(false);
        }
      } else {
        // Check if there's an inactive customer with the same name or phone
        const match = inactiveCustomers.find(c => 
          (c.name.toLowerCase() === customerData.name.toLowerCase()) || 
          (c.phone && customerData.phone && c.phone === customerData.phone)
        );
        
        if (match) {
          // Found a match - ask user what to do
          setSimilarInactiveCustomer(match);
          setPendingCustomerData(customerData);
          setSimilarCustomerDialogOpen(true);
          // Don't close the form yet
        } else {
          // No match - proceed with creating new customer
          const result = await createCustomer(customerData);
          if (result) {
            await refetchCustomers();
            setFormOpen(false);
          }
        }
      }
    } catch (error: any) {
      console.error('Error saving customer:', error);
      // Create a message to display in the form
      const formErrorMessage = error?.message || 'Erro ao salvar cliente. Tente novamente.';
      // We need to return the error message to the form dialog
      throw new Error(formErrorMessage);
    }
  }, [selectedCustomer, inactiveCustomers, createCustomer, updateCustomer, refetchCustomers]);
  
  const handleCreateNewCustomer = useCallback(async () => {
    if (pendingCustomerData) {
      try {
        // console.log('Creating new customer:', pendingCustomerData);
        const result = await createCustomer(pendingCustomerData, true);
        // console.log('Create customer result:', result);
        await refetchCustomers();
        setSimilarCustomerDialogOpen(false);
        setFormOpen(false);
        setPendingCustomerData(null);
        setSimilarInactiveCustomer(null);
      } catch (error) {
        console.error('Error creating new customer:', error);
      }
    }
  }, [pendingCustomerData, createCustomer, refetchCustomers]);
  
  const handleReactivateSimilarCustomer = useCallback(async () => {
    if (similarInactiveCustomer && pendingCustomerData) {
      try {
        // Set loading state
        setLoadingInactiveCustomers(true);
        
        // First reactivate
        await reactivateCustomer(similarInactiveCustomer.id);
        
        // Then update with new data
        await updateCustomer(similarInactiveCustomer.id, pendingCustomerData);
        
        // If we're currently showing inactive customers, refresh the list
        if (showInactive) {
          // Refresh the filtered list to remove the reactivated customer
          setFilteredCustomers(prev => 
            prev.filter(c => c.id !== similarInactiveCustomer.id)
          );
        }
        
        // Close dialogs
        setSimilarCustomerDialogOpen(false);
        setFormOpen(false);
        setPendingCustomerData(null);
        setSimilarInactiveCustomer(null);
        setLoadingInactiveCustomers(false);
      } catch (error) {
        console.error('Error reactivating and updating customer:', error);
        setLoadingInactiveCustomers(false);
      }
    }
  }, [similarInactiveCustomer, pendingCustomerData, reactivateCustomer, updateCustomer, showInactive]);

  const handleDeleteCustomer = useCallback((customerId: number) => {
    setCustomerToDelete(customerId);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeleteCustomer = useCallback(async () => {
    if (customerToDelete) {
      try {
        const success = await deleteCustomerFromDb(customerToDelete);
        if (success) {
          setDeleteDialogOpen(false);
          setCustomerToDelete(null);
          await refetchCustomers();
          await refetchPets();
        }
      } catch (error) {
        console.error('Error deleting customer:', error);
      }
    }
  }, [customerToDelete, deleteCustomerFromDb, refetchCustomers, refetchPets]);

  const handleReactivateCustomer = useCallback((customerId: number) => {
    setCustomerToReactivate(customerId);
    setReactivateDialogOpen(true);
  }, []);

  const confirmReactivateCustomer = useCallback(async () => {
    if (customerToReactivate) {
      try {
        const success = await reactivateCustomer(customerToReactivate);
        if (success) {
          setReactivateDialogOpen(false);
          setCustomerToReactivate(null);
          await refetchCustomers();
          await refetchPets();
        }
      } catch (error) {
        console.error('Error reactivating customer:', error);
      }
    }
  }, [customerToReactivate, reactivateCustomer, refetchCustomers, refetchPets]);

  const handleViewPets = useCallback(async (customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      try {
        const dbPets = await getPetsByCustomerId(customerId);
        const adaptedPets = dbPets.map(adaptPet);
        setSelectedCustomerId(customerId);
        setSelectedCustomerName(customer.name);
        setCustomerPets(adaptedPets);
        setPetsDialogOpen(true);
      } catch (error) {
        console.error('Error fetching customer pets:', error);
      }
    }
  }, [customers, getPetsByCustomerId]);

  const handleAddPet = useCallback((customerId: number) => {
    // Navigate to the pets page with customer ID as state
    navigate('/pets', { state: { addPetForCustomer: customerId } });
    setPetsDialogOpen(false);
  }, [navigate]);

  const handleViewPetDetails = useCallback((petId: number) => {
    // Navigate to the pets page with pet ID as state
    navigate('/pets', { state: { viewPetDetails: petId } });
    setPetsDialogOpen(false);
  }, [navigate]);

  const handleViewPackages = useCallback((customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      setSelectedCustomerId(customerId);
      setSelectedCustomerName(customer.name);
      setPackagesDialogOpen(true);
    }
  }, [customers]);

  const handleViewPendingSales = useCallback(async (customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      try {
        setPendingSalesLoading(true);
        setPendingSalesError(null);
        // Fetch fresh pending sales data
        const pendingSales = await getPendingSalesByCustomerId(customerId);
        setSelectedCustomerId(customerId);
        setSelectedCustomerName(customer.name);
        setSelectedCustomerPendingSales(pendingSales);
        setPendingSalesDialogOpen(true);
      } catch (error) {
        console.error('Error fetching customer pending sales:', error);
        setPendingSalesError('Failed to fetch pending sales information');
      } finally {
        setPendingSalesLoading(false);
      }
    }
  }, [customers, getPendingSalesByCustomerId]);

  const handleMarkSaleAsPaid = useCallback(async (saleId: number) => {
    try {
      const success = await markSaleAsPaid(saleId);
      if (success) {
        // Refresh pending sales for this specific customer
        const pendingSales = await getPendingSalesByCustomerId(selectedCustomerId);
        setSelectedCustomerPendingSales(pendingSales);
        // refetchCustomers() is no longer needed here as markSaleAsPaid updates the customer's status internally
      }
    } catch (error) {
      console.error('Error marking sale as paid:', error);
    }
  }, [markSaleAsPaid, getPendingSalesByCustomerId, selectedCustomerId]);

  const handleViewSaleDetails = useCallback((saleId: number) => {
    // Navigate to sales page with sale ID parameter
    navigate(`/sales?saleId=${saleId}`);
    setPendingSalesDialogOpen(false);
  }, [navigate]);

  // Add a specific loading state for inactive customers
  const [loadingInactiveCustomers, setLoadingInactiveCustomers] = useState(false);

  // Update the toggleShowInactive function
  const toggleShowInactive = useCallback(() => {
    const newState = !showInactive;
    setShowInactive(newState);
    
    // Fetch inactive customers if needed
    if (newState && !inactiveCustomersLoaded) {
      setLoadingInactiveCustomers(true);
      fetchInactiveCustomers().finally(() => {
        setLoadingInactiveCustomers(false);
      });
    }
  }, [showInactive, inactiveCustomersLoaded, fetchInactiveCustomers]);
  
  // Add effect to track when inactive customers are loaded
  useEffect(() => {
    if (inactiveCustomersLoaded) {
      setLoadingInactiveCustomers(false);
    }
  }, [inactiveCustomersLoaded]);

  const toggleShowPendingSales = useCallback(() => {
    setShowOnlyWithPendingSales(!showOnlyWithPendingSales);
  }, [showOnlyWithPendingSales]);

  useEffect(() => {
    if (customers && customers.length > 0) {
      const fetchAllCustomerPackagesStatus = async () => {
        const noShowStatus: Record<number, boolean> = {};
        for (const customer of customers) {
          if (customer.status === 'active') { // Only check active customers
            try {
              const packages = await fetchCustomerPackages(customer.id);
              const hasNoShow = packages.some(pkg => pkg.status === 'on_hold_no_show');
              noShowStatus[customer.id] = hasNoShow;
            } catch (error) {
              console.error(`Error fetching packages for customer ${customer.id}:`, error);
              noShowStatus[customer.id] = false; // Assume no no-show if error
            }
          }
        }
        setCustomersWithNoShow(noShowStatus);
      };

      void fetchAllCustomerPackagesStatus();
    }
  }, [customers, fetchCustomerPackages]);

  const sortedAndFilteredCustomers = useMemo(() => {
    let currentCustomers = showInactive ? inactiveCustomers : customers;

    if (showOnlyWithPendingSales) {
      currentCustomers = currentCustomers.filter(customer => customer.hasPendingPayments === true);
    }

    if (debouncedSearchTerm) {
      const lowercasedFilter = debouncedSearchTerm.toLowerCase();
      currentCustomers = currentCustomers.filter(customer =>
        customer.name.toLowerCase().includes(lowercasedFilter) ||
        (customer.email && customer.email.toLowerCase().includes(lowercasedFilter)) ||
        (customer.phone && customer.phone.includes(lowercasedFilter)) ||
        (customer.address && customer.address.toLowerCase().includes(lowercasedFilter)) ||
        (customer.pets && customer.pets.some(pet => pet.name.toLowerCase().includes(lowercasedFilter))) ||
        (customer.additional_notes && customer.additional_notes.toLowerCase().includes(lowercasedFilter))
      );
    }

    // New sorting logic
    return currentCustomers.sort((a, b) => {
      const aHasNoShow = customersWithNoShow[a.id] === true;
      const bHasNoShow = customersWithNoShow[b.id] === true;

      if (aHasNoShow && !bHasNoShow) {
        return -1; // a comes first
      }
      if (!aHasNoShow && bHasNoShow) {
        return 1; // b comes first
      }

      // If both have or don't have no-show, sort by created_at (newest first)
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
      
      if (dateB !== dateA) {
        return dateB - dateA; // Sorts descending (newest first)
      }
      
      // Fallback sort by ID if dates are the same or missing
      return a.id - b.id;
    });
  }, [
    customers,
    inactiveCustomers,
    showInactive,
    debouncedSearchTerm,
    showOnlyWithPendingSales,
    customersWithNoShow
  ]);

  // Reset cache when filtered customers change
  useEffect(() => {
    if (sortedAndFilteredCustomers && sortedAndFilteredCustomers.length > 0) {
      cache.clearAll();
    }
  }, [sortedAndFilteredCustomers, cache]);

  if (customersLoading || loadingInactiveCustomers) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          {loadingInactiveCustomers ? 'Carregando clientes inativos...' : 'Carregando...'}
        </Typography>
      </Box>
    );
  }

  if (customersError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar clientes: {customersError}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PersonIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Box>
            <Typography 
              variant="h4" 
              component="h1" 
              sx={{ 
                fontWeight: 'bold', 
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -8,
                  left: 0,
                  width: 60,
                  height: 4,
                  backgroundColor: theme.palette.primary.main,
                  borderRadius: 2,
                }
              }}
            >
              {showInactive ? 'Clientes Inativos' : 'Gerenciamento de Clientes'}
            </Typography>
          </Box>
        </Box>
        <Box>
          <Button
            variant={showInactive ? "contained" : "outlined"}
            onClick={toggleShowInactive}
            sx={{ mr: 2 }}
            startIcon={<PersonIcon />}
          >
            {showInactive ? "Mostrar Ativos" : "Mostrar Inativos"}
          </Button>
          
          {!showInactive && (
            <Badge 
              badgeContent={pendingPaymentsCount} 
              color="warning"
              sx={{ 
                '& .MuiBadge-badge': { 
                  fontSize: '0.6rem', 
                  height: '16px', 
                  minWidth: '16px' 
                },
                mr: 2 
              }}
              invisible={pendingPaymentsCount === 0}
            >
              <Button
                variant={showOnlyWithPendingSales ? "contained" : "outlined"}
                onClick={toggleShowPendingSales}
                color="warning"
                startIcon={<PaymentIcon />}
              >
                {showOnlyWithPendingSales ? "Todos os Clientes" : "Clientes Devendo"}
              </Button>
            </Badge>
          )}
          
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />}
            onClick={handleAddCustomer}
          >
            Adicionar Cliente
          </Button>
        </Box>
      </Box>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Buscar clientes por nome, email, telefone ou endereço"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Divider sx={{ mb: 3 }} />
      
      {sortedAndFilteredCustomers.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Nenhum cliente encontrado. Adicione um novo cliente para começar.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ height: 'calc(100vh - 250px)', width: '100%' }}>
          <WindowScroller>
            {({ height, isScrolling, onChildScroll, scrollTop }) => (
              <AutoSizer disableHeight>
                {({ width }) => {
                  const columnCount = getColumnCount(width);
                  const rowCount = getRowCount(sortedAndFilteredCustomers.length, columnCount);
                  
                  return (
                    <List
                      autoHeight
                      height={height || 500}
                      isScrolling={isScrolling}
                      onScroll={onChildScroll}
                      rowCount={rowCount}
                      rowHeight={cache.rowHeight}
                      scrollTop={scrollTop}
                      width={width}
                      overscanRowCount={3}
                      deferredMeasurementCache={cache}
                      style={{
                        outline: 'none',
                        paddingBottom: '20px',
                        overflowX: 'hidden'
                      }}
                      rowRenderer={({ index, key, style, parent }) => {
                        return (
                          <CellMeasurer
                            cache={cache}
                            columnIndex={0}
                            key={key}
                            parent={parent}
                            rowIndex={index}
                          >
                            {({ measure }) => (
                              <div 
                                style={{ 
                                  ...style, 
                                  padding: '8px',
                                  boxSizing: 'border-box',
                                  display: 'flex'
                                }}
                              >
                                {/* Create a row with potentially multiple columns */}
                                {Array.from({ length: columnCount }).map((_, colIndex) => {
                                  const customer = getItemAtIndex(sortedAndFilteredCustomers, index, colIndex, columnCount);
                                  if (!customer) return <div key={colIndex} style={{ flex: 1 }} />;
                                  
                                  return (
                                    <div 
                                      key={colIndex} 
                                      style={{ 
                                        flex: 1, 
                                        padding: '0 8px',
                                        boxSizing: 'border-box'
                                      }}
                                    >
                                      <CustomerCard 
                                        customer={customer}
                                        onEdit={() => handleEditCustomer(customer)}
                                        onDelete={() => handleDeleteCustomer(customer.id)}
                                        onViewPets={() => handleViewPets(customer.id)}
                                        onViewPackages={() => handleViewPackages(customer.id)}
                                        onReactivate={() => handleReactivateCustomer(customer.id)}
                                        onViewPendingSales={() => handleViewPendingSales(customer.id)}
                                        isInactive={customer.status === 'inactive'}
                                        hideActions={false}
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            )}
                          </CellMeasurer>
                        );
                      }}
                    />
                  );
                }}
              </AutoSizer>
            )}
          </WindowScroller>
        </Box>
      )}

      {/* Customer form dialog */}
      <CustomerFormDialog 
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSave={handleSaveCustomer}
        customer={selectedCustomer}
        title={selectedCustomer ? 'Editar Cliente' : 'Adicionar Novo Cliente'}
      />

      {/* Customer pets dialog */}
      <CustomerPetsDialog
        open={petsDialogOpen}
        onClose={() => setPetsDialogOpen(false)}
        customerName={selectedCustomerName}
        pets={customerPets}
        onAddPet={() => handleAddPet(selectedCustomerId)}
        onViewPetDetails={handleViewPetDetails}
        loading={petsLoading}
        error={petsError}
      />

      {/* Customer packages dialog */}
      <CustomerPackagesDialog
        open={packagesDialogOpen}
        onClose={() => setPackagesDialogOpen(false)}
        customerName={selectedCustomerName}
        customerId={selectedCustomerId}
      />

      {/* Confirm delete dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirmar Desativação</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Tem certeza que deseja desativar este cliente?
          </DialogContentText>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body1">Esta ação irá:</Typography>
            <ul>
              <li>Mover o cliente para a lista de inativos</li>
              <li>Apagar todos os pets deste cliente</li>
              <li>Remover todos os agendamentos ativos (não afeta agendamentos já concluídos)</li>
            </ul>
            <Typography variant="body1">
              Você pode reativar o cliente posteriormente se necessário.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancelar</Button>
          <Button onClick={confirmDeleteCustomer} color="error">
            Desativar
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={reactivateDialogOpen}
        onClose={() => setReactivateDialogOpen(false)}
      >
        <DialogTitle>Reativar Cliente</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Tem certeza que deseja reativar este cliente?
          </DialogContentText>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body1">Esta ação irá:</Typography>
            <ul>
              <li>Mover o cliente de volta para a lista de ativos</li>
              <li>Restaurar a visibilidade de todos os pets deste cliente</li>
              <li>Permitir que novos agendamentos sejam feitos</li>
            </ul>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReactivateDialogOpen(false)}>Cancelar</Button>
          <Button onClick={confirmReactivateCustomer} color="primary">
            Reativar
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Similar inactive customer dialog */}
      <Dialog
        open={similarCustomerDialogOpen}
        onClose={() => setSimilarCustomerDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ borderBottom: 1, borderColor: 'divider', pb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">Cliente Inativo Encontrado</Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Alert severity="info" sx={{ mb: 3 }}>
            Encontramos um cliente inativo com dados similares aos que você informou.
          </Alert>
          
          {similarInactiveCustomer && (
            <Box sx={{ 
              mt: 2, 
              p: 2, 
              bgcolor: 'background.paper', 
              borderRadius: 1, 
              boxShadow: 1,
              borderLeft: '4px solid',
              borderColor: 'primary.main',
              mb: 4
            }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                {similarInactiveCustomer.name}
              </Typography>
              <Grid container spacing={1} sx={{ mt: 1 }}>
                {similarInactiveCustomer.phone && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Telefone:</strong> {similarInactiveCustomer.phone}
                    </Typography>
                  </Grid>
                )}
                {similarInactiveCustomer.email && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Email:</strong> {similarInactiveCustomer.email}
                    </Typography>
                  </Grid>
                )}
                {similarInactiveCustomer.address && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Endereço:</strong> {similarInactiveCustomer.address}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
          
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
            O que você deseja fazer?
          </Typography>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Box 
                onClick={() => handleReactivateSimilarCustomer()}
                role="button"
                aria-label="Reativar cliente existente"
                sx={{ 
                  p: 2, 
                  border: '1px solid', 
                  borderColor: 'success.main', 
                  borderRadius: 1,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    backgroundColor: 'success.light',
                    color: 'success.contrastText',
                    transform: 'translateY(-2px)',
                    boxShadow: 2
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <RefreshIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="bold">Reativar o cliente existente</Typography>
                </Box>
                <Typography variant="body2">
                  Reactive o cliente inativo e atualize seus dados com as novas informações.
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box 
                onClick={() => handleCreateNewCustomer()}
                role="button"
                aria-label="Criar novo cliente"
                sx={{ 
                  p: 2, 
                  border: '1px solid', 
                  borderColor: 'primary.main', 
                  borderRadius: 1,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    backgroundColor: 'primary.light',
                    color: 'primary.contrastText',
                    transform: 'translateY(-2px)',
                    boxShadow: 2
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <PersonAddIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="bold">Criar um novo cliente</Typography>
                </Box>
                <Typography variant="body2">
                  Ignore o cliente inativo e crie um novo registro com as informações fornecidas.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: 1, borderColor: 'divider' }}>
          <Button onClick={() => setSimilarCustomerDialogOpen(false)}>Cancelar</Button>
        </DialogActions>
      </Dialog>

      {/* Add pending sales dialog */}
      <PendingSalesDialog
        open={pendingSalesDialogOpen}
        onClose={() => setPendingSalesDialogOpen(false)}
        customerName={selectedCustomerName}
        pendingSales={selectedCustomerPendingSales}
        loading={pendingSalesLoading}
        error={pendingSalesError}
        onMarkAsPaid={handleMarkSaleAsPaid}
        onPrintReceipt={(saleId) => console.log('Print receipt', saleId)}
        onViewSaleDetails={handleViewSaleDetails}
      />
    </Box>
  );
};

export default Customers; 