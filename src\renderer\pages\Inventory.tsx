import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Badge from '@mui/material/Badge';
import Chip from '@mui/material/Chip';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import { useTheme } from '@mui/material/styles';
import { 
  Add as AddIcon, 
  Search as SearchIcon,
  FilterList as FilterIcon,
  WarningAmber as WarningAmberIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';
import { ProductCard } from '../components/Inventory/ProductCard';
import { ProductFormDialog } from '../components/Inventory/ProductFormDialog';
import { 
  Product, 
  ProductFormData, 
  getStockStatus,
  productCategories
} from '../types/inventory';
import { useProducts } from '../hooks/useProducts';
// Import React Virtualized components
import {
  WindowScroller,
  AutoSizer,
  List,
  CellMeasurer,
  CellMeasurerCache
} from 'react-virtualized';

// Custom hook for debouncing values
const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set debouncedValue to value after the specified delay
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cancel the timeout if value changes or unmount
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Helper function to adapt database model to frontend type
const adaptProduct = (dbProduct: any): Product => {
  // Verificar se dbProduct é nulo ou indefinido
  if (!dbProduct) {
    console.warn('Recebido produto nulo ou indefinido em adaptProduct');
    // Retornar um produto vazio para evitar erros
    return {
      id: -1,
      name: '',
      category: '',
      description: null,
      price: 0,
      cost_price: 0,
      stock_quantity: 0,
      min_stock_level: 0,
      is_deleted: false,
      created_at: new Date(),
      updated_at: new Date()
    };
  }
  
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    category: dbProduct.category,
    description: dbProduct.description,
    price: dbProduct.price,
    cost_price: dbProduct.cost_price || 0, // Incluindo o preço de custo
    stock_quantity: dbProduct.stock_quantity,
    min_stock_level: dbProduct.min_stock_level,
    is_deleted: dbProduct.is_deleted || false,
    created_at: dbProduct.created_at,
    updated_at: dbProduct.updated_at
  };
};

type FilterOption = 'all' | 'low_stock' | string;

const Inventory: React.FC = () => {
  const theme = useTheme();
  // Use the products hook
  const { 
    products: dbProducts, 
    loading, 
    error: apiError,
    createProduct,
    updateProduct,
    updateProductStock: updateStockInDb,
    deleteProduct: deleteProductFromDb,
    refetch,
    setProductsState
  } = useProducts();

  // Convert database products to frontend types - com segurança para valores nulos
  const products = useMemo(() => {
    if (!dbProducts || !Array.isArray(dbProducts)) {
      console.warn('dbProducts é nulo, indefinido ou não é um array');
      return [];
    }
    return dbProducts.filter(Boolean).map(adaptProduct);
  }, [dbProducts]);

  const [searchInput, setSearchInput] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchInput, 150); // 150ms debounce delay
  const [filterOption, setFilterOption] = useState<FilterOption>('all');
  
  // Optimize filtering with useMemo instead of state+useEffect
  const filteredProducts = useMemo(() => {
    let filtered = [...products]; // Start with adapted products
    
    // Apply filter
    if (filterOption === 'low_stock') {
      filtered = filtered.filter(product => getStockStatus(product) === 'low');
    } else if (filterOption !== 'all') {
      filtered = filtered.filter(product => product.category === filterOption);
    }
    
    // Apply search
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(lowerSearchTerm) ||
        product.description?.toLowerCase().includes(lowerSearchTerm) ||
        product.category.toLowerCase().includes(lowerSearchTerm)
      );
    }
    
    return filtered;
  }, [products, filterOption, searchTerm]);
  
  const [formOpen, setFormOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | undefined>(undefined);
  
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Add CellMeasurerCache for variable height rows
  const cache = useMemo(() => new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 280, // Optimized estimate based on typical ProductCard rendering height
    minHeight: 250,
    // Using keyMapper to optimize re-measuring when the same item is rendered in different positions
    keyMapper: (rowIndex: number, columnIndex: number) => {
      const itemIndex = rowIndex * getColumnCount(window.innerWidth) + columnIndex;
      return itemIndex < filteredProducts.length ? `product-${filteredProducts[itemIndex].id}` : itemIndex;
    },
  }), [filteredProducts]); // Add filteredProducts as a dependency

  // Reset cache when filtered products change
  useEffect(() => {
    cache.clearAll();
  }, [filteredProducts, cache]);

  // Calculate number of columns based on screen width
  const getColumnCount = (width: number) => {
    if (width < 600) return 1; // Mobile view
    if (width < 960) return 2; // Tablet view
    return 3; // Desktop view - 3 columns
  };

  // Calculate row count based on items and columns
  const getRowCount = (itemCount: number, columnCount: number) => {
    return Math.ceil(itemCount / columnCount);
  };

  // Get item at index accounting for column layout
  const getItemAtIndex = (items: Product[], rowIndex: number, columnIndex: number, columnCount: number) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    return itemIndex < items.length ? items[itemIndex] : null;
  };

  // Calculate low stock items count
  const lowStockCount = products.filter(product => 
    getStockStatus(product) === 'low'
  ).length;

  // Update searchTerm when debouncedSearchTerm changes
  useEffect(() => {
    setSearchTerm(debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(event.target.value);
  }, []);

  const handleFilterChange = useCallback((_event: React.SyntheticEvent, newValue: FilterOption) => {
    setFilterOption(newValue);
  }, []);

  const handleAddProduct = useCallback(() => {
    setSelectedProduct(undefined);
    setFormOpen(true);
  }, []);

  const handleEditProduct = useCallback((product: Product) => {
    setSelectedProduct(product);
    setFormOpen(true);
  }, []);

  const handleSaveProduct = useCallback(async (productData: ProductFormData) => {
    try {
      // Define o tipo explicitamente para evitar erro de tipagem implícita
      let newOrUpdatedProduct: any = null;
      
      if (selectedProduct) {
        // Update existing product
        newOrUpdatedProduct = await updateProduct(selectedProduct.id, productData);
        
        if (!newOrUpdatedProduct) {
          throw new Error('Falha ao atualizar o produto');
        }
        
        // Update local state instead of refetching
        const updatedProducts = dbProducts.map(product => {
          if (product && product.id === selectedProduct.id) {
            return newOrUpdatedProduct;
          }
          return product;
        });
        
        // Update the products in the hook's state
        setProductsState(updatedProducts);
      } else {
        // Add new product
        newOrUpdatedProduct = await createProduct(productData);
        
        if (!newOrUpdatedProduct) {
          throw new Error('Falha ao criar um novo produto');
        }
        
        // Add to local state instead of refetching
        setProductsState([...dbProducts, newOrUpdatedProduct]);
      }
      
      setFormOpen(false);
    } catch (error) {
      console.error('Error saving product:', error);
      // You might want to show an error message to the user here
    }
  }, [selectedProduct, updateProduct, createProduct, dbProducts, setProductsState]);

  const handleDeleteProduct = useCallback((productId: number) => {
    setProductToDelete(productId);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeleteProduct = useCallback(async () => {
    if (productToDelete) {
      try {
        await deleteProductFromDb(productToDelete);
        
        // Update local state instead of refetching
        const updatedProducts = dbProducts.filter(product => product.id !== productToDelete);
        setProductsState(updatedProducts);
        
        setDeleteDialogOpen(false);
        setProductToDelete(null);
      } catch (error) {
        console.error('Error deleting product:', error);
        // Add a clearer error message for the user
        setError("Não foi possível excluir este produto. Uma venda existente faz referência a ele. O produto foi marcado como excluído e não aparecerá mais na lista de produtos disponíveis.");
      }
    }
  }, [productToDelete, deleteProductFromDb, dbProducts, setProductsState, setError]);

  const handleUpdateStock = useCallback(async (productId: number, amount: number) => {
    try {
      await updateStockInDb(productId, amount);
      
      // Update local state instead of refetching all products
      const updatedProducts = dbProducts.map(product => {
        if (product && product.id === productId) {
          return {
            ...product,
            stock_quantity: product.stock_quantity + amount
          };
        }
        return product;
      });
      
      // Update the products in the hook's state
      setProductsState(updatedProducts);
    } catch (error) {
      console.error('Error updating stock:', error);
      // You might want to show an error message to the user here
    }
  }, [updateStockInDb, dbProducts, setProductsState]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (apiError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar produtos: {apiError}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InventoryIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 'bold', 
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -8,
                    left: 0,
                    width: 60,
                    height: 4,
                    backgroundColor: theme.palette.primary.main,
                    borderRadius: 2,
                  }
                }}
              >
                Inventário
              </Typography>
            </Box>
            <Button 
              variant="contained" 
              color="primary" 
              startIcon={<AddIcon />}
              onClick={handleAddProduct}
            >
              Adicionar Produto
            </Button>
          </Box>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Alert 
              severity="info" 
              onClose={() => setError(null)}
              sx={{ mb: 2 }}
            >
              {error}
            </Alert>
          </Grid>
        )}

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <TextField
              fullWidth
              placeholder="Buscar produtos por nome, descrição ou categoria"
              variant="outlined"
              value={searchInput}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={filterOption}
              onChange={handleFilterChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab 
                label="Todos os Produtos" 
                value="all" 
              />
              <Tab 
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <WarningAmberIcon color="warning" sx={{ mr: 0.5, fontSize: 20 }} />
                    Estoque Baixo {lowStockCount > 0 ? `(${lowStockCount})` : ''}
                  </Box>
                } 
                value="low_stock" 
              />
              {productCategories.map(category => (
                <Tab 
                  key={category} 
                  label={category} 
                  value={category} 
                />
              ))}
            </Tabs>
          </Paper>
        </Grid>

        {filteredProducts.length === 0 ? (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Typography variant="h6" color="text.secondary">
                Nenhum produto encontrado. {filterOption !== 'all' ? 'Tente um filtro diferente.' : 'Adicione um novo produto para começar.'}
              </Typography>
            </Box>
          </Grid>
        ) : (
          <Grid item xs={12}>
            <Box sx={{ height: 'calc(100vh - 280px)', width: '100%' }}>
              <WindowScroller>
                {({ height, isScrolling, onChildScroll, scrollTop }) => (
                  <AutoSizer disableHeight>
                    {({ width }) => {
                      const columnCount = getColumnCount(width);
                      const rowCount = getRowCount(filteredProducts.length, columnCount);
                      
                      return (
                        <List
                          autoHeight
                          height={height || 500}
                          isScrolling={isScrolling}
                          onScroll={onChildScroll}
                          rowCount={rowCount}
                          rowHeight={cache.rowHeight}
                          scrollTop={scrollTop}
                          width={width}
                          overscanRowCount={3}
                          deferredMeasurementCache={cache}
                          style={{
                            outline: 'none',
                            paddingBottom: '20px',
                            overflowX: 'hidden'
                          }}
                          rowRenderer={({ index, key, style, parent }) => {
                            return (
                              <CellMeasurer
                                cache={cache}
                                columnIndex={0}
                                key={key}
                                parent={parent}
                                rowIndex={index}
                              >
                                {({ measure }) => (
                                  <div 
                                    style={{ 
                                      ...style, 
                                      padding: '8px',
                                      boxSizing: 'border-box',
                                      display: 'flex'
                                    }}
                                  >
                                    {/* Create a row with potentially multiple columns */}
                                    {Array.from({ length: columnCount }).map((_, colIndex) => {
                                      const product = getItemAtIndex(filteredProducts, index, colIndex, columnCount);
                                      if (!product) return <div key={colIndex} style={{ flex: 1 }} />;
                                      
                                      return (
                                        <div 
                                          key={colIndex} 
                                          style={{ 
                                            flex: 1, 
                                            padding: '0 8px',
                                            boxSizing: 'border-box'
                                          }}
                                        >
                                          <ProductCard 
                                            product={product}
                                            onEdit={() => handleEditProduct(product)}
                                            onDelete={() => handleDeleteProduct(product.id)}
                                            onUpdateStock={handleUpdateStock}
                                          />
                                        </div>
                                      );
                                    })}
                                  </div>
                                )}
                              </CellMeasurer>
                            );
                          }}
                        />
                      );
                    }}
                  </AutoSizer>
                )}
              </WindowScroller>
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Product form dialog */}
      <ProductFormDialog 
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSave={handleSaveProduct}
        product={selectedProduct}
        title={selectedProduct ? 'Editar Produto' : 'Adicionar Novo Produto'}
      />

      {/* Confirm delete dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Tem certeza que deseja excluir este produto? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancelar</Button>
          <Button onClick={confirmDeleteProduct} color="error">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Inventory; 