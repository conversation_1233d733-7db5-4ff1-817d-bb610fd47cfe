import React, { forwardRef } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Grid from '@mui/material/Grid';
import Chip from '@mui/material/Chip';
import Alert from '@mui/material/Alert';
import Badge from '@mui/material/Badge';
import {
  CardGiftcard as CardGiftcardIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import {
  Sale,
  SaleItem,
  formatCurrency,
  formatDate,
  paymentMethodLabels
} from '../../types/sales';

interface ReceiptProps {
  sale: Sale;
  storeName?: string;
  storeAddress?: string;
  storePhone?: string;
  storeEmail?: string;
  storeWebsite?: string;
  storeCnpj?: string;
  additionalInfo?: string;
}

export const Receipt = forwardRef<HTMLDivElement, ReceiptProps>(({
  sale,
  storeName = 'Pet Shop Management',
  storeAddress = '123 Main St, Anytown, USA',
  storePhone = '(*************',
  storeEmail = '<EMAIL>',
  storeWebsite = 'www.petshop.com',
  storeCnpj = '',
  additionalInfo = 'Obrigado pela preferência!'
}, ref) => {
  // Check if any sale items use packages
  const hasPackageItems = sale.items.some(item => !!item.customer_package_id || !!item.is_package_service);
  
  // Check if ALL items are package items
  const isAllPackageItems = hasPackageItems && sale.items.every(item => !!item.customer_package_id || !!item.is_package_service);
  
  const getItemName = (item: SaleItem): string => {
    // First try the direct references (most reliable)
    if (item.product && item.product.name) {
      return item.product.name;
    }
    
    if (item.service && item.service.name) {
      return item.service.name;
    }
    
    // Try to give a more descriptive fallback name
    if (item.productId || item.product_id) {
      return `Produto #${item.productId || item.product_id}`;
    }
    
    if (item.serviceId || item.service_id) {
      return `Serviço #${item.serviceId || item.service_id}`;
    }
    
    // Ultimate fallback
    return 'Item Desconhecido';
  };

  const getItemType = (item: SaleItem): string => {
    // Check for package services first
    if (item.customer_package_id || item.is_package_service) {
      return 'Serviço via Pacote';
    }
    
    // Most reliable check - if we have the object
    if (item.product) return 'Produto';
    if (item.service) return 'Serviço';
    
    // Fallback to ID checks
    if (item.productId || item.product_id) return 'Produto';
    if (item.serviceId || item.service_id) return 'Serviço';
    
    return 'Desconhecido';
  };

  // Check if an item is from a package
  const isPackageItem = (item: SaleItem): boolean => {
    return !!item.customer_package_id || !!item.is_package_service;
  };

  return (
    <Paper 
      ref={ref}
      sx={{ 
        p: 4, 
        maxWidth: '800px', 
        mx: 'auto',
        '@media print': {
          boxShadow: 'none',
          p: 0
        }
      }}
    >
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {storeName}
        </Typography>
        <Typography variant="body2" gutterBottom>
          {storeAddress}
        </Typography>
        <Typography variant="body2" gutterBottom>
          Telefone: {storePhone} | Email: {storeEmail}
        </Typography>
        <Typography variant="body2" gutterBottom>
          {storeWebsite}
        </Typography>
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      {/* Receipt Info */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6}>
          <Typography variant="subtitle2">Recibo #:</Typography>
          <Typography variant="body1">{sale.id}</Typography>
        </Grid>
        <Grid item xs={6} sx={{ textAlign: 'right' }}>
          <Typography variant="subtitle2">Data:</Typography>
          <Typography variant="body1">{formatDate(sale.sale_date.toString())}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Typography variant="subtitle2">Cliente:</Typography>
          <Typography variant="body1">{sale.customer?.name || 'Cliente Avulso'}</Typography>
        </Grid>
        <Grid item xs={12}>
          {hasPackageItems ? (
            <Box>
              <Typography variant="subtitle2">Tipo de Transação:</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                <Chip 
                  size="small" 
                  label="Serviço Pré-pago" 
                  color="primary"
                  icon={<CardGiftcardIcon />}
                />
              </Box>
            </Box>
          ) : (
            <>
              <Typography variant="subtitle2">Método de Pagamento:</Typography>
              <Typography variant="body1">
                {paymentMethodLabels[sale.payment_method]}
              </Typography>
            </>
          )}
          {hasPackageItems && isAllPackageItems && (
            <Typography variant="body1" sx={{ mt: 0.5 }}>
              Sem cobrança - Serviço proveniente de pacote pré-pago
            </Typography>
          )}
        </Grid>
      </Grid>
      
      {/* Items */}
      <Typography variant="h6" gutterBottom>
        Itens
      </Typography>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell><Typography variant="subtitle2">Item</Typography></TableCell>
            <TableCell><Typography variant="subtitle2">Tipo</Typography></TableCell>
            <TableCell align="center"><Typography variant="subtitle2">Qtd</Typography></TableCell>
            <TableCell align="right"><Typography variant="subtitle2">Preço</Typography></TableCell>
            <TableCell align="right"><Typography variant="subtitle2">Total</Typography></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sale.items.map((item, index) => (
            <TableRow 
              key={`item-${item.id || index}`}
              sx={isPackageItem(item) ? { 
                backgroundColor: 'rgba(144, 202, 249, 0.1)', 
                borderLeft: '2px solid #2196f3'
              } : {}}
            >
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getItemName(item)}
                  {isPackageItem(item) && (
                    <Chip
                      icon={<CardGiftcardIcon fontSize="small" />}
                      label="Pacote"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ fontWeight: 'medium' }}
                    />
                  )}
                </Box>
              </TableCell>
              <TableCell>
                {getItemType(item)}
              </TableCell>
              <TableCell align="center">{item.quantity}</TableCell>
              <TableCell align="right">
                {isPackageItem(item) ? (
                  <Typography variant="body2" color="primary">
                    -
                  </Typography>
                ) : (
                  formatCurrency(item.price_per_unit)
                )}
              </TableCell>
              <TableCell align="right">
                {isPackageItem(item) ? (
                  <Typography variant="body2" color="primary">
                    -
                  </Typography>
                ) : (
                  formatCurrency(item.quantity * item.price_per_unit)
                )}
              </TableCell>
            </TableRow>
          ))}
          <TableRow key="total-row">
            <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>
              Total:
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 'bold' }}>
              {hasPackageItems && isAllPackageItems 
                ? <Typography variant="body2" color="primary">Serviços de pacote pré-pago</Typography>
                : formatCurrency(sale.total_amount)}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      
      <Divider sx={{ my: 3 }} />
      
      {/* Footer */}
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        {storeCnpj && (
          <Typography variant="body2" gutterBottom>
            CNPJ: {storeCnpj}
          </Typography>
        )}
        <Typography variant="body2" gutterBottom>
          {additionalInfo}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Para dúvidas ou reclamações, entre em contato pelo telefone {storePhone}.
        </Typography>
      </Box>
    </Paper>
  );
});

Receipt.displayName = 'Receipt'; 