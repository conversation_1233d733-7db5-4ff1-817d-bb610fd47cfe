import { En<PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, OneToMany } from 'typeorm';
import { Customer } from './Customer';
import { SaleItem } from './SaleItem';

@Entity('sales')
export class Sale {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Customer)
  customer!: Customer;

  @Column('decimal', { precision: 10, scale: 2 })
  total_amount!: number;

  @Column()
  payment_method!: string;

  @Column({ default: 'paid' })
  status!: string;

  @CreateDateColumn()
  sale_date!: Date;

  @OneToMany(() => SaleItem, saleItem => saleItem.sale)
  items!: SaleItem[];
} 