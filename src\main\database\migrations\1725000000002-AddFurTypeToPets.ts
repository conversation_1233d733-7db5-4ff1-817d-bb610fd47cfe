import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFurTypeToPets1725000000002 implements MigrationInterface {
    name = 'AddFurTypeToPets1725000000002';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add fur_type column to pets table
        await queryRunner.query(`ALTER TABLE "pets" ADD COLUMN "fur_type" TEXT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove fur_type column if migration needs to be reverted
        await queryRunner.query(`ALTER TABLE "pets" DROP COLUMN "fur_type"`);
    }
} 