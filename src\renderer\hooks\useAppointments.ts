import { useState, useEffect, useCallback } from 'react';
import { Appointment } from '../../main/database/models/Appointment';

// Add a cache interface for date range queries
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresIn: number; // milliseconds
}

interface DateRangeCache {
  [key: string]: CacheEntry<Appointment[]>;
}

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

interface UseAppointmentsResult {
  appointments: Appointment[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getAppointmentById: (id: number) => Promise<Appointment | null>;
  getAppointmentsByCustomerId: (customerId: number) => Promise<Appointment[]>;
  getAppointmentsByPetId: (petId: number) => Promise<Appointment[]>;
  getUpcomingAppointments: () => Promise<Appointment[]>;
  getAppointmentsByDateRange: (startDate: Date, endDate: Date) => Promise<Appointment[]>;
  createAppointment: (appointmentData: Partial<Appointment>) => Promise<Appointment | null>;
  updateAppointment: (id: number, appointmentData: Partial<Appointment>) => Promise<Appointment | null>;
  updateAppointmentStatus: (id: number, status: string) => Promise<Appointment | null>;
  deleteAppointment: (id: number) => Promise<boolean>;
}

export const useAppointments = (): UseAppointmentsResult => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Create a cache for date range queries
  const [dateRangeCache, setDateRangeCache] = useState<DateRangeCache>({});

  // Add a memoized function for updating the cache
  const updateDateRangeCache = useCallback((cacheKey: string, data: Appointment[]) => {
    setDateRangeCache(prevCache => ({
      ...prevCache,
      [cacheKey]: {
        data,
        timestamp: Date.now(),
        expiresIn: CACHE_EXPIRATION
      }
    }));
  }, []);
  
  // Simple helper function with no dependencies
  const checkCacheEntry = (entry?: CacheEntry<any>): boolean => {
    if (!entry) return false;
    
    const now = Date.now();
    return (now - entry.timestamp) < entry.expiresIn;
  };

  // Add helper to get cache key for date range - no need to memoize this
  const getDateRangeCacheKey = (startDate: Date, endDate: Date): string => {
    return `${startDate.toISOString()}_${endDate.toISOString()}`;
  };

  const fetchAppointments = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('appointments:getAll');
      if (response.success) {
        // Sort appointments by date, newest first
        const sortedAppointments = [...response.data].sort((a, b) => {
          const dateA = new Date(a.appointment_date).getTime();
          const dateB = new Date(b.appointment_date).getTime();
          return dateB - dateA; // Descending order (newest first)
        });
        setAppointments(sortedAppointments);
      } else {
        setError(response.error || 'Failed to fetch appointments');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const getAppointmentById = useCallback(async (id: number): Promise<Appointment | null> => {
    try {
      const response = await window.electronAPI.invoke('appointments:getById', id);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch appointment with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const getAppointmentsByCustomerId = useCallback(async (customerId: number): Promise<Appointment[]> => {
    try {
      const response = await window.electronAPI.invoke('appointments:getByCustomerId', customerId);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch appointments for customer ID ${customerId}`);
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  const getAppointmentsByPetId = useCallback(async (petId: number): Promise<Appointment[]> => {
    try {
      const response = await window.electronAPI.invoke('appointments:getByPetId', petId);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch appointments for pet ID ${petId}`);
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  const getUpcomingAppointments = useCallback(async (): Promise<Appointment[]> => {
    try {
      const response = await window.electronAPI.invoke('appointments:getUpcoming');
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Failed to fetch upcoming appointments');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  const getAppointmentsByDateRange = useCallback(async (startDate: Date, endDate: Date): Promise<Appointment[]> => {
    try {
      // Create a cache key for this date range
      const cacheKey = getDateRangeCacheKey(startDate, endDate);
      
      // Check if we have a valid cached result - directly access the cache
      const cacheEntry = dateRangeCache[cacheKey];
      if (checkCacheEntry(cacheEntry)) {
        return cacheEntry!.data;
      }
      
      // If not in cache or expired, fetch from the API
      const response = await window.electronAPI.invoke(
        'appointments:getByDateRange', 
        startDate.toISOString(), 
        endDate.toISOString()
      );
      
      if (response.success) {
        // Store in cache
        updateDateRangeCache(cacheKey, response.data);
        return response.data;
      }
      
      setError(response.error || 'Failed to fetch appointments by date range');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, [dateRangeCache, updateDateRangeCache]);

  const createAppointment = useCallback(async (appointmentData: Partial<Appointment>): Promise<Appointment | null> => {
    try {
      const response = await window.electronAPI.invoke('appointments:create', appointmentData);
      if (response.success) {
        await fetchAppointments(); // Refresh the list
        
        // Clear the date range cache as data has changed
        setDateRangeCache({});
        
        return response.data;
      }
      setError(response.error || 'Failed to create appointment');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchAppointments]);

  const updateAppointment = useCallback(async (
    id: number, 
    appointmentData: Partial<Appointment>
  ): Promise<Appointment | null> => {
    try {
      const response = await window.electronAPI.invoke('appointments:update', id, appointmentData);
      if (response.success) {
        await fetchAppointments(); // Refresh the list
        
        // Clear the date range cache as data has changed
        setDateRangeCache({});
        
        return response.data;
      }
      setError(response.error || `Failed to update appointment with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchAppointments]);

  const updateAppointmentStatus = useCallback(async (id: number, status: string): Promise<Appointment | null> => {
    try {
      const response = await window.electronAPI.invoke('appointments:updateStatus', id, status);
      if (response.success) {
        await fetchAppointments(); // Refresh the list
        
        // Clear the date range cache as data has changed
        setDateRangeCache({});
        
        return response.data;
      }
      setError(response.error || `Failed to update status for appointment with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchAppointments]);

  const deleteAppointment = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('appointments:delete', id);
      if (response.success) {
        await fetchAppointments(); // Refresh the list
        
        // Clear the date range cache as data has changed
        setDateRangeCache({});
        
        return true;
      }
      setError(response.error || `Failed to delete appointment with ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchAppointments]);

  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  return {
    appointments,
    loading,
    error,
    refetch: fetchAppointments,
    getAppointmentById,
    getAppointmentsByCustomerId,
    getAppointmentsByPetId,
    getUpcomingAppointments,
    getAppointmentsByDateRange,
    createAppointment,
    updateAppointment,
    updateAppointmentStatus,
    deleteAppointment
  };
}; 