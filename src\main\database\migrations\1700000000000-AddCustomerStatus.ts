import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCustomerStatus1700000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE customers
            ADD COLUMN status TEXT NOT NULL DEFAULT 'active'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE customers
            DROP COLUMN status
        `);
    }
} 