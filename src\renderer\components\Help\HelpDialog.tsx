import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Grid from '@mui/material/Grid'; // Added for layout
import Paper from '@mui/material/Paper'; // Added for card-like sections
import Chip from '@mui/material/Chip'; // Added for tags/status

import {
  Close as CloseIcon,
  HelpOutline as HelpIcon, // Changed from Help for consistency
  Dashboard as DashboardIcon,
  Pets as PetsIcon,
  People as PeopleIcon,
  Inventory as InventoryIcon,
  ShoppingCart as SalesIcon,
  EventAvailable as AppointmentsIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  AttachMoney as MoneyIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  DateRange as DateRangeIcon,
  Today as TodayIcon,
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Home as HomeIcon,
  Notes as NotesIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  CalendarMonth as CalendarMonthIcon,
  ViewList as ViewListIcon,
  Schedule as ScheduleIcon,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  TableChart as TableChartIcon,
  Security as SecurityIcon,
  ColorLens as ColorLensIcon,
  Healing as HealingIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ArrowForward as ArrowForwardIcon,
  Person as PersonIcon,
  Payment as PaymentIcon,
  CardGiftcard as CardGiftcardIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  Wc as WcIcon,
  ContentCut as ContentCutIcon,
  PhotoCamera as PhotoIcon, // Changed from Photo
  Speed as SpeedIcon,
  GridView as GridViewIcon,
  Verified as VerifiedIcon,
  FormatListNumbered as FormatListNumberedIcon,
  Check as CheckIcon,
  Recommend as RecommendIcon,
  CalendarToday as CalendarTodayIcon,
  // AttachMoney as AttachMoneyIcon, // Already imported
  ArrowBack as ArrowBackIcon,
  ErrorOutline as ErrorOutlineIcon,
  Notifications as NotificationsIcon,
  Backup as BackupIcon,
  Smartphone as SmartphoneIcon,
  Language as LanguageIcon,
  EventNote as EventNoteIcon,
  Autorenew as AutorenewIcon,
  Cancel as CancelIcon,
  PersonOff as PersonOffIcon,
  TipsAndUpdates as TipsAndUpdatesIcon,
  // Payments as PaymentsIcon, // Already imported
  AccessTime as AccessTimeIcon,
  Lightbulb as LightbulbIcon,
  Message as MessageIcon,
  Redeem as RedeemIcon, // For packages
  ReceiptLong as ReceiptLongIcon, // For receipts
  AddShoppingCart as AddShoppingCartIcon, // For new sale
  ListAlt as ListAltIcon, // For service list
  DonutSmall as DonutSmallIcon, // for pet type/breed
  LocalOffer as LocalOfferIcon, // for pet size/tag
  Description as DescriptionIcon, // for descriptions
  Event as EventIcon, // for calendar events
  Style as StyleIcon, // For product categories/pet type
  ConfirmationNumber as ConfirmationNumberIcon, // For PIN
  Category as CategoryIcon, // For product category
  BarChart as BarChartReportIcon, // For bar chart in reports
  PieChart as PieChartReportIcon, // For pie chart in reports
  ShowChart as ShowChartIcon, // For line chart in reports
  AccountBalanceWallet as AccountBalanceWalletIcon, // For 'Fiado'
  Replay as ReplayIcon, // For reactivate
  Block as BlockIcon, // For deactivate
  LowPriority as LowPriorityIcon, // For low stock
  HourglassEmpty as HourglassEmptyIcon, // For duration
  Build as BuildIcon, // For services
  CloudDownload as CloudDownloadIcon, // For backup export
  CloudUpload as CloudUploadIcon, // For backup import
} from '@mui/icons-material';
import { useHelp } from '../../contexts/HelpContext';
import { CalendarIcon } from '@mui/x-date-pickers';

const HelpDialog: React.FC = () => {
  const { isHelpOpen, closeHelp, currentPagePath } = useHelp();

  const getDialogTitle = () => {
    switch (currentPagePath) {
      case '/':
      case '/dashboard':
        return 'Ajuda do Dashboard';
      case '/inventory':
        return 'Ajuda do Inventário';
      case '/customers':
        return 'Ajuda de Clientes';
      case '/pets':
        return 'Ajuda de Pets';
      case '/packages':
        return 'Ajuda de Pacotes';
      case '/sales':
        return 'Ajuda de Vendas';
      case '/appointments':
        return 'Ajuda de Agendamentos';
      case '/reports':
        return 'Ajuda de Relatórios';
      case '/settings':
        return 'Ajuda de Configurações';
      default:
        return 'Ajuda Geral';
    }
  };

  const getDialogIcon = () => {
    switch (currentPagePath) {
      case '/':
      case '/dashboard':
        return <DashboardIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/inventory':
        return <InventoryIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/customers':
        return <PeopleIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/pets':
        return <PetsIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/packages':
        return <RedeemIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/sales':
        return <SalesIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/appointments':
        return <AppointmentsIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/reports':
        return <ReportsIcon color="primary" sx={{ fontSize: 28 }} />;
      case '/settings':
        return <SettingsIcon color="primary" sx={{ fontSize: 28 }} />;
      default:
        return <HelpIcon color="primary" sx={{ fontSize: 28 }} />;
    }
  };

  const Section: React.FC<{ title: string; icon?: React.ReactNode; children: React.ReactNode }> = ({ title, icon, children }) => (
    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
        {icon && <Box sx={{ mr: 1.5, display: 'flex', color: 'primary.main' }}>{icon}</Box>}
        <Typography variant="h6" component="h3" color="primary.main" sx={{ fontWeight: 'bold' }}>
          {title}
        </Typography>
      </Box>
      <Divider sx={{ mb: 1.5 }} />
      {children}
    </Paper>
  );

  const HelpListItem: React.FC<{ icon: React.ReactNode; primary: string; secondary?: string | React.ReactNode }> = ({ icon, primary, secondary }) => (
    <ListItem sx={{ py: 0.5, alignItems: 'flex-start' }}>
      <ListItemIcon sx={{ minWidth: 36, mt: 0.5, color: 'primary.dark' }}>{icon}</ListItemIcon>
      <ListItemText primary={primary} secondary={secondary} primaryTypographyProps={{ fontWeight: 'medium' }} />
    </ListItem>
  );


  const renderDashboardHelp = () => (
    <Box>
      <Section title="Visão Geral do Dashboard" icon={<InfoIcon />}>
        <Typography paragraph>
          O Dashboard oferece um resumo visual das principais métricas e atividades do seu pet shop.
          Alterne entre a visualização de "Hoje" e "Semana" para analisar dados em diferentes períodos.
        </Typography>
      </Section>

      <Section title="Indicadores Chave" icon={<SpeedIcon />}>
        <Typography paragraph>
          Acompanhe rapidamente:
        </Typography>
        <List dense>
          <HelpListItem icon={<MoneyIcon />} primary="Receita" secondary="Total de receita gerada no período. Clique para ir aos Relatórios." />
          <HelpListItem icon={<AppointmentsIcon />} primary="Agendamentos" secondary="Número de agendamentos. Clique para gerenciar Agendamentos." />
          <HelpListItem icon={<WarningIcon />} primary="Estoque Baixo" secondary="Produtos que precisam de reposição. Clique para ir ao Inventário." />
          <HelpListItem icon={<PeopleIcon />} primary="Clientes Cadastrados" secondary="Total de clientes no sistema. Clique para ver Clientes." />
        </List>
      </Section>

      <Section title="Gráficos Detalhados" icon={<BarChartReportIcon />}>
        <Typography paragraph>
          Visualize o desempenho com gráficos interativos:
        </Typography>
        <List dense>
          <HelpListItem icon={<TrendingUpIcon />} primary="Evolução da Receita" secondary="Acompanhe a receita de Serviços, Produtos e Pacotes. 'Hoje' mostra por hora, 'Semana' por dia." />
          <HelpListItem icon={<PieChartReportIcon />} primary="Serviços Populares" secondary="Distribuição dos serviços mais agendados. Clique no título para ir à Configuração de Serviços." />
          <HelpListItem icon={<InventoryIcon />} primary="Status do Estoque" secondary="Visão do estoque: Baixo, Ideal ou Excesso. Clique no título para ir ao Inventário." />
        </List>
      </Section>

      <Section title="Lista de Agendamentos" icon={<EventNoteIcon />}>
        <Typography paragraph>
          Veja os próximos agendamentos para o período selecionado. Ações rápidas:
        </Typography>
        <List dense>
          <HelpListItem icon={<EditIcon />} primary="Editar Agendamento" secondary="Modifique os detalhes do agendamento." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir Agendamento" secondary="Remova o agendamento (com confirmação)." />
          <HelpListItem icon={<CheckCircleIcon />} primary="Alterar Status" secondary="Atualize o status (Agendado, Confirmado, Concluído, etc.)." />
          <HelpListItem icon={<SalesIcon />} primary="Concluir e Gerar Venda" secondary="Ao marcar como 'Concluído', o uso de pacotes é registrado. Para agendamentos normais, o sistema perguntará se deseja criar uma venda." />
        </List>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <LightbulbIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Dica:</strong> Clique nos títulos dos gráficos ou nos cartões de indicadores para navegar diretamente para as seções relevantes do sistema.
        </Typography>
      </Box>
    </Box>
  );

  const renderInventoryHelp = () => (
    <Box>
      <Section title="Gerenciamento de Inventário" icon={<InfoIcon />}>
        <Typography paragraph>
          Controle todos os seus produtos, monitore níveis de estoque e gerencie categorias.
        </Typography>
      </Section>

      <Section title="Ações Principais" icon={<BuildIcon />}>
        <List dense>
          <HelpListItem icon={<AddIcon />} primary="Adicionar Produto" secondary="Cadastre novos itens, definindo nome, categoria, preços (venda e custo), estoque inicial e mínimo." />
          <HelpListItem icon={<SearchIcon />} primary="Buscar Produtos" secondary="Encontre produtos rapidamente por nome, descrição ou categoria." />
          <HelpListItem icon={<FilterListIcon />} primary="Filtrar Produtos" secondary="Use as abas para ver 'Todos', 'Estoque Baixo' ou produtos de uma 'Categoria' específica." />
        </List>
      </Section>

      <Section title="Cartões de Produto" icon={<StyleIcon />}>
        <Typography paragraph>
          Cada produto é exibido em um cartão com informações e ações:
        </Typography>
        <List dense>
          <HelpListItem icon={<DescriptionIcon />} primary="Detalhes" secondary="Nome, descrição, categoria, preços, quantidade em estoque." />
          <HelpListItem icon={<LowPriorityIcon />} primary="Status do Estoque" secondary="Indicador visual: Baixo (vermelho), Ideal (verde), Excesso (laranja)." />
          <HelpListItem icon={<AddIcon />} primary="Ajuste Rápido de Estoque" secondary="Use os botões '+' e '-' para adicionar ou remover unidades do estoque diretamente no cartão." />
          <HelpListItem icon={<EditIcon />} primary="Editar Produto" secondary="Acesse o formulário para modificar todas as informações do produto." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir Produto" secondary="Remove o produto. Se vinculado a vendas, será marcado como 'excluído' e ocultado, mas não removido do histórico." />
        </List>
      </Section>

      <Section title="Preço de Custo e Lucratividade" icon={<MoneyIcon />}>
        <Typography paragraph>
          O campo "Preço de Custo" no formulário do produto é crucial. Ele é usado para:
        </Typography>
        <List dense>
          <HelpListItem icon={<TrendingUpIcon />} primary="Calcular Lucratividade" secondary="Permite que a seção de Relatórios analise a margem de lucro de cada produto." />
          <HelpListItem icon={<ReportsIcon />} primary="Informar Decisões" secondary="Ajuda a identificar os produtos mais rentáveis e a otimizar sua estratégia de preços e estoque." />
        </List>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'warning.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <WarningIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Importante:</strong> Mantenha o "Preço de Custo" atualizado para garantir a precisão dos relatórios de lucratividade.
        </Typography>
      </Box>
    </Box>
  );

  const renderCustomersHelp = () => (
    <Box>
      <Section title="Gerenciamento de Clientes" icon={<InfoIcon />}>
        <Typography paragraph>
          Centralize as informações dos seus clientes, seus pets, pacotes adquiridos e histórico de pagamentos.
        </Typography>
      </Section>

      <Section title="Ações e Filtros" icon={<BuildIcon />}>
        <List dense>
          <HelpListItem icon={<AddIcon />} primary="Adicionar Cliente" secondary="Cadastre novos clientes com informações de contato e endereço." />
          <HelpListItem icon={<SearchIcon />} primary="Buscar Clientes" secondary="Encontre clientes por nome, email, telefone ou endereço." />
          <HelpListItem icon={<BlockIcon />} primary="Mostrar Inativos" secondary="Visualize clientes que foram desativados." />
          <HelpListItem icon={<PaymentIcon />} primary="Clientes Devendo" secondary="Filtre para ver apenas clientes com pagamentos pendentes (status 'Fiado'). Um contador no botão indica quantos existem." />
          <HelpListItem icon={<WarningIcon />} primary="Clientes com Pacotes Não Compareceu" secondary="Cartões de clientes com pacotes em espera devido a não comparecimento são destacados com um aviso." />
        </List>
      </Section>

      <Section title="Cartões de Cliente" icon={<PersonIcon />}>
        <Typography paragraph>
          Cada cliente possui um cartão com acesso rápido a:
        </Typography>
        <List dense>
          <HelpListItem icon={<EditIcon />} primary="Editar Informações" secondary="Atualize os dados cadastrais do cliente." />
          <HelpListItem icon={<PetsIcon />} primary="Ver Pets" secondary="Acesse a lista de pets do cliente, adicione novos ou veja detalhes." />
          <HelpListItem icon={<RedeemIcon />} primary="Ver Pacotes" secondary="Consulte os pacotes de serviços adquiridos pelo cliente e seu uso." />
          <HelpListItem icon={<PaymentIcon />} primary="Vendas Pendentes" secondary="Visualize e gerencie pagamentos em aberto do cliente." />
          <HelpListItem icon={<BlockIcon />} primary="Desativar Cliente" secondary="Move o cliente para a lista de inativos. Pets associados são ocultados e agendamentos ativos removidos." />
          <HelpListItem icon={<ReplayIcon />} primary="Reativar Cliente" secondary="(Para clientes inativos) Restaura o cliente para o status ativo." />
        </List>
      </Section>

      <Section title="Lidando com Clientes Inativos" icon={<PersonOffIcon />}>
        <Typography paragraph>
          Ao tentar adicionar um cliente com nome ou telefone similar a um cliente inativo, o sistema oferecerá opções:
        </Typography>
        <List dense>
          <HelpListItem icon={<ReplayIcon />} primary="Reativar e Atualizar" secondary="Reative o cliente inativo existente e atualize seus dados com as novas informações." />
          <HelpListItem icon={<AddIcon />} primary="Criar Novo Cliente" secondary="Ignore o cliente inativo e crie um novo registro." />
        </List>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <LightbulbIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Dica:</strong> Mantenha os dados dos clientes atualizados para facilitar a comunicação e o agendamento de serviços.
        </Typography>
      </Box>
    </Box>
  );

  const renderPackagesHelp = () => (
    <Box>
      <Section title="Gerenciamento de Pacotes de Serviços" icon={<InfoIcon />}>
        <Typography paragraph>
          Crie e administre pacotes de serviços para oferecer aos seus clientes, geralmente com um preço promocional.
        </Typography>
      </Section>

      <Section title="Ações Principais" icon={<BuildIcon />}>
        <List dense>
          <HelpListItem icon={<AddIcon />} primary="Criar Novo Pacote" secondary="Defina um novo pacote com nome, descrição, número de sessões, validade, serviços incluídos e preço." />
          <HelpListItem icon={<SearchIcon />} primary="Buscar Pacotes" secondary="Encontre pacotes por nome ou descrição." />
        </List>
      </Section>

      <Section title="Cartões de Pacote" icon={<RedeemIcon />}>
        <Typography paragraph>
          Cada pacote é detalhado em um cartão:
        </Typography>
        <List dense>
          <HelpListItem icon={<DescriptionIcon />} primary="Detalhes" secondary="Nome, descrição, quantidade de serviços, validade em dias." />
          <HelpListItem icon={<ListAltIcon />} primary="Serviços Incluídos" secondary="Lista dos serviços que compõem o pacote." />
          <HelpListItem icon={<MoneyIcon />} primary="Preço" secondary="Preço promocional do pacote e economia em relação ao preço regular dos serviços avulsos." />
          <HelpListItem icon={<EditIcon />} primary="Editar Pacote" secondary="Modifique as configurações do pacote." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir Pacote" secondary="Marca o pacote como inativo. Ele não poderá mais ser vendido, mas pacotes já adquiridos por clientes continuarão válidos. Requer confirmação." />
        </List>
      </Section>

      <Section title="Formulário de Pacote" icon={<StyleIcon />}>
        <Typography paragraph>
          Ao criar ou editar um pacote, preencha:
        </Typography>
        <List dense>
          <HelpListItem icon={<CardGiftcardIcon />} primary="Nome do Pacote" secondary="Identificador do pacote." />
          <HelpListItem icon={<MoneyIcon />} primary="Preço do Pacote" secondary="Valor total a ser pago pelo pacote." />
          <HelpListItem icon={<HealingIcon />} primary="Serviços Incluídos" secondary="Selecione os serviços que fazem parte da oferta." />
          <HelpListItem icon={<FormatListNumberedIcon />} primary="Total de Ocorrências" secondary="Número total de usos/sessões do pacote." />
          <HelpListItem icon={<CalendarIcon />} primary="Tipo de Frequência" secondary="Selecione se o pacote é semanal, mensal ou defina uma frequência personalizada." />
        </List>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'warning.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <WarningIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Atenção:</strong> "Excluir" um pacote na verdade o marca como inativo. Isso impede novas vendas desse pacote, mas não afeta os pacotes já comprados por clientes, que continuarão funcionando normalmente até sua validade ou uso total.
        </Typography>
      </Box>
    </Box>
  );

  const renderPetsHelp = () => (
    <Box>
      <Section title="Gerenciamento de Pets" icon={<InfoIcon />}>
        <Typography paragraph>
          Cadastre e gerencie as informações de todos os animais de estimação atendidos, incluindo seu histórico de serviços.
        </Typography>
      </Section>

      <Section title="Ações Principais" icon={<BuildIcon />}>
        <List dense>
          <HelpListItem icon={<AddIcon />} primary="Adicionar Pet" secondary="Cadastre um novo pet, associando-o a um cliente e preenchendo detalhes como nome, tipo, raça, idade, foto, etc." />
          <HelpListItem icon={<SearchIcon />} primary="Buscar Pets" secondary="Encontre pets por nome, tipo, raça ou nome do proprietário." />
        </List>
      </Section>

      <Section title="Cartões de Pet" icon={<PetsIcon />}>
        <Typography paragraph>
          Cada pet é exibido em um cartão com:
        </Typography>
        <List dense>
          <HelpListItem icon={<PhotoIcon />} primary="Foto do Pet" secondary="Imagem do animal (se cadastrada)." />
          <HelpListItem icon={<DescriptionIcon />} primary="Informações Básicas" secondary="Nome, tipo, raça, idade, porte, gênero, tipo de pelo." />
          <HelpListItem icon={<PersonIcon />} primary="Proprietário" secondary="Nome e contato do cliente responsável." />
          <HelpListItem icon={<NotesIcon />} primary="Observações" secondary="Notas adicionais sobre o pet." />
          <HelpListItem icon={<EditIcon />} primary="Editar Pet" secondary="Modifique as informações do pet." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir Pet" secondary="Remova o cadastro do pet (com confirmação)." />
          <HelpListItem icon={<HealingIcon />} primary="Ver Serviços" secondary="Acesse o histórico de serviços realizados para este pet. Nesta tela, você pode adicionar novos registros de serviço ou excluir existentes." />
        </List>
      </Section>

      <Section title="Registro de Serviços do Pet" icon={<ListAltIcon />}>
        <Typography paragraph>
          Ao clicar em "Ver Serviços" em um cartão de pet, você pode:
        </Typography>
        <List dense>
          <HelpListItem icon={<AddIcon />} primary="Adicionar Serviço Realizado" secondary="Registre um serviço específico que foi feito para o pet, selecionando o tipo de serviço (da lista de serviços cadastrados em Configurações), a data e notas." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir Registro de Serviço" secondary="Remova um serviço do histórico do pet." />
        </List>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <LightbulbIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Dica:</strong> Manter o histórico de serviços do pet atualizado pode ser útil para consultas futuras e para entender melhor as necessidades de cada animal.
        </Typography>
      </Box>
    </Box>
  );

  const renderAppointmentsHelp = () => (
    <Box>
      <Section title="Gerenciamento de Agendamentos" icon={<InfoIcon />}>
        <Typography paragraph>
          Organize e acompanhe todos os agendamentos de serviços para os pets. Alterne entre visualização em lista ou calendário.
        </Typography>
      </Section>

      <Section title="Ações Principais" icon={<BuildIcon />}>
        <List dense>
          <HelpListItem icon={<AddIcon />} primary="Adicionar Agendamento" secondary="Crie novos agendamentos, selecionando cliente, pet, serviço, data/hora e status inicial." />
          <HelpListItem icon={<SearchIcon />} primary="Buscar Agendamentos" secondary="Filtre por cliente, pet, serviço ou anotações." />
          <HelpListItem icon={<ViewListIcon />} primary="Visualização em Lista" secondary="Veja os agendamentos em cartões, ordenados por data (mais recentes primeiro)." />
          <HelpListItem icon={<CalendarMonthIcon />} primary="Visualização em Calendário" secondary="Visualize os agendamentos em um formato de calendário mensal. Clique em um evento para editar." />
        </List>
      </Section>

      <Section title="Cartões de Agendamento (Visualização em Lista)" icon={<StyleIcon />}>
        <Typography paragraph>
          Cada cartão de agendamento exibe:
        </Typography>
        <List dense>
          <HelpListItem icon={<PersonIcon />} primary="Cliente e Pet" />
          <HelpListItem icon={<HealingIcon />} primary="Serviço Agendado" />
          <HelpListItem icon={<ScheduleIcon />} primary="Data e Hora" />
          <HelpListItem icon={<InfoIcon />} primary="Status Atual" />
          <HelpListItem icon={<NotesIcon />} primary="Anotações" />
          <HelpListItem icon={<EditIcon />} primary="Editar" secondary="Modifique os detalhes do agendamento." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir" secondary="Remova o agendamento (com confirmação)." />
          <HelpListItem icon={<AutorenewIcon />} primary="Alterar Status" secondary="Atualize o progresso: Agendado, Confirmado, Concluído, Cancelado, Não Compareceu." />
        </List>
      </Section>

      <Section title="Status e Integrações" icon={<TrendingUpIcon />}>
        <Typography paragraph>
          Gerenciar o status é fundamental:
        </Typography>
        <List dense>
          <HelpListItem icon={<CheckCircleIcon />} primary="Concluído" secondary={
            <>
             Ao marcar um agendamento como 'Concluído':
              <ul style={{paddingLeft: '20px', margin: '4px 0'}}>
                <li>Se for um serviço de pacote, o uso é registrado automaticamente.</li>
                <li>Se for um serviço avulso, o sistema perguntará se você deseja criar uma venda, pré-preenchendo os dados na tela de Nova Venda.</li>
              </ul>
            </>
          }/>
          <HelpListItem icon={<PersonOffIcon />} primary="Não Compareceu" secondary="Registra a ausência do cliente. Se for um agendamentode pacote, o sistema pausa o pacote vinculado." />
        </List>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <LightbulbIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Dica:</strong> Use a visualização em calendário para ter uma visão clara da sua agenda e identificar horários disponíveis.
        </Typography>
      </Box>
    </Box>
  );

  const renderSalesHelp = () => (
    <Box>
      <Section title="Gerenciamento de Vendas" icon={<InfoIcon />}>
        <Typography paragraph>
          Registre e acompanhe todas as transações de produtos e serviços. Crie novas vendas, visualize o histórico, imprima recibos e gerencie o status dos pagamentos.
        </Typography>
      </Section>

      <Section title="Ações Principais" icon={<BuildIcon />}>
        <List dense>
          <HelpListItem icon={<AddShoppingCartIcon />} primary="Nova Venda" secondary="Inicie o registro de uma nova venda. Você será direcionado para o formulário de venda." />
          <HelpListItem icon={<SearchIcon />} primary="Buscar Vendas" secondary="Encontre vendas por cliente, método de pagamento, data, valor total ou itens vendidos." />
          <HelpListItem icon={<PaymentIcon />} primary="Filtrar Vendas Pendentes" secondary="Visualize apenas as vendas com status 'Pendente' (Fiado). Um contador no botão indica a quantidade." />
        </List>
      </Section>

      <Section title="Formulário de Nova Venda" icon={<StyleIcon />}>
        <Typography paragraph>
          Ao criar uma venda:
        </Typography>
        <List dense>
          <HelpListItem icon={<PersonIcon />} primary="Selecionar Cliente (Opcional)" secondary="Associe a venda a um cliente cadastrado." />
          <HelpListItem icon={<AddIcon />} primary="Adicionar Itens" secondary="Inclua produtos do inventário ou serviços." />
          <HelpListItem icon={<MoneyIcon />} primary="Definir Quantidade e Preço" secondary="Ajuste a quantidade. O preço é puxado do cadastro, mas pode ser editado." />
          <HelpListItem icon={<PaymentIcon />} primary="Método de Pagamento" secondary="Escolha como o cliente pagou (Dinheiro, Cartão, Pix, etc.)." />
          <HelpListItem icon={<InfoIcon />} primary="Status da Venda" secondary="Defina como 'Paga' ou 'Pendente' (Fiado)." />
          <HelpListItem icon={<CheckIcon />} primary="Finalizar Venda" secondary="Conclui o registro. O estoque de produtos é atualizado automaticamente." />
        </List>
      </Section>

      <Section title="Cartões de Venda (Histórico)" icon={<ListAltIcon />}>
        <Typography paragraph>
          Cada venda registrada é exibida em um cartão com:
        </Typography>
        <List dense>
          <HelpListItem icon={<DescriptionIcon />} primary="Detalhes da Venda" secondary="Número da venda, cliente, data, valor total, método de pagamento e status." />
          <HelpListItem icon={<HealingIcon />} primary="Itens Vendidos" secondary="Lista de produtos e serviços incluídos." />
          <HelpListItem icon={<PrintIcon />} primary="Imprimir Recibo" secondary="Gere um recibo detalhado para o cliente." />
          <HelpListItem icon={<AutorenewIcon />} primary="Atualizar Status" secondary="Altere o status de 'Pendente' para 'Paga' e vice-versa." />
          <HelpListItem icon={<DeleteIcon />} primary="Excluir Venda" secondary="Remova o registro da venda. Você terá a opção de restaurar (devolver) os produtos vendidos ao estoque ou não." />
          <HelpListItem icon={<WarningIcon />} primary="Alerta de Pagamento Pendente" secondary="Vendas com status 'Pendente' há mais tempo que o configurado em 'Configurações > Fiado' são destacadas." />
        </List>
        <Typography paragraph sx={{ mt: 1 }}>
          As vendas são carregadas progressivamente conforme você rola a lista para melhor performance.
        </Typography>
      </Section>

      <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <LightbulbIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Dica:</strong> Se você concluir um agendamento na tela de Agendamentos, pode ser redirecionado para 'Nova Venda' com os dados do cliente e serviço já preenchidos!
        </Typography>
      </Box>
    </Box>
  );

  const renderReportsHelp = () => (
    <Box>
      <Section title="Relatórios e Análises" icon={<InfoIcon />}>
        <Typography paragraph>
          Analise o desempenho do seu negócio com dados detalhados sobre vendas, lucratividade, clientes e estoque.
          Utilize os filtros de "Período" e "Tipo de Relatório" para focar nas informações desejadas.
        </Typography>
      </Section>

      <Section title="Análise de Lucratividade" icon={<MoneyIcon />}>
        <Typography paragraph>
          Entenda a rentabilidade dos seus produtos. (Requer "Preço de Custo" cadastrado no Inventário).
        </Typography>
        <List dense>
          <HelpListItem icon={<GridViewIcon />} primary="Visão Geral" secondary="Cards com Receita Total, Lucro Total, Custos Totais e Margem de Lucro no período, com comparativo percentual." />
          <HelpListItem icon={<BarChartReportIcon />} primary="Receita vs. Custos vs. Lucro" secondary="Gráfico comparando estas três métricas ao longo do tempo." />
          <HelpListItem icon={<TableChartIcon />} primary="Produtos Mais Lucrativos" secondary="Tabela com os produtos que geraram maior lucro, detalhando receita e margem." />
          <HelpListItem icon={<TrendingUpIcon />} primary="Tendência da Margem de Lucro" secondary="Gráfico de linha mostrando a evolução da margem de lucro percentual." />
        </List>
      </Section>

      <Section title="Visão Geral de Vendas" icon={<SalesIcon />}>
        <List dense>
          <HelpListItem icon={<ShowChartIcon />} primary="Tendência de Vendas" secondary="Gráfico de linha mostrando o valor total das vendas ao longo do período." />
          <HelpListItem icon={<TableChartIcon />} primary="Produtos/Serviços Mais Vendidos" secondary="Lista dos itens com maior receita gerada." />
          <HelpListItem icon={<PieChartReportIcon />} primary="Vendas por Categoria" secondary="Gráfico de pizza da receita por categoria de produto e pacotes." />
        </List>
      </Section>

      <Section title="Análise de Clientes" icon={<PeopleIcon />}>
        <List dense>
          <HelpListItem icon={<TableChartIcon />} primary="Principais Clientes" secondary="Tabela com os clientes que mais gastaram, incluindo número de visitas e total gasto." />
        </List>
      </Section>

      <Section title="Status do Estoque" icon={<InventoryIcon />}>
        <List dense>
          <HelpListItem icon={<WarningIcon />} primary="Alertas de Estoque Baixo" secondary="Lista de produtos com quantidade abaixo do nível mínimo definido." />
        </List>
      </Section>
    </Box>
  );

  const renderSettingsHelp = () => (
    <Box>
      <Section title="Configurações do Sistema" icon={<InfoIcon />}>
        <Typography paragraph>
          Personalize o sistema, gerencie serviços, configure informações de segurança, recibos e notificações.
        </Typography>
      </Section>

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.dark' }}>
              <SecurityIcon sx={{ mr: 1 }} /> Segurança
            </Typography>
            <List dense>
              <HelpListItem icon={<ConfirmationNumberIcon />} primary="Alterar PIN de Bloqueio" secondary="Defina ou altere o PIN numérico de 4 dígitos para bloqueio rápido da tela. Se já houver um PIN, será necessário informar o atual." />
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.dark' }}>
              <ColorLensIcon sx={{ mr: 1 }} /> Aparência
            </Typography>
            <List dense>
              <HelpListItem icon={<StyleIcon />} primary="Cores do Tema" secondary="Escolha as cores primária e secundária usando o seletor ou código hexadecimal. Veja a pré-visualização antes de salvar." />
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.dark' }}>
              <HealingIcon sx={{ mr: 1 }} /> Serviços
            </Typography>
            <List dense>
              <HelpListItem icon={<AddIcon />} primary="Adicionar/Editar Serviços" secondary="Cadastre ou modifique serviços, definindo nome, descrição, duração (minutos) e preço." />
              <HelpListItem icon={<DeleteIcon />} primary="Excluir Serviços" secondary="Remove serviços (marcando como inativos se já utilizados em vendas/agendamentos)." />
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.dark' }}>
              <ReceiptLongIcon sx={{ mr: 1 }} /> Recibo
            </Typography>
            <List dense>
              <HelpListItem icon={<HomeIcon />} primary="Informações da Loja" secondary="Configure nome, endereço, telefone, email, site e CNPJ que aparecerão nos recibos." />
              <HelpListItem icon={<NotesIcon />} primary="Informações Adicionais" secondary="Adicione uma mensagem personalizada ao final dos recibos." />
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.dark' }}>
              <AccountBalanceWalletIcon sx={{ mr: 1 }} /> Fiado (Vendas Pendentes)
            </Typography>
            <List dense>
              <HelpListItem icon={<NotificationsIcon />} primary="Ativar Notificações" secondary="Receba alertas sobre vendas com pagamento pendente." />
              <HelpListItem icon={<AccessTimeIcon />} primary="Limite de Dias" secondary="Defina após quantos dias de pendência uma notificação deve ser gerada (1-90 dias)." />
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.dark' }}>
              <BackupIcon sx={{ mr: 1 }} /> Backup
            </Typography>
            <List dense>
              <HelpListItem icon={<CloudDownloadIcon />} primary="Exportar Backup" secondary="Crie uma cópia de segurança do seu banco de dados (arquivo .sqlite)." />
              <HelpListItem icon={<CloudUploadIcon />} primary="Importar Backup" secondary="Restaure o sistema a partir de um arquivo de backup salvo anteriormente. Use com cautela, pois substitui os dados atuais." />
            </List>
          </Paper>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, p: 2, backgroundColor: 'info.lighter', borderRadius: 1 }}>
        <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
          <LightbulbIcon sx={{ mr: 0.5, verticalAlign: 'bottom' }} />
          <strong>Dica:</strong> As configurações de "Recibo" e "Aparência" ajudam a alinhar o sistema e os documentos com a marca do seu pet shop.
        </Typography>
      </Box>
    </Box>
  );


  const renderContent = () => {
    if (currentPagePath === '/' || currentPagePath.includes('/dashboard')) {
      return renderDashboardHelp();
    } else if (currentPagePath.includes('/inventory')) {
      return renderInventoryHelp();
    } else if (currentPagePath.includes('/customers')) {
      return renderCustomersHelp();
    } else if (currentPagePath.includes('/pets')) {
      return renderPetsHelp();
    } else if (currentPagePath.includes('/packages')) {
      return renderPackagesHelp();
    } else if (currentPagePath.includes('/sales')) {
      return renderSalesHelp();
    } else if (currentPagePath.includes('/appointments')) {
      return renderAppointmentsHelp();
    } else if (currentPagePath.includes('/reports')) {
      return renderReportsHelp();
    } else if (currentPagePath.includes('/settings')) {
      return renderSettingsHelp();
    } else {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Bem-vindo à Central de Ajuda!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Navegue até uma página específica para ver um guia detalhado sobre suas funcionalidades.
            Se precisar de assistência geral, entre em contato com o suporte.
          </Typography>
        </Box>
      );
    }
  };

  return (
    <Dialog
      open={isHelpOpen}
      onClose={closeHelp}
      maxWidth="md"
      fullWidth
      scroll="paper" // Permite rolagem do conteúdo se for maior que a tela
      aria-labelledby="help-dialog-title"
    >
      <DialogTitle id="help-dialog-title" sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: 1, borderColor: 'divider', py: 1.5, px: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {getDialogIcon()}
          <Typography variant="h6" component="div" sx={{ ml: 1.5, fontWeight: 'bold' }}>
            {getDialogTitle()}
          </Typography>
        </Box>
        <IconButton onClick={closeHelp} size="medium" aria-label="Fechar ajuda">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ px: 2, py: 2 }}> {/* Ajuste de padding */}
        {renderContent()}
      </DialogContent>
      <DialogActions sx={{ borderTop: 1, borderColor: 'divider', px: 2, py: 1.5 }}>
        <Button onClick={closeHelp} variant="contained" color="primary" startIcon={<CheckIcon />}>
          Entendido
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default HelpDialog;