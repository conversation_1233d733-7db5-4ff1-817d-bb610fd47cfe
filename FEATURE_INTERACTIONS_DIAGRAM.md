# Pet Shop Application - Feature Interaction Diagram

This document visually outlines how the different features and modules of the Pet Shop Desktop Application interact with each other. The diagram is created using Mermaid syntax.

```mermaid
graph TD
    subgraph UserInterface[User Interface - React]
        direction LR
        Dashboard[Dashboard Page]
        Customers[Customers Page]
        Pets[Pets Page]
        Appointments[Appointments Page]
        Sales[Sales Page]
        Packages[Packages Page]
        Settings[Settings Page]
    end

    subgraph BackendLogic[Backend Logic - Electron Main Process + Preload]
        direction LR
        AuthHandler[Authentication Handler]
        DB_Customers[DB: Customers]
        DB_Pets[DB: Pets]
        DB_Appointments[DB: Appointments]
        DB_Services[DB: Services]
        DB_Packages[DB: Packages Definition]
        DB_CustomerPackages[DB: Customer Packages]
        DB_Sales[DB: Sales]
        DB_StoreInfo[DB: Store Info]
        DB_PendingSalesSettings[DB: Pending Sales Settings]
        NotificationSystem[Notification System]
    end

    subgraph GlobalContexts[Global React Contexts]
        direction LR
        AuthContext[AuthContext]
        ThemeContext[ThemeContext]
        NotificationContext[NotificationContext]
    end

    %% UI to Backend Interactions
    Dashboard -->|Fetch Data| DB_Customers
    Dashboard -->|Fetch Data| DB_Pets
    Dashboard -->|Fetch Data| DB_Appointments
    Dashboard -->|Fetch Data| DB_Sales
    Dashboard -->|Quick Actions| Customers
    Dashboard -->|Quick Actions| Pets
    Dashboard -->|Quick Actions| Appointments

    Customers -->|CRUD| DB_Customers
    Customers -->|View/Manage Pets| Pets
    Customers -->|View/Manage Packages| DB_CustomerPackages
    Customers -->|Buy Package| Packages
    Customers -->|Buy Package Triggers| Sales
    Customers -->|View Pending Sales| DB_Sales
    Customers -->|Mark Sale Paid| DB_Sales
    Customers -->|Reactivate Similar| DB_Customers

    Pets -->|CRUD| DB_Pets
    Pets -->|Associate with| DB_Customers
    Pets -->|Link to| Appointments

    Appointments -->|CRUD| DB_Appointments
    Appointments -->|Links| DB_Customers
    Appointments -->|Links| DB_Pets
    Appointments -->|Links| DB_Services
    Appointments --"Completed"-->|Triggers Create Sale| Sales
    Appointments --"No Show"-->|Triggers Notification| NotificationSystem

    Sales -->|CRUD| DB_Sales
    Sales -->|Links| DB_Customers
    Sales -->|Items From| DB_Services
    Sales -->|Items From Packages| DB_CustomerPackages
    Sales -->|Uses Store Info for Receipt| DB_StoreInfo
    Sales --"Pending Payment (Fiado)"-->|Tracked by| DB_Customers
    Sales --"Pending Payment (Fiado)"-->|Notified by| NotificationSystem

    Packages -->|CRUD Definitions| DB_Packages
    Packages -->|Composed of| DB_Services
    Packages --"Customer Buys"-->|Creates Instance| DB_CustomerPackages
    Packages --"Customer Buys"-->|Triggers Create Sale| Sales

    Settings --"Security"-->|Change Password/PIN| AuthHandler
    Settings --"Appearance"-->|Update Theme| ThemeContext
    Settings --"Services"-->|CRUD| DB_Services
    Settings --"Receipt"-->|CRUD| DB_StoreInfo
    Settings --"Fiado"-->|CRUD| DB_PendingSalesSettings
    Settings --"Fiado"-->|Configures| NotificationSystem

    %% Context Interactions
    AuthHandler -->|Updates State| AuthContext
    AuthContext -->|Provides Auth State| Customers
    AuthContext -->|Provides Auth State| Pets
    AuthContext -->|Provides Auth State| Appointments
    AuthContext -->|Provides Auth State| Sales
    AuthContext -->|Provides Auth State| Packages
    AuthContext -->|Provides Auth State| Settings
    AuthContext -->|Provides Auth State| Dashboard

    ThemeContext -->|Applies Theme| UserInterface

    NotificationContext -->|Displays Notifications| UserInterface
    NotificationSystem -->|Sends Notifications via| NotificationContext

    %% General Data Flow
    UserInterface -- IPC via window.electronAPI --> BackendLogic
    BackendLogic -- SQLite Queries --> Database[(SQLite Database)]

    classDef ui fill:#D6EAF8,stroke:#3498DB,stroke-width:2px;
    classDef backend fill:#D5F5E3,stroke:#2ECC71,stroke-width:2px;
    classDef context fill:#FCF3CF,stroke:#F1C40F,stroke-width:2px;
    classDef db fill:#EBDEF0,stroke:#8E44AD,stroke-width:2px;

    class UserInterface,Dashboard,Customers,Pets,Appointments,Sales,Packages,Settings ui;
    class BackendLogic,AuthHandler,NotificationSystem backend;
    class GlobalContexts,AuthContext,ThemeContext,NotificationContext context;
    class DB_Customers,DB_Pets,DB_Appointments,DB_Services,DB_Packages,DB_CustomerPackages,DB_Sales,DB_StoreInfo,DB_PendingSalesSettings,Database db;
```

## Explanation of Diagram Elements:

*   **Boxes with rounded corners:** Represent major pages or modules within the User Interface (React).
*   **Boxes with sharp corners (within subgraphs):** Represent backend handlers, database tables/abstractions, or global contexts.
*   **Arrows (`-->`):** Indicate a flow of data, a trigger, or an interaction.
*   **Text on arrows:** Describes the nature of the interaction.
*   **Subgraphs:** Group related components (User Interface, Backend Logic, Global Contexts).
*   **`Database[(SQLite Database)]`:** Represents the underlying SQLite database.

## Key Interaction Flows:

1.  **User Actions & Data Display:** User interacts with pages (e.g., `Customers Page`). The page component fetches data from the backend (e.g., `DB: Customers`) via IPC calls. Data is then displayed.
2.  **CRUD Operations:** Most pages allow Create, Read, Update, Delete operations which translate to calls to specific backend database handlers.
3.  **Cross-Module Interactions:**
    *   Creating an **Appointment** links a Customer, Pet, and Service.
    *   Completing an **Appointment** can trigger the creation of a **Sale**.
    *   Buying a **Package** by a Customer creates a customer-specific package record and a **Sale**.
    *   **Sales** can be for services or packages and are linked to Customers (especially for 'Fiado' - pending payments).
    *   **Settings** module allows managing Services (used in Appointments/Packages), Receipt Info (used by Sales), and Fiado notification rules (used by the Notification System for Sales).
4.  **Global State Management:**
    *   `AuthContext` manages user login state and PIN, affecting access and UI elements.
    *   `ThemeContext` manages UI appearance, configurable via Settings.
    *   `NotificationContext` is used by the `NotificationSystem` (backend) to display global messages to the user.
5.  **Backend Logic:** The Electron main process handles all database interactions and core backend logic, exposing necessary functions to the renderer process via the preload script (`window.electronAPI`).

This diagram provides a high-level overview. Specific details of data structures and individual function calls are further detailed in the `PROJECT_DOCUMENTATION.md` file.
