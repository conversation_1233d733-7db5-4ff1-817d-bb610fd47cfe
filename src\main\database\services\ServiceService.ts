import { AppDataSource } from '../connection';
import { Service } from '../models/Service';

export class ServiceService {
  private repository = AppDataSource.getRepository(Service);

  async findAll(): Promise<Service[]> {
    return this.repository.find({ order: { name: 'ASC' } });
  }

  async findById(id: number): Promise<Service | null> {
    return this.repository.findOneBy({ id });
  }

  async create(serviceData: Partial<Service>): Promise<Service> {
    const service = this.repository.create(serviceData);
    return this.repository.save(service);
  }

  async update(id: number, serviceData: Partial<Service>): Promise<Service | null> {
    await this.repository.update(id, serviceData);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }
} 