import { useState, useEffect, useCallback } from 'react';
import { Sale } from '../../main/database/models/Sale';
import { SaleItem } from '../../main/database/models/SaleItem';

// Add a cache interface for date range queries
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresIn: number; // milliseconds
}

interface DateRangeCache {
  [key: string]: CacheEntry<Sale[]>;
}

interface SaleItemsCache {
  [key: string]: CacheEntry<SaleItem[]>;
}

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

interface UseSalesResult {
  sales: Sale[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getSaleById: (id: number) => Promise<Sale | null>;
  getSalesByCustomerId: (customerId: number) => Promise<Sale[]>;
  getSalesByDateRange: (startDate: Date, endDate: Date) => Promise<Sale[]>;
  getSaleItems: (saleId: number) => Promise<SaleItem[]>;
  createSale: (saleData: Partial<Sale>, items: Partial<SaleItem>[]) => Promise<Sale | null>;
  deleteSale: (id: number, withRestocking?: boolean) => Promise<boolean>;
  updateSaleStatus: (id: number, status: string) => Promise<Sale | null>;
}

export const useSales = (): UseSalesResult => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Create caches for different query types
  const [dateRangeCache, setDateRangeCache] = useState<DateRangeCache>({});
  const [saleItemsCache, setSaleItemsCache] = useState<SaleItemsCache>({});

  // Simple helper function with no dependencies
  const checkCacheEntry = (entry?: CacheEntry<any>): boolean => {
    if (!entry) return false;
    const now = Date.now();
    return (now - entry.timestamp) < entry.expiresIn;
  };

  // Add helper to get cache key for date range - no need to memoize this
  const getDateRangeCacheKey = (startDate: Date, endDate: Date): string => {
    return `${startDate.toISOString()}_${endDate.toISOString()}`;
  };

  // Memoize cache update functions
  const updateDateRangeCache = useCallback((cacheKey: string, data: Sale[]) => {
    setDateRangeCache(prevCache => ({
      ...prevCache,
      [cacheKey]: {
        data,
        timestamp: Date.now(),
        expiresIn: CACHE_EXPIRATION
      }
    }));
  }, []);

  const updateSaleItemsCache = useCallback((cacheKey: string, data: SaleItem[]) => {
    setSaleItemsCache(prevCache => ({
      ...prevCache,
      [cacheKey]: {
        data,
        timestamp: Date.now(),
        expiresIn: CACHE_EXPIRATION
      }
    }));
  }, []);

  const clearAllCaches = useCallback(() => {
    setDateRangeCache({});
    setSaleItemsCache({});
  }, []);

  const fetchSales = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('sales:getAll');
      if (response.success) {
        setSales(response.data);
      } else {
        setError(response.error || 'Failed to fetch sales');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const getSaleById = useCallback(async (id: number): Promise<Sale | null> => {
    try {
      const response = await window.electronAPI.invoke('sales:getById', id);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch sale with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const getSalesByCustomerId = useCallback(async (customerId: number): Promise<Sale[]> => {
    try {
      const response = await window.electronAPI.invoke('sales:getByCustomerId', customerId);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch sales for customer ID ${customerId}`);
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  const getSalesByDateRange = useCallback(async (startDate: Date, endDate: Date): Promise<Sale[]> => {
    try {
      // Create a cache key for this date range
      const cacheKey = getDateRangeCacheKey(startDate, endDate);
      
      // Check if we have a valid cached result - directly access the cache
      const cacheEntry = dateRangeCache[cacheKey];
      if (checkCacheEntry(cacheEntry)) {
        return cacheEntry.data;
      }
      
      // If not in cache or expired, fetch from the API
      const response = await window.electronAPI.invoke(
        'sales:getByDateRange', 
        startDate.toISOString(), 
        endDate.toISOString()
      );
      
      if (response.success) {
        // Store in cache
        updateDateRangeCache(cacheKey, response.data);
        return response.data;
      }
      
      setError(response.error || 'Failed to fetch sales by date range');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, [dateRangeCache, updateDateRangeCache]);

  const getSaleItems = useCallback(async (saleId: number): Promise<SaleItem[]> => {
    try {
      // Create a cache key for this sale's items
      const cacheKey = `items_${saleId}`;
      
      // Check if we have a valid cached result - directly access the cache
      const cacheEntry = saleItemsCache[cacheKey];
      if (checkCacheEntry(cacheEntry)) {
        return cacheEntry.data;
      }
      
      // If not in cache or expired, fetch from the API
      const response = await window.electronAPI.invoke('sales:getSaleItems', saleId);
      
      if (response.success) {
        // Store in cache
        updateSaleItemsCache(cacheKey, response.data);
        return response.data;
      }
      
      setError(response.error || `Failed to fetch sale items for sale ID ${saleId}`);
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, [saleItemsCache, updateSaleItemsCache]);

  const createSale = useCallback(async (
    saleData: Partial<Sale>, 
    items: Partial<SaleItem>[]
  ): Promise<Sale | null> => {
    try {
      // Ensure correct field names are used for the sale
      // Keep the typedSaleData as-is, we need both formats
      const typedSaleData = saleData as any;
      
      // Ensure each item has the correct field names
      const processedItems = items.map(item => {
        // Clone the item to avoid mutating the original
        const processedItem = { ...item } as any;
        
        // Ensure we're sending explicit null values, not undefined
        if ('productId' in processedItem) {
          processedItem.productId = processedItem.productId === undefined ? null : processedItem.productId;
        }
        if ('serviceId' in processedItem) {
          processedItem.serviceId = processedItem.serviceId === undefined ? null : processedItem.serviceId;
        }
        
        return processedItem;
      });
      
      // Send the data to the backend
      const response = await window.electronAPI.invoke('sales:create', typedSaleData, processedItems);
      
      if (response.success) {
        await fetchSales(); // Refresh the list
        
        // Clear date range and sale items caches as data has changed
        clearAllCaches();
        
        return response.data;
      }
      setError(response.error || 'Failed to create sale');
      return null;
    } catch (err) {
      // console.error('Error in createSale:', err);
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchSales, clearAllCaches]);

  const deleteSale = useCallback(async (id: number, withRestocking = true): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('sales:delete', id, withRestocking);
      if (response.success) {
        await fetchSales(); // Refresh the list
        
        // Clear date range and sale items caches as data has changed
        clearAllCaches();
        
        return true;
      }
      setError(response.error || `Failed to delete sale with ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchSales, clearAllCaches]);

  const updateSaleStatus = useCallback(async (id: number, status: string): Promise<Sale | null> => {
    try {
      const response = await window.electronAPI.invoke('sales:updateStatus', id, status);
      if (response.success) {
        await fetchSales(); // Refresh the list
        
        // Use clearAllCaches instead of directly setting cache state
        clearAllCaches();
        
        return response.data;
      }
      setError(response.error || `Failed to update status for sale with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchSales, clearAllCaches]);

  useEffect(() => {
    fetchSales();
  }, [fetchSales]);

  return {
    sales,
    loading,
    error,
    refetch: fetchSales,
    getSaleById,
    getSalesByCustomerId,
    getSalesByDateRange,
    getSaleItems,
    createSale,
    deleteSale,
    updateSaleStatus
  };
}; 