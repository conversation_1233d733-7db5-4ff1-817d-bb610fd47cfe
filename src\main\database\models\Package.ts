import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Service } from './Service';

export type FrequencyType = 'weekly' | 'monthly' | 'custom_days';

@Entity('packages')
export class Package {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @Column({ nullable: true, type: 'text' })
  description!: string;

  @Column({ type: 'float' })
  price!: number;

  @ManyToOne(() => Service)
  @JoinColumn({ name: 'service_id' })
  service!: Service;

  @Column()
  service_id!: number;

  @Column()
  total_occurrences!: number;

  @Column({ type: 'varchar' })
  frequency_type!: FrequencyType;

  @Column()
  frequency_interval!: number;

  @Column({ default: true })
  is_active!: boolean;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
} 