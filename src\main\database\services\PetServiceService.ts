import { getRepository } from 'typeorm';
import { PetService } from '../models/PetService';
import { AppDataSource } from '../connection';

export class PetServiceService {
  async findAll(): Promise<PetService[]> {
    const repository = AppDataSource.getRepository(PetService);
    return repository.find({
      relations: ['pet']
    });
  }

  async findById(id: number): Promise<PetService | null> {
    const repository = AppDataSource.getRepository(PetService);
    return repository.findOne({
      where: { id },
      relations: ['pet']
    });
  }

  async findByPetId(petId: number): Promise<PetService[]> {
    console.log(`DEBUG: PetServiceService.findByPetId called with petId: ${petId}`);
    const repository = AppDataSource.getRepository(PetService);
    
    try {
      // First check if the pet ID exists at all
      const count = await repository.count({ where: { pet_id: petId } });
      console.log(`DEBUG: Found ${count} services for pet ID: ${petId}`);
      
      const services = await repository.find({
        where: { pet_id: petId },
        relations: ['pet'],
        order: { service_date: 'DESC' } // Show newest services first
      });
      
      console.log(`DEBUG: Retrieved ${services.length} services for pet ID: ${petId}`);
      console.log(`DEBUG: Services:`, JSON.stringify(services));
      
      return services;
    } catch (error) {
      console.error(`DEBUG: Error in findByPetId for pet ID ${petId}:`, error);
      throw error;
    }
  }

  async create(petServiceData: Partial<PetService>): Promise<PetService> {
    const repository = AppDataSource.getRepository(PetService);
    const petService = repository.create(petServiceData);
    return repository.save(petService);
  }

  async update(id: number, petServiceData: Partial<PetService>): Promise<PetService | null> {
    const repository = AppDataSource.getRepository(PetService);
    await repository.update(id, petServiceData);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    const repository = AppDataSource.getRepository(PetService);
    const result = await repository.delete(id);
    return result.affected ? result.affected > 0 : false;
  }
} 