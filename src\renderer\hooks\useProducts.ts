import { useState, useEffect, useCallback } from 'react';
import { Product } from '../../main/database/models/Product';

// Add a cache interface for query results
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresIn: number; // milliseconds
}

// Cache for low stock products
interface ProductsCache {
  lowStock?: CacheEntry<Product[]>;
}

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

interface UseProductsResult {
  products: Product[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getProductById: (id: number) => Promise<Product | null>;
  getProductsByCategory: (category: string) => Promise<Product[]>;
  getLowStockProducts: () => Promise<Product[]>;
  createProduct: (productData: Partial<Product>) => Promise<Product | null>;
  updateProduct: (id: number, productData: Partial<Product>) => Promise<Product | null>;
  updateProductStock: (id: number, quantity: number) => Promise<Product | null>;
  deleteProduct: (id: number) => Promise<boolean>;
  setProductsState: (products: Product[]) => void;
}

export const useProducts = (): UseProductsResult => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [productsCache, setProductsCache] = useState<ProductsCache>({});

  // Simple helper function with no dependencies
  const checkCacheEntry = (entry?: CacheEntry<any>): boolean => {
    if (!entry) return false;
    
    const now = Date.now();
    return (now - entry.timestamp) < entry.expiresIn;
  };

  // Add a memoized function for updating the cache
  const updateLowStockCache = useCallback((data: Product[]) => {
    setProductsCache(prevCache => ({
      ...prevCache,
      lowStock: {
        data,
        timestamp: Date.now(),
        expiresIn: CACHE_EXPIRATION
      }
    }));
  }, []);

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('products:getAll');
      if (response.success) {
        setProducts(response.data);
      } else {
        setError(response.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const getProductById = useCallback(async (id: number): Promise<Product | null> => {
    try {
      const response = await window.electronAPI.invoke('products:getById', id);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch product with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const getProductsByCategory = useCallback(async (category: string): Promise<Product[]> => {
    try {
      const response = await window.electronAPI.invoke('products:getByCategory', category);
      if (response.success) {
        return response.data;
      }
      setError(response.error || `Failed to fetch products in category ${category}`);
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, []);

  const getLowStockProducts = useCallback(async (): Promise<Product[]> => {
    try {
      // Check if we have a valid cached result - directly access the cache
      const cacheEntry = productsCache.lowStock;
      if (checkCacheEntry(cacheEntry)) {
        return cacheEntry!.data;
      }
      
      // If not in cache or expired, fetch from the API
      const response = await window.electronAPI.invoke('products:getLowStock');
      
      if (response.success) {
        // Store in cache
        updateLowStockCache(response.data);
        return response.data;
      }
      
      setError(response.error || 'Failed to fetch low stock products');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    }
  }, [productsCache.lowStock, updateLowStockCache]);

  const createProduct = useCallback(async (productData: Partial<Product>): Promise<Product | null> => {
    try {
      const response = await window.electronAPI.invoke('products:create', productData);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        
        // Clear cache as data has changed
        setProductsCache({});
        
        return response.data;
      }
      setError(response.error || 'Failed to create product');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchProducts]);

  const updateProduct = useCallback(async (id: number, productData: Partial<Product>): Promise<Product | null> => {
    try {
      const response = await window.electronAPI.invoke('products:update', id, productData);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        
        // Clear cache as data has changed
        setProductsCache({});
        
        return response.data;
      }
      setError(response.error || `Failed to update product with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchProducts]);

  const updateProductStock = useCallback(async (id: number, quantity: number): Promise<Product | null> => {
    try {
      const response = await window.electronAPI.invoke('products:updateStock', id, quantity);
      if (response.success) {
        // Don't refetch all products, as we'll update the state manually in the component
        
        // But clear cache as data has changed, especially lowStock cache
        setProductsCache({});
        
        return response.data;
      }
      setError(response.error || `Failed to update stock for product with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, []);

  const deleteProduct = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('products:delete', id);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        
        // Clear cache as data has changed
        setProductsCache({});
        
        return true;
      }
      setError(response.error || `Failed to delete product with ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchProducts]);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    products,
    loading,
    error,
    refetch: fetchProducts,
    getProductById,
    getProductsByCategory,
    getLowStockProducts,
    createProduct,
    updateProduct,
    updateProductStock,
    deleteProduct,
    setProductsState: setProducts
  };
}; 