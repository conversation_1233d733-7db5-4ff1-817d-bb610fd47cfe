export interface Product {
  id: number;
  name: string;
  category: string;
  description: string | null;
  price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
  is_deleted: boolean;
  created_at: string | Date;
  updated_at: string | Date;
}

export interface ProductFormData {
  name: string;
  category: string;
  description: string;
  price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
}

export type StockStatus = 'low' | 'optimal' | 'overstocked';

export const getStockStatus = (product: Product): StockStatus => {
  if (product.stock_quantity <= product.min_stock_level) {
    return 'low';
  } else if (product.stock_quantity > product.min_stock_level * 3) {
    return 'overstocked';
  } else {
    return 'optimal';
  }
};

export const stockStatusColors = {
  low: '#f44336', // Red
  optimal: '#4caf50', // Green
  overstocked: '#ff9800' // Orange
};

export const productCategories = [
  'Ração para Pets',
  'Brinquedos',
  'Acessórios',
  'Produtos de Higiene',
  'Camas e Conforto',
  'Saúde e Bem-estar',
  'Petiscos',
  'Produtos de Limpeza',
  'Equipamentos de Treinamento',
  'Outros'
]; 