import React, { useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from '@contexts/AuthContext';
import { NotificationProvider } from '@contexts/NotificationContext';
import { ThemeProviderWrapper } from './contexts/ThemeContext';
import { HelpProvider } from './contexts/HelpContext';
import { DatabaseProvider } from './contexts/DatabaseContext';
import DatabaseLoader from '@components/common/DatabaseLoader';
import MainLayout from '@components/Layout/MainLayout';
import { Dashboard } from '@pages/Dashboard';
import SetupPin from '@components/SetupPin';
import ThemeRefresher from './components/ThemeRefresher';
import HelpDialog from './components/Help/HelpDialog';

// Pages
import Inventory from './pages/Inventory';
import Customers from './pages/Customers';
import Pets from './pages/Pets';
import Sales from './pages/Sales';
import Appointments from './pages/Appointments';
import Reports from './pages/Reports';
import Settings from './pages/Settings';
import Packages from './pages/Packages';

const App: React.FC = () => {
  // Signal to main process that the UI is loaded
  useEffect(() => {
    // Small delay to ensure component tree is fully mounted
    const timer = setTimeout(() => {
      // Check if electron object exists (in case running in browser)
      if (window.electron && window.electron.sendMainWindowReady) {
        window.electron.sendMainWindowReady();
      }
    }, 300);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <ThemeProviderWrapper>
      <CssBaseline />
      <ThemeRefresher />
      <DatabaseProvider>
        <DatabaseLoader>
          <Router>
            <AuthProvider>
              <NotificationProvider>
                <HelpProvider>
                  <SetupPin />
                  <HelpDialog />
                  <Routes>
                    <Route
                      path="/*"
                      element={
                        <MainLayout>
                          <Routes>
                            <Route path="/" element={<Dashboard />} />
                            <Route path="/inventory" element={<Inventory />} />
                            <Route path="/customers" element={<Customers />} />
                            <Route path="/pets" element={<Pets />} />
                            <Route path="/sales" element={<Sales />} />
                            <Route path="/appointments" element={<Appointments />} />
                            <Route path="/packages" element={<Packages />} />
                            <Route path="/reports" element={<Reports />} />
                            <Route path="/settings" element={<Settings />} />
                            <Route path="*" element={<Navigate to="/" replace />} />
                          </Routes>
                        </MainLayout>
                      }
                    />
                  </Routes>
                </HelpProvider>
              </NotificationProvider>
            </AuthProvider>
          </Router>
        </DatabaseLoader>
      </DatabaseProvider>
    </ThemeProviderWrapper>
  );
};

export default App; 