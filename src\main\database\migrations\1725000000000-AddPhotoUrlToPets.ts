import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPhotoUrlToPets1725000000000 implements MigrationInterface {
    name = 'AddPhotoUrlToPets1725000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add photo_url column to pets table
        await queryRunner.query(`ALTER TABLE "pets" ADD COLUMN "photo_url" TEXT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove photo_url column if migration needs to be reverted
        await queryRunner.query(`ALTER TABLE "pets" DROP COLUMN "photo_url"`);
    }
} 