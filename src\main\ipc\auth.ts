import { ipcMain, IpcMainInvokeEvent } from 'electron';
import bcrypt from 'bcryptjs';
import { store } from '../store'; // Import store

const SALT_ROUNDS = 10;

export const setupAuthHandlers = () => {
  ipcMain.handle('auth:setPinHash', async (_event: IpcMainInvokeEvent, pin: string) => {
    try {
      if (!pin || pin.length !== 4 || !/^\d+$/.test(pin)) {
        throw new Error('PIN must be 4 digits.');
      }
      const hashedPin = await bcrypt.hash(pin, SALT_ROUNDS);
      store.set('screenLockPinHash', hashedPin);
      return { success: true };
    } catch (error) {
      console.error('Error setting PIN hash:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('auth:verifyPin', async (_event: IpcMainInvokeEvent, pin: string) => {
    try {
      if (!pin) throw new Error('PIN cannot be empty.');
      const storedHash = store.get('screenLockPinHash') as string | undefined;
      if (!storedHash) {
        return { success: false, error: 'No PIN set.', matches: false }; // No PIN set, so no match
      }
      const matches = await bcrypt.compare(pin, storedHash);
      return { success: true, matches };
    } catch (error) {
      console.error('Error verifying PIN:', error);
      // In case of error (e.g., storedHash is somehow malformed, though unlikely with bcrypt hash output)
      return { success: false, error: (error as Error).message, matches: false }; 
    }
  });

  ipcMain.handle('auth:isPinConfigured', async (_event: IpcMainInvokeEvent) => {
    try {
      const storedHash = store.get('screenLockPinHash') as string | undefined;
      return { success: true, isConfigured: !!storedHash };
    } catch (error) {
      console.error('Error checking if PIN is configured:', error);
      return { success: false, error: (error as Error).message, isConfigured: false };
    }
  });

  // Removed the auth:changePassword handler as it was tied to the old SQLite User model
  // and bcrypt password hashing. Password changes with Supabase should use its own mechanisms.

}; 