import React, { useState, useEffect, useRef } from 'react';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import { SelectChangeEvent } from '@mui/material/';
import Grid from '@mui/material/Grid';
import Divider from '@mui/material/Divider';
import TableContainer from '@mui/material/TableContainer';
import Table from '@mui/material/Table';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton from '@mui/material/ToggleButton';
import InputAdornment from '@mui/material/InputAdornment';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import { SaleItemForm } from './SaleItemForm';
import { 
  SaleFormData, 
  SaleItemFormData, 
  PaymentMethod,
  paymentMethodLabels,
  formatCurrency,
  SaleStatus,
  saleStatusLabels
} from '../../types/sales';
import { Product } from '../../types/inventory';
import { Customer } from '../../types/customers';
import { Pet } from '../../types/pets';
import { CustomerPackage } from '../../types/packages';
import DeleteIcon from '@mui/icons-material/Delete';
import PaidIcon from '@mui/icons-material/Paid';
import PendingIcon from '@mui/icons-material/Pending';
import PersonIcon from '@mui/icons-material/Person';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import LocalAtmIcon from '@mui/icons-material/LocalAtm';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import PixIcon from '@mui/icons-material/Payments';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveIcon from '@mui/icons-material/Save';
import WarningIcon from '@mui/icons-material/Warning';

interface SaleFormProps {
  onSubmit: (sale: SaleFormData) => void;
  onCancel: () => void;
  products: Product[];
  services: any[]; // Service type
  customers: Customer[];
  pets: Pet[]; // Add pets prop
  customerPackages?: CustomerPackage[]; // Add customer packages prop
  getCustomerPackages?: (customerId: number) => Promise<CustomerPackage[]>; // Add getCustomerPackages function
  initialData?: {
    customer_id?: number;
    payment_method?: PaymentMethod;
    items?: SaleItemFormData[];
  };
}

export const SaleForm: React.FC<SaleFormProps> = ({
  onSubmit,
  onCancel,
  products,
  services,
  customers,
  pets,
  customerPackages = [],
  getCustomerPackages,
  initialData
}) => {
  const [customerId, setCustomerId] = useState<number | ''>(initialData?.customer_id || 0);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(initialData?.payment_method || 'cash');
  const [saleStatus, setSaleStatus] = useState<SaleStatus>('paid');
  const [items, setItems] = useState<SaleItemFormData[]>(initialData?.items || []);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [customerPackagesList, setCustomerPackagesList] = useState<CustomerPackage[]>([]);
  const [loadingPackages, setLoadingPackages] = useState(false);
  
  // Ref to store the latest getCustomerPackages function to prevent infinite loops
  const getCustomerPackagesRef = useRef(getCustomerPackages);
  
  // Update the ref whenever getCustomerPackages changes
  useEffect(() => {
    getCustomerPackagesRef.current = getCustomerPackages;
  }, [getCustomerPackages]);
  
  // Add state for the customer change confirmation dialog
  const [customerChangeDialog, setCustomerChangeDialog] = useState<{
    open: boolean;
    newCustomerId: number | '';
  }>({
    open: false,
    newCustomerId: ''
  });
  
  // Track if the component has been initialized to prevent clearing items on first load
  const [isInitialized, setIsInitialized] = useState(false);

  // Set initial data from appointment if available
  useEffect(() => {
    if (initialData) {
      if (initialData.customer_id) {
        setCustomerId(initialData.customer_id);
      }
      if (initialData.payment_method) {
        setPaymentMethod(initialData.payment_method);
      }
      if (initialData.items && initialData.items.length > 0) {
        setItems(initialData.items);
      }
      // Mark as initialized
      setIsInitialized(true);
    } else {
      setIsInitialized(true);
    }
  }, [initialData]);

  // Set payment status to paid when "Cliente à vista" is selected
  useEffect(() => {
    if (customerId === 0) {
      setSaleStatus('paid');
    }
  }, [customerId]);

  // Load customer packages when customer changes
  useEffect(() => {
    // Prevent running this effect if no customer is selected
    if (typeof customerId !== 'number' || customerId <= 0) {
      setCustomerPackagesList([]);
      setLoadingPackages(false); // Reset loading state when no valid customer is selected
      return;
    }
    
    // Prevents infinite loops by accessing the stable ref
    const currentGetPackages = getCustomerPackagesRef.current;
    if (!currentGetPackages) {
      setLoadingPackages(false); // Also reset loading if there's no getter function
      return;
    }
    
    let isMounted = true;
    
    const loadCustomerPackages = async () => {
      setLoadingPackages(true);
      try {
        const packages = await currentGetPackages(customerId);
        // Only update state if component is still mounted
        if (isMounted) {
          setCustomerPackagesList(packages);
        }
      } catch (error) {
        console.error('Error loading customer packages:', error);
      } finally {
        if (isMounted) {
          setLoadingPackages(false);
        }
      }
    };

    loadCustomerPackages();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [customerId]); // Remove getCustomerPackages from dependencies

  // Track validation errors
  useEffect(() => {
    const newErrors: {
      customer?: string;
      payment_method?: string;
      items?: string;
    } = {};
    
    if (items.length === 0 && isSubmitting) {
      newErrors.items = 'Adicione pelo menos um item à venda';
    }
    
    setFormError(Object.keys(newErrors).length > 0 ? Object.values(newErrors).join('\n') : null);
  }, [items, isSubmitting]);

  // Effect to clear items when customer changes (but not on initial load)
  useEffect(() => {
    // Only clear items if we've already initialized and we're not on the first render
    if (isInitialized && customerId !== initialData?.customer_id) {
      // We don't need to clear items here since we use the dialog confirmation
      // Items will be cleared after user confirms the customer change
    }
  }, [customerId, isInitialized, initialData?.customer_id]);

  const handleCustomerChange = (event: SelectChangeEvent<number>) => {
    const newCustomerId = Number(event.target.value);
    
    // If there are items in the cart, show confirmation dialog before changing customer
    if (items.length > 0) {
      setCustomerChangeDialog({
        open: true,
        newCustomerId
      });
    } else {
      // If no items, just change the customer
      setCustomerId(newCustomerId);
    }
  };

  // Function to confirm customer change and clear items
  const confirmCustomerChange = () => {
    // First change the customer
    setCustomerId(customerChangeDialog.newCustomerId);
    // Then clear the items
    setItems([]);
    // Close the dialog
    setCustomerChangeDialog({ open: false, newCustomerId: '' });
  };

  // Function to cancel customer change
  const cancelCustomerChange = () => {
    setCustomerChangeDialog({ open: false, newCustomerId: '' });
  };

  const handlePaymentMethodChange = (event: SelectChangeEvent<PaymentMethod>) => {
    setPaymentMethod(event.target.value as PaymentMethod);
  };

  const handleStatusChange = (event: React.MouseEvent<HTMLElement>, newStatus: SaleStatus | null) => {
    if (newStatus !== null) {
      setSaleStatus(newStatus);
    }
  };

  const handleAddItem = (item: SaleItemFormData) => {
    setItems([...items, item]);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  const calculateTotal = (): number => {
    return items.reduce((sum, item) => {
      // Skip items that use packages since they are already paid for
      if (item.type === 'package' && item.customer_package_id) {
        return sum;
      }
      return sum + (item.price_per_unit * item.quantity);
    }, 0);
  };

  const validateForm = (): boolean => {
    setIsSubmitting(true);
    
    const newErrors: {
      customer?: string;
      payment_method?: string;
      items?: string;
    } = {};
    
    if (items.length === 0) {
      newErrors.items = 'Adicione pelo menos um item à venda';
    }
    
    setFormError(Object.keys(newErrors).length > 0 ? Object.values(newErrors).join('\n') : null);
    
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (validateForm()) {
      // Create the sale data
      const saleData: SaleFormData = {
        customer_id: customerId as number,
        payment_method: paymentMethod,
        status: saleStatus,
        items: items.map(item => ({
          type: item.type,
          id: item.id,
          quantity: item.quantity,
          price_per_unit: item.price_per_unit,
          name: item.name,
          petId: item.petId || null,
          customer_package_id: item.customer_package_id || null,
          is_package_service: item.type === 'package' && !!item.customer_package_id
        }))
      };
      
      // Call the parent's onSubmit function
      onSubmit(saleData);
    }
  };

  // Helper function to get the icon for a payment method
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash':
        return <AttachMoneyIcon />;
      case 'credit_card':
        return <CreditCardIcon />;
      case 'debit_card':
        return <CreditScoreIcon />;
      case 'online':
        return <PixIcon />;
      case 'other':
        return <AccountBalanceIcon />;
      default:
        return <LocalAtmIcon />;
    }
  };

  return (
    <Box>
      {/* Customer Change Confirmation Dialog */}
      <Dialog
        open={customerChangeDialog.open}
        onClose={cancelCustomerChange}
        aria-labelledby="customer-change-dialog-title"
      >
        <DialogTitle id="customer-change-dialog-title" sx={{ display: 'flex', alignItems: 'center' }}>
          <WarningIcon color="warning" sx={{ mr: 1 }} />
          Alterar Cliente
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Alterar o cliente irá remover todos os itens adicionados à venda. 
            Deseja continuar?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelCustomerChange}>Cancelar</Button>
          <Button onClick={confirmCustomerChange} color="primary" variant="contained">
            Continuar e Remover Itens
          </Button>
        </DialogActions>
      </Dialog>

      {/* Main form container */}
      <Paper 
        elevation={2} 
        sx={{ 
          p: 2, 
          borderRadius: 2,
          mb: 2
        }}
      >
        {/* Customer and Payment section */}
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 1 }}>
              <Typography variant="subtitle1" color="primary" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PersonIcon sx={{ mr: 1, fontSize: 20 }} />
                Cliente
              </Typography>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="customer-select-label">Selecione o Cliente</InputLabel>
                <Select
                  labelId="customer-select-label"
                  value={customerId}
                  onChange={handleCustomerChange}
                  label="Selecione o Cliente"
                  size="small"
                  sx={{ 
                    '& .MuiSelect-select': { 
                      display: 'flex',
                      alignItems: 'center',
                      minHeight: '1.4375em'
                    }
                  }}
                >
                  <MenuItem value={0}>Cliente à vista</MenuItem>
                  {customers.map(customer => (
                    <MenuItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 1 }}>
              <Typography variant="subtitle1" color="primary" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CreditCardIcon sx={{ mr: 1, fontSize: 20 }} />
                Forma de Pagamento
              </Typography>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="payment-method-select-label">Forma de Pagamento</InputLabel>
                <Select
                  labelId="payment-method-select-label"
                  value={paymentMethod}
                  onChange={handlePaymentMethodChange}
                  label="Forma de Pagamento"
                  size="small"
                  sx={{ 
                    '& .MuiSelect-select': { 
                      display: 'flex',
                      alignItems: 'center',
                      minHeight: '1.4375em'
                    }
                  }}
                  startAdornment={
                    <InputAdornment position="start">
                      {getPaymentMethodIcon(paymentMethod)}
                    </InputAdornment>
                  }
                >
                  {Object.entries(paymentMethodLabels).map(([key, label]) => (
                    <MenuItem key={key} value={key}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 1.5 }} />

        {/* Payment Status section - only show if not Cliente à vista */}
        {customerId !== 0 && (
          <>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" color={saleStatus === 'paid' ? 'success.main' : 'warning.main'} fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                {saleStatus === 'paid' ? 
                  <PaidIcon sx={{ mr: 1, fontSize: 20 }} /> : 
                  <PendingIcon sx={{ mr: 1, fontSize: 20 }} />
                }
                Status do Pagamento
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={7}>
                  <ToggleButtonGroup
                    value={saleStatus}
                    exclusive
                    onChange={handleStatusChange}
                    fullWidth
                    size="small"
                    sx={{ 
                      mb: 1, 
                      display: 'flex', 
                      height: '100%', 
                      gap: 1, // Add gap between buttons
                      '& .MuiToggleButtonGroup-grouped': {
                        border: '1px solid var(--border-color, rgba(0, 0, 0, 0.12)) !important',
                        borderRadius: '4px !important',
                        margin: '0 !important'
                      }
                    }}
                  >
                    <ToggleButton 
                      value="paid" 
                      sx={{ 
                        py: 0.75,
                        '--border-color': saleStatus === 'paid' ? 'success.main' : 'rgba(0, 0, 0, 0.12)',
                        flex: 1,
                        display: 'flex',
                        justifyContent: 'flex-start',
                        '&.Mui-selected': { 
                          backgroundColor: '#e8f5e9', 
                          boxShadow: 'inset 0px 0px 0px 1px #4caf50',
                          '&:hover': {
                            backgroundColor: '#c8e6c9'
                          }
                        } 
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PaidIcon sx={{ mr: 0.5 }} color="success" />
                        <Box>
                          <Typography variant="body2" fontWeight="bold">Pagar Agora</Typography>
                          <Typography variant="caption" color="success.main" align="left">
                            Marcar venda como PAGA
                          </Typography>
                        </Box>
                      </Box>
                    </ToggleButton>
                    <ToggleButton 
                      value="pending" 
                      sx={{ 
                        py: 0.75,
                        '--border-color': saleStatus === 'pending' ? 'warning.main' : 'rgba(0, 0, 0, 0.12)',
                        flex: 1,
                        display: 'flex',
                        justifyContent: 'flex-start',
                        '&.Mui-selected': { 
                          backgroundColor: '#fff3e0', 
                          boxShadow: 'inset 0px 0px 0px 1px #ff9800',
                          '&:hover': {
                            backgroundColor: '#ffe0b2'
                          }
                        } 
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PendingIcon sx={{ mr: 0.5 }} color="warning" />
                        <Box>
                          <Typography variant="body2" fontWeight="bold">Pagar Depois</Typography>
                          <Typography variant="caption" color="warning.main" align="left">
                            Marcar venda como PENDENTE
                          </Typography>
                        </Box>
                      </Box>
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Grid>
                
                <Grid item xs={12} md={5}>
                  {/* Payment status explanation - more compact */}
                  <Box sx={{ 
                    bgcolor: saleStatus === 'paid' ? 'rgba(76, 175, 80, 0.08)' : 'rgba(255, 152, 0, 0.08)', 
                    borderRadius: 1, 
                    p: 1,
                    border: '1px solid',
                    borderColor: saleStatus === 'paid' ? 'success.light' : 'warning.light',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <Typography variant="caption" color={saleStatus === 'paid' ? 'success.dark' : 'warning.dark'} sx={{ fontSize: '0.75rem' }}>
                      {saleStatus === 'paid' ? (
                        <>
                          • Valor contabilizado nas receitas<br />
                          • Recibo será gerado após finalizar
                        </>
                      ) : (
                        <>
                          • Valor não contabilizado até pagamento<br />
                          • Poderá ser marcado como pago posteriormente
                        </>
                      )}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
            
            <Divider sx={{ my: 1.5 }} />
          </>
        )}

        {/* Items section with simpler styling */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1" color="primary" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <ShoppingCartIcon sx={{ mr: 1, fontSize: 20 }} />
            Itens da Venda
          </Typography>

      <SaleItemForm
        onAddItem={handleAddItem}
        onRemoveItem={handleRemoveItem}
        products={products}
        services={services}
        items={items}
        pets={pets}
        customerId={customerId}
        customerPackages={customerPackagesList}
        hidePackageOption={customerId === 0}
        hideServiceOption={customerId === 0}
      />

      {formError && (
            <Alert severity="error" sx={{ mt: 2 }}>
          {formError}
            </Alert>
          )}
        </Box>
      </Paper>

      {/* Footer with total and action buttons */}
      <Paper 
        elevation={2} 
        sx={{ 
          p: 1.5, 
          borderRadius: 2, 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          position: 'sticky',
          bottom: 0,
          zIndex: 10,
          boxShadow: '0 -1px 4px rgba(0,0,0,0.05)',
          mt: 'auto'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AttachMoneyIcon color="primary" sx={{ fontSize: 24, mr: 0.5 }} />
          <Box>
            <Typography variant="caption" color="text.secondary">
              Total da Venda:
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main', lineHeight: 1.2 }}>
              {formatCurrency(calculateTotal())}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button 
            variant="outlined" 
            color="error" 
            onClick={onCancel}
            startIcon={<CancelIcon />}
            size="small"
          >
              Cancelar
            </Button>
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleSubmit}
              disabled={items.length === 0}
            startIcon={<SaveIcon />}
            size="small"
            sx={{ fontWeight: 'bold' }}
            >
              Finalizar Venda
            </Button>
        </Box>
      </Paper>
    </Box>
  );
}; 