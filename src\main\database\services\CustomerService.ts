import { AppDataSource } from '../connection';
import { Customer } from '../models/Customer';
import { AppointmentService } from './AppointmentService';
import { PetService } from './PetService';

export class CustomerService {
  private repository = AppDataSource.getRepository(Customer);
  private appointmentService = new AppointmentService();

  async findAll(): Promise<Customer[]> {
    return this.repository.find({ 
      where: { status: 'active' },
      order: { name: 'ASC' } 
    });
  }

  async findById(id: number): Promise<Customer | null> {
    return this.repository.findOneBy({ id });
  }

  async findByEmailOrPhone(search: string): Promise<Customer[]> {
    return this.repository
      .createQueryBuilder('customer')
      .where('(customer.email LIKE :search OR customer.phone LIKE :search OR customer.name LIKE :search) AND customer.status = :status', 
        { search: `%${search}%`, status: 'active' })
      .getMany();
  }

  async create(customerData: Partial<Customer>, forceCreate: boolean = false): Promise<Customer> {
    // Basic validation for customer name
    if (!customerData.name || typeof customerData.name !== 'string' || customerData.name.trim().length < 3) {
      throw new Error('O nome do cliente deve ter pelo menos 3 caracteres.');
    }
    
    // Sanitize input data by trimming fields
    const trimmedData = {
      ...customerData,
      name: customerData.name.trim()
    };
    
    // Handle empty strings for optional fields
    if (trimmedData.email === '') trimmedData.email = undefined;
    if (trimmedData.phone === '') trimmedData.phone = undefined;
    if (trimmedData.address === '') trimmedData.address = undefined;
    if (trimmedData.additional_notes === '') trimmedData.additional_notes = undefined;
    
    // If forceCreate is true, bypass the duplicate check
    if (!forceCreate) {
      // Check for existing active customer with same name
      const existingActiveCustomer = await this.repository.findOne({
        where: [
          { name: trimmedData.name, status: 'active' }
        ]
      });

      if (existingActiveCustomer) {
        throw new Error('Um cliente ativo com este nome já existe.');
      }
      
      // Check for existing inactive customer with same name or phone
      const whereClause: any[] = [{ name: trimmedData.name, status: 'inactive' }];
      
      // Only add phone to check if it's provided
      if (trimmedData.phone) {
        whereClause.push({ phone: trimmedData.phone, status: 'inactive' });
      }
      
      const existingInactiveCustomer = await this.repository.findOne({
        where: whereClause
      });

      if (existingInactiveCustomer) {
        throw new Error('Um cliente inativo com este nome ou telefone já existe. Gostaria de reativá-lo?');
      }
    }

    const customer = this.repository.create(trimmedData);
    return this.repository.save(customer);
  }

  async update(id: number, customerData: Partial<Customer>): Promise<Customer | null> {
    // Basic validation for customer name if provided
    if (customerData.name && (typeof customerData.name !== 'string' || customerData.name.trim().length < 3)) {
      throw new Error('O nome do cliente deve ter pelo menos 3 caracteres.');
    }
    
    // Sanitize input data
    const trimmedData = { ...customerData };
    
    // Trim name if provided
    if (trimmedData.name) {
      trimmedData.name = trimmedData.name.trim();
    }
    
    // Handle empty strings for optional fields
    if (trimmedData.email === '') trimmedData.email = undefined;
    if (trimmedData.phone === '') trimmedData.phone = undefined;
    if (trimmedData.address === '') trimmedData.address = undefined;
    if (trimmedData.additional_notes === '') trimmedData.additional_notes = undefined;
    
    await this.repository.update(id, trimmedData);
    return this.findById(id);
  }

  async delete(id: number): Promise<boolean> {
    // Get all appointments for this customer
    const appointments = await this.appointmentService.findByCustomerId(id);
    
    // Cancel all future appointments
    for (const appointment of appointments) {
      if (new Date(appointment.appointment_date) > new Date()) {
        await this.appointmentService.updateStatus(appointment.id, 'cancelled');
      }
    }

    // Get all pets for this customer
    const petService = new PetService();
    const pets = await petService.findByCustomerId(id);

    // Update all pets to be hidden
    for (const pet of pets) {
      await petService.update(pet.id, { is_hidden: true });
    }

    // Soft delete the customer
    const result = await this.repository.update(id, { status: 'inactive' });
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }

  async deactivate(id: number): Promise<Customer | null> {
    try {
      const customer = await this.findById(id);
      if (!customer) return null;

      // Update customer status
      await this.repository.update(id, { status: 'inactive' });

      // Hide all pets associated with this customer
      const petService = new PetService();
      const pets = await petService.findByCustomerId(id);
      for (const pet of pets) {
        await petService.update(pet.id, { is_hidden: true });
      }

      return this.findById(id);
    } catch (error) {
      console.error('Error deactivating customer:', error);
      throw error;
    }
  }

  async reactivate(id: number): Promise<Customer | null> {
    try {
      const customer = await this.findById(id);
      if (!customer) return null;

      // Update customer status
      await this.repository.update(id, { status: 'active' });

      // Unhide all pets associated with this customer
      const petService = new PetService();
      const pets = await petService.findByCustomerId(id);
      for (const pet of pets) {
        await petService.update(pet.id, { is_hidden: false });
      }

      return this.findById(id);
    } catch (error) {
      console.error('Error reactivating customer:', error);
      throw error;
    }
  }

  async findInactive(): Promise<Customer[]> {
    return this.repository.find({ 
      where: { status: 'inactive' },
      order: { name: 'ASC' } 
    });
  }

  async getPetCount(id: number): Promise<number> {
    try {
      const result = await this.repository.count({
        where: { id: id, status: 'active' }
      });
      return result;
    } catch (error) {
      console.error('Error getting pet count:', error);
      throw error;
    }
  }
} 