## Customer Page Sorting and Display Update

**Objective**: Modify the customer display on the `Customers.tsx` page to prioritize customers with "no-show" package statuses and to include the customer creation date on their cards.

**Changes Implemented**:

1.  **`CustomerCard.tsx` Update**:
    *   Added a "Criado em" (Created At) field to each customer card.
    *   This field displays the `customer.created_at` date, formatted as DD/MM/YYYY.
    *   It appears below the customer's address information on the card.
    *   A helper function `simpleFormatDate` was added within `CustomerCard.tsx` for this specific date formatting.
    *   Relevant icon (`CalendarTodayIcon`) and `formatDate` (initially considered, then replaced by `simpleFormatDate`) imports were managed.

2.  **`Customers.tsx` Sorting Logic Update**:
    *   **Primary Sort**: Customers with one or more packages in the `on_hold_no_show` status are now displayed at the top of the list.
        *   The `useCustomerPackages` hook was added to `Customers.tsx`.
        *   A new state variable, `customersWithNoShow: Record<number, boolean>`, was introduced to store whether each customer has such a package.
        *   A `useEffect` hook was added to populate `customersWithNoShow`. When the `customers` list updates, this effect iterates through active customers, calls `fetchCustomerPackages(customer.id)` for each, and updates the state based on whether any of their packages have `status === 'on_hold_no_show'`.
    *   **Secondary Sort**: Customers are then sorted by their `created_at` timestamp in descending order (newest first).
    *   **Tertiary Sort**: A fallback sort by `customer.id` is used if `created_at` timestamps are identical.
    *   The existing `useMemo` hook for `filteredCustomers` was renamed and updated to `sortedAndFilteredCustomers` to incorporate this new multi-level sorting logic *after* the existing filtering (search term, active/inactive, pending payments) is applied.
    *   All parts of the component previously referencing `filteredCustomers` (e.g., for display in the virtualized list, for counts) were updated to use `sortedAndFilteredCustomers`.
    *   The `CellMeasurerCache` for the `react-virtualized` list is correctly cleared when `sortedAndFilteredCustomers` changes.

**Outcome**:
The customer list on the `Customers` page now presents customers requiring attention (due to no-shows) first, followed by a chronological listing (newest first). Each customer card also displays its creation date. The approach for identifying customers with no-show packages involves an iteration and fetching of package data within `Customers.tsx`, which might be an area for future optimization if performance issues arise with a very large number of active customers.
