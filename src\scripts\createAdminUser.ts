import 'reflect-metadata';
import bcrypt from 'bcryptjs';
import path from 'path';
import { AppDataSource } from '../main/database/connection';
import { User } from '../main/database/models/User';

// Get the same path that Electron would use
const appData = process.env.APPDATA || (process.platform === 'darwin' 
  ? path.join(process.env.HOME || '', 'Library', 'Application Support') 
  : path.join(process.env.HOME || '', '.config'));
const dbDir = path.join(appData, 'pet-shop-management');

// Ensure the directory exists
if (!require('fs').existsSync(dbDir)) {
  require('fs').mkdirSync(dbDir, { recursive: true });
}

const createAdminUser = async () => {
  try {
    // Initialize the database connection
    await AppDataSource.initialize();
    // console.log('Database connection established');

    const userRepository = AppDataSource.getRepository(User);

    // Check if admin user already exists
    const existingAdmin = await userRepository.findOne({
      where: { username: 'admin' }
    });

    if (existingAdmin) {
      // console.log('Admin user already exists');
      return;
    }

    // Create admin user
    const passwordHash = await bcrypt.hash('admin123', 10);
    const adminUser = userRepository.create({
      username: 'admin',
      password_hash: passwordHash,
      role: 'admin',
      created_at: new Date(),
    });

    await userRepository.save(adminUser);
    // console.log('Admin user created successfully');
    // console.log('Username: admin');
    // console.log('Password: admin123');
    // console.log('Please change the password after first login');

  } catch (error) {
    // console.error('Error creating admin user:', error);
  } finally {
    try {
      // Only destroy the connection if it was initialized
      if (AppDataSource.isInitialized) {
        await AppDataSource.destroy();
      }
    } catch (error) {
      // console.error('Error closing database connection:', error);
    }
  }
};

// Run the script
createAdminUser(); 