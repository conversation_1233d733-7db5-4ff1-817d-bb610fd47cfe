Recommended Publishing Workflow

1. Develop & Test: Implement new features or bug fixes.

2. Update package.json Version: Increment the version field in your package.json file (e.g., from 1.0.0 to 1.1.0).

3. Commit Changes: Commit your code changes, including the package.json update.

git add .
git commit -m "v1.1.0 release"

4. Create and Push Git Tag: Create an annotated Git tag that exactly matches the new version in package.json and push it to your remote repository.
      
git tag -a v1.1.0 -m "v1.1.0"
git push origin main
git push origin v1.1.0

5. Run electron-forge publish: Execute the publish command. This will build your app and upload the release assets to GitHub Releases, associated with the v1.1.0 tag.
      
electron-forge publish

Verify Release: Go to your GitHub repository's "Releases" tab and verify that the new release (v1.1.0) exists and contains the necessary files (installers, and latest.yml/latest.json).