import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Pet } from './Pet';

@Entity('customers')
export class Customer {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @Column({ nullable: true, default: null })
  email!: string;

  @Column({ nullable: true, default: null })
  phone!: string;

  @Column({ nullable: true, default: null })
  address!: string;

  @Column({ nullable: true, default: null })
  additional_notes!: string;

  @Column({ default: 'active' })
  status!: 'active' | 'inactive';

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;

  @OneToMany(() => Pet, pet => pet.customer)
  pets!: Pet[];
} 