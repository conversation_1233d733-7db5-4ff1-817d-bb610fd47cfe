import React from "react";
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Button from '@mui/material/Button';
import DialogActions from '@mui/material/DialogActions';
import ListItemButton from '@mui/material/ListItemButton';
import CardActions from '@mui/material/CardActions';
import Tooltip from '@mui/material/Tooltip';
import ListItemIcon from '@mui/material/ListItemIcon';
import Badge from '@mui/material/Badge';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Pets as PetsIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  Restore as RestoreIcon,
  CardGiftcard as CardGiftcardIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  PaymentOutlined as PaymentIcon,
  CalendarToday as CalendarTodayIcon,
} from "@mui/icons-material";
import { Customer } from "../../types/customers";
import { useNavigate } from "react-router-dom";
import { useCustomerPackages } from "../../hooks/useCustomerPackages";
import { formatDate } from "../../types/sales";

interface CustomerCardProps {
  customer: Customer;
  onEdit: () => void;
  onDelete: () => void;
  onViewPets: () => void;
  onViewPackages: () => void;
  onReactivate: () => void;
  onViewPendingSales?: () => void;
  isInactive: boolean;
  hideActions?: boolean;
  // noShowPackageCount?: number;
}

export const CustomerCard: React.FC<CustomerCardProps> = ({
  customer,
  onEdit,
  onDelete,
  onViewPets,
  onViewPackages,
  onReactivate,
  onViewPendingSales,
  isInactive,
  hideActions,
  // noShowPackageCount
}) => {
  const navigate = useNavigate();
  const [pets, setPets] = React.useState<any[]>([]);
  const [petsDialogOpen, setPetsDialogOpen] = React.useState(false);
  const { getCustomerPackages } = useCustomerPackages();
  const [noShowPackageCount, setNoShowPackageCount] = React.useState(0);

  React.useEffect(() => {
    if (customer && customer.id && !isInactive) {
      getCustomerPackages(customer.id)
        .then(packages => {
          const count = packages.filter(pkg => pkg.status === 'on_hold_no_show').length;
          setNoShowPackageCount(count);
        })
        .catch(error => {
          console.error("Error fetching customer packages for badge:", error);
          setNoShowPackageCount(0);
        });
    } else {
      setNoShowPackageCount(0);
    }
  }, [customer, customer.id, getCustomerPackages, isInactive]);

  const handleEdit = () => {
    onEdit();
  };

  const handleDelete = () => {
    onDelete();
  };

  const handleViewPets = async () => {
    if (hideActions) return;

    try {
      const response = await window.electronAPI.invoke(
        "pets:getByCustomerIdIncludingHidden",
        customer.id,
      );
      if (response.success) {
        const activePets = response.data.filter((pet: any) => !pet.is_hidden);
        setPets(activePets);
        setPetsDialogOpen(true);
      }
    } catch (error) {
      console.error("Error fetching pets:", error);
    }
  };

  const handleClosePetsDialog = () => {
    setPetsDialogOpen(false);
  };

  const handleReactivate = () => {
    onReactivate();
  };

  const navigateToPet = (petId: number) => {
    setPetsDialogOpen(false);
    navigate(`/pets?pet=${petId}`);
  };

  // Use customer name initials for the avatar
  const getInitials = (name: string) => {
    const words = name.split(" ");
    if (words.length >= 2) {
      return `${words[0][0]}${words[1][0]}`.toUpperCase();
    } else if (words.length === 1 && words[0].length > 0) {
      return words[0][0].toUpperCase();
    }
    return "C";
  };

  // Format phone number for display
  const formatPhone = (phone: string | null) => {
    if (!phone) return null;
    return phone;
  };

  const simpleFormatDate = (dateString?: string) => {
    if (!dateString) return 'Data não informada';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Data inválida';
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }).format(date);
    } catch (e) {
      return 'Data inválida';
    }
  };

  return (
    <Card
      sx={{
        position: "relative",
        opacity: isInactive ? 0.7 : 1,
        transition: "all 0.2s",
        "&:hover": {
          boxShadow: 3,
          transform: "translateY(-2px)",
        },
      }}
    >
      {isInactive && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            backgroundColor: "error.main",
            color: "white",
            px: 1,
            py: 0.5,
            borderBottomLeftRadius: 8,
          }}
        >
          <Typography variant="caption" fontWeight="bold">
            INATIVO
          </Typography>
        </Box>
      )}

      {!isInactive && customer.hasPendingPayments && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            backgroundColor: "warning.main",
            color: "white",
            px: 1,
            py: 0.5,
            borderBottomLeftRadius: 8,
            display: "flex",
            alignItems: "center",
            gap: 0.5,
          }}
        >
          <WarningIcon fontSize="small" />
          <Typography variant="caption" fontWeight="bold">
            PAGAMENTO PENDENTE
          </Typography>
        </Box>
      )}

      <CardContent
        sx={{
          flexGrow: 1,
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          minHeight: "262px",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
          <Avatar sx={{ mr: 2, bgcolor: "primary.main" }}>
            {getInitials(customer.name)}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {customer.name}
            </Typography>
          </Box>
        </Box>

        <Stack spacing={1} sx={{ mb: 1 }}>
          {customer.email ? (
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <EmailIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary" noWrap>
                {customer.email}
              </Typography>
            </Box>
          ) : (
            <Box sx={{ display: "flex", alignItems: "center", opacity: 0.5 }}>
              <EmailIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary">
                Sem email
              </Typography>
            </Box>
          )}
          {customer.phone ? (
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <PhoneIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary">
                {formatPhone(customer.phone)}
              </Typography>
            </Box>
          ) : (
            <Box sx={{ display: "flex", alignItems: "center", opacity: 0.5 }}>
              <PhoneIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary">
                Sem telefone
              </Typography>
            </Box>
          )}
          {customer.address ? (
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <LocationIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary" noWrap>
                {customer.address}
              </Typography>
            </Box>
          ) : (
            <Box sx={{ display: "flex", alignItems: "center", opacity: 0.5 }}>
              <LocationIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary">
                Sem endereço
              </Typography>
            </Box>
          )}
          {customer.created_at && (
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <CalendarTodayIcon
                sx={{ mr: 1, color: "text.secondary", fontSize: "1rem" }}
              />
              <Typography variant="body2" color="text.secondary" noWrap>
                Criado em: {simpleFormatDate(customer.created_at)}
              </Typography>
            </Box>
          )}
        </Stack>

        <Box sx={{ flexGrow: 1 }}>
          {customer.additional_notes && (
            <>
              <Divider sx={{ my: 1.5 }} />
              <Box>
                <Typography
                  variant="caption"
                  fontWeight="medium"
                  color="primary"
                  display="block"
                  sx={{ mb: 0.5 }}
                >
                  Observações
                </Typography>
                <Box
                  sx={{
                    maxHeight: "85px",
                    overflow: "auto",
                    bgcolor: "background.paper",
                    p: 1.5,
                    borderRadius: 1,
                    borderLeft: "3px solid",
                    borderColor: "primary.light",
                    boxShadow: "inset 0 0 6px rgba(0,0,0,0.05)",
                    "&::-webkit-scrollbar": {
                      width: "6px",
                    },
                    "&::-webkit-scrollbar-thumb": {
                      backgroundColor: "rgba(0,0,0,0.1)",
                      borderRadius: "4px",
                    },
                  }}
                >
                  <Typography
                    variant="body2"
                    color="text.primary"
                    sx={{
                      whiteSpace: "pre-line",
                      fontSize: "0.875rem",
                      lineHeight: 1.5,
                    }}
                  >
                    {customer.additional_notes}
                  </Typography>
                </Box>
              </Box>
            </>
          )}
        </Box>
      </CardContent>

      {!hideActions && (
        <CardActions
          disableSpacing
          sx={{
            mt: 'auto',
            pt: 0,
            pb: 1,
            px: 1,
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {!isInactive && (
              <>
                <Tooltip title="Pets do Cliente">
                  <Button onClick={handleViewPets} size="small" startIcon={<PetsIcon />}>
                    Pets
                  </Button>
                </Tooltip>
                <Tooltip title="Pacotes do Cliente">
                  <Badge badgeContent={noShowPackageCount} color="error" invisible={noShowPackageCount === 0}>
                    <Button onClick={onViewPackages} size="small" startIcon={<CardGiftcardIcon />}>
                      Pacotes
                    </Button>
                  </Badge>
                </Tooltip>

                {customer.hasPendingPayments && onViewPendingSales && (
                  <Tooltip title="Pagamentos Pendentes">
                    <Badge
                      badgeContent={customer.pendingSales?.length || 0}
                      color="warning"
                      overlap="circular"
                      sx={{
                        "& .MuiBadge-badge": {
                          fontSize: "0.6rem",
                          height: "16px",
                          minWidth: "16px",
                        },
                      }}
                    >
                      <IconButton
                        onClick={onViewPendingSales}
                        size="small"
                        color="warning"
                      >
                        <PaymentIcon />
                      </IconButton>
                    </Badge>
                  </Tooltip>
                )}
              </>
            )}
          </Box>
          <Box>
            {isInactive ? (
              <Tooltip title="Reativar Cliente">
                <IconButton
                  onClick={handleReactivate}
                  size="small"
                  color="success"
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            ) : (
              <>
                <Tooltip title="Editar Cliente">
                  <IconButton onClick={handleEdit} size="small" color="primary">
                    <EditIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Desativar Cliente">
                  <IconButton onClick={handleDelete} size="small" color="error">
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              </>
            )}
          </Box>
        </CardActions>
      )}

      <Dialog
        open={petsDialogOpen}
        onClose={handleClosePetsDialog}
        aria-labelledby="pets-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="pets-dialog-title"
          sx={{
            borderBottom: 1,
            borderColor: "divider",
            mb: 2,
            display: "flex",
            alignItems: "center",
            gap: 1,
            pb: 2,
          }}
        >
          <PetsIcon color="primary" />
          <Typography component="span" variant="h6">
            Pets de {customer.name}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {pets.length === 0 ? (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                py: 4,
                textAlign: "center",
              }}
            >
              <PetsIcon
                sx={{
                  fontSize: 60,
                  color: "text.secondary",
                  opacity: 0.3,
                  mb: 2,
                }}
              />
              <Typography variant="h6" color="text.secondary">
                Nenhum pet encontrado
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Este cliente não possui pets cadastrados
              </Typography>
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {pets.map((pet) => {
                // Get pet type icon and color
                const getPetTypeIcon = (type: string) => {
                  switch (type?.toLowerCase()) {
                    case "cachorro":
                    case "dog":
                      return "🐕";
                    case "gato":
                    case "cat":
                      return "🐈";
                    case "pássaro":
                    case "bird":
                    case "ave":
                      return "🦜";
                    case "coelho":
                    case "rabbit":
                      return "🐇";
                    case "hamster":
                    case "pequeno roedor":
                      return "🐹";
                    case "peixe":
                    case "fish":
                      return "🐠";
                    case "réptil":
                    case "reptil":
                      return "🦎";
                    default:
                      return "🐾";
                  }
                };

                const getPetSizeLabel = (size: string) => {
                  switch (size?.toLowerCase()) {
                    case "pequeno":
                    case "small":
                      return "Pequeno";
                    case "médio":
                    case "medium":
                      return "Médio";
                    case "grande":
                    case "large":
                      return "Grande";
                    default:
                      return size || "Não informado";
                  }
                };

                return (
                  <Box
                    key={pet.id}
                    component="div"
                    sx={{
                      mb: 1.5,
                      border: "1px solid",
                      borderColor: "divider",
                      borderRadius: 1,
                      overflow: "hidden",
                      "&:hover": {
                        borderColor: "primary.main",
                        boxShadow: 1,
                      },
                      transition: "all 0.2s",
                    }}
                  >
                    <ListItemButton
                      onClick={() => navigateToPet(pet.id)}
                      sx={{ p: 0 }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          width: "100%",
                        }}
                      >
                        <Box
                          sx={{
                            width: 50,
                            height: "100%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "primary.50",
                            fontSize: "1.8rem",
                          }}
                        >
                          {getPetTypeIcon(pet.type)}
                        </Box>
                        <Box
                          sx={{
                            flex: 1,
                            p: 1.5,
                            display: "flex",
                            flexDirection: "column",
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <Typography variant="subtitle1" fontWeight="medium">
                              {pet.name}
                            </Typography>
                            {pet.age && (
                              <Chip
                                label={`${pet.age} anos`}
                                size="small"
                                color="default"
                                sx={{ height: 22 }}
                              />
                            )}
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              gap: 1,
                              alignItems: "center",
                              mt: 0.5,
                            }}
                          >
                            {pet.breed && (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {pet.breed}
                              </Typography>
                            )}
                            {pet.size && (
                              <>
                                <Box
                                  component="span"
                                  sx={{
                                    width: 3,
                                    height: 3,
                                    borderRadius: "50%",
                                    backgroundColor: "text.disabled",
                                  }}
                                />
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {getPetSizeLabel(pet.size)}
                                </Typography>
                              </>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </ListItemButton>
                  </Box>
                );
              })}
            </List>
          )}
        </DialogContent>
        <DialogActions
          sx={{ borderTop: 1, borderColor: "divider", px: 3, py: 1.5 }}
        >
          <Button onClick={handleClosePetsDialog} variant="outlined">
            Fechar
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};
