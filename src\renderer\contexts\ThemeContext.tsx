import React, { createContext, useContext, useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { theme as defaultTheme } from '../theme';
import { alpha } from '@mui/material';

interface ThemeContextType {
  primaryColor: string;
  secondaryColor: string;
  updateTheme: (primary: string, secondary: string) => void;
}

const ThemeContext = createContext<ThemeContextType>({
  primaryColor: defaultTheme.palette.primary.main,
  secondaryColor: defaultTheme.palette.secondary.main,
  updateTheme: () => {},
});

export const useAppTheme = () => useContext(ThemeContext);

// Debounce function
const useDebounce = <T extends any>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Custom hook for debounced localstorage writes
const useDebounceLocalStorage = (delay = 300) => {
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  const debouncedSave = useCallback((key: string, value: any) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    
    timerRef.current = setTimeout(() => {
      try {
        localStorage.setItem(key, JSON.stringify(value));
        console.log("Theme saved to localStorage"); // For debugging
      } catch (error) {
        console.error("Error saving to localStorage:", error);
      }
      timerRef.current = null;
    }, delay);
  }, [delay]);

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return debouncedSave;
};

export const ThemeProviderWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [primaryColor, setPrimaryColor] = useState<string>(defaultTheme.palette.primary.main);
  const [secondaryColor, setSecondaryColor] = useState<string>(defaultTheme.palette.secondary.main);
  
  // Debounce color values for smoother UI with color picker
  const debouncedPrimaryColor = useDebounce(primaryColor, 100);
  const debouncedSecondaryColor = useDebounce(secondaryColor, 100);
  
  // Debounced localStorage save
  const saveToLocalStorage = useDebounceLocalStorage(300); // 300ms debounce

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('appTheme');
    if (savedTheme) {
      try {
        const themeColors = JSON.parse(savedTheme);
        setPrimaryColor(themeColors.primary);
        setSecondaryColor(themeColors.secondary);
      } catch (error) {
        console.error('Error parsing saved theme:', error);
      }
    }
  }, []);

  // Memoize the theme object calculation
  const currentTheme = useMemo(() => {
    console.log("Recalculating theme object..."); // For debugging
    return createTheme({
      ...defaultTheme,
      palette: {
        ...defaultTheme.palette,
        primary: {
          main: debouncedPrimaryColor,
          light: alpha(debouncedPrimaryColor, 0.8),
          dark: alpha(debouncedPrimaryColor, 1),
        },
        secondary: {
          main: debouncedSecondaryColor,
          light: alpha(debouncedSecondaryColor, 0.8),
          dark: alpha(debouncedSecondaryColor, 1),
        },
      },
      components: {
        ...defaultTheme.components,
        MuiButton: {
          styleOverrides: {
            root: {
              borderRadius: '8px',
              boxShadow: 'none',
              padding: '8px 16px',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              },
            },
            containedPrimary: {
              '&:hover': {
                backgroundColor: alpha(debouncedPrimaryColor, 0.9),
              },
            },
            containedSecondary: {
              '&:hover': {
                backgroundColor: alpha(debouncedSecondaryColor, 0.9),
              },
            },
            outlinedPrimary: {
              borderColor: debouncedPrimaryColor,
              '&:hover': {
                borderColor: debouncedPrimaryColor,
                backgroundColor: alpha(debouncedPrimaryColor, 0.1),
              },
            },
            outlinedSecondary: {
              borderColor: debouncedSecondaryColor,
              '&:hover': {
                borderColor: debouncedSecondaryColor,
                backgroundColor: alpha(debouncedSecondaryColor, 0.1),
              },
            },
            textPrimary: {
              '&:hover': {
                backgroundColor: alpha(debouncedPrimaryColor, 0.1),
              },
            },
            textSecondary: {
              '&:hover': {
                backgroundColor: alpha(debouncedSecondaryColor, 0.1),
              },
            },
          },
        },
        MuiDrawer: {
          styleOverrides: {
            paper: {
              backgroundColor: debouncedPrimaryColor,
              backgroundImage: `linear-gradient(to bottom, ${alpha(debouncedPrimaryColor, 1)}, ${debouncedPrimaryColor})`,
              color: '#fff',
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: '3px',
              },
            },
          },
        },
        MuiCssBaseline: {
          styleOverrides: {
            '*': {
              scrollbarWidth: 'thin',
              scrollbarColor: `${alpha(debouncedPrimaryColor, 0.6)} transparent`,
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: alpha(debouncedPrimaryColor, 0.6),
                borderRadius: '4px',
                transition: 'background-color 0.3s ease',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                backgroundColor: debouncedPrimaryColor,
              }
            },
          },
        },
      },
    });
  }, [debouncedPrimaryColor, debouncedSecondaryColor]); // Only recalculate when debounced values change

  const updateTheme = useCallback((primary: string, secondary: string) => {
    setPrimaryColor(primary);
    setSecondaryColor(secondary);

    // Debounced save to localStorage
    saveToLocalStorage('appTheme', { primary, secondary });
  }, [saveToLocalStorage]);

  // Memoize the context value to prevent unnecessary re-renders of consumers
  const contextValue = useMemo(() => ({
    primaryColor,
    secondaryColor,
    updateTheme,
  }), [primaryColor, secondaryColor, updateTheme]);

  return (
    <ThemeContext.Provider
      value={contextValue}
    >
      <ThemeProvider theme={currentTheme}>
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
}; 