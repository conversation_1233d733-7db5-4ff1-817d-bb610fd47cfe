import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider';
import Fab from '@mui/material/Fab';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import useTheme from '@mui/material/styles/useTheme';
import { 
  Add as AddIcon, 
  Search as SearchIcon,
  FilterList as FilterIcon,
  Pets as PetsIcon
} from '@mui/icons-material';
import { PetCard } from '../components/Pets/PetCard';
import { PetFormDialog } from '../components/Pets/PetFormDialog';
import { PetServicesDialog } from '../components/Pets/PetServicesDialog';
import { AddServiceDialog } from '../components/Pets/AddServiceDialog';
import { Pet, PetFormData, PetService } from '../types/pets';
import { useCustomers } from '../hooks/useCustomers';
import { usePets } from '../hooks/usePets';
import { useServices } from '../hooks/useServices';
// Import React Virtualized components
import {
  WindowScroller,
  AutoSizer,
  List,
  CellMeasurer,
  CellMeasurerCache
} from 'react-virtualized';
import { WindowScrollerChildProps } from 'react-virtualized/dist/es/WindowScroller';
import { ListRowProps } from 'react-virtualized/dist/es/List';

// Helper function to adapt database pet model to frontend type
const adaptPet = (dbPet: any): Pet => ({
  id: dbPet.id,
  customer_id: dbPet.customer?.id || 0,
  name: dbPet.name,
  type: dbPet.type,
  breed: dbPet.breed,
  age: dbPet.age,
  size: dbPet.size,
  gender: dbPet.gender,
  fur_type: dbPet.fur_type,
  additional_notes: dbPet.additional_notes,
  photo_url: dbPet.photo_url || null,
  is_hidden: dbPet.is_hidden || false,
  status: dbPet.status || 'active',
  created_at: dbPet.created_at,
  updated_at: dbPet.updated_at,
  customer: dbPet.customer ? {
    id: dbPet.customer.id,
    name: dbPet.customer.name,
    email: dbPet.customer.email,
    phone: dbPet.customer.phone
  } : undefined
});

// Helper function to adapt database customer model to frontend type compatible with PetFormDialog
const adaptCustomer = (dbCustomer: any): any => ({
  id: dbCustomer.id,
  name: dbCustomer.name,
  email: dbCustomer.email || null,
  phone: dbCustomer.phone || null,
  address: dbCustomer.address || null,
  additional_notes: dbCustomer.additional_notes || null,
  created_at: dbCustomer.created_at,
  updated_at: dbCustomer.updated_at
});

// Mock data for pet services (will be replaced later with real API)
const mockServices: PetService[] = [
  {
    id: 1,
    pet_id: 1,
    service_name: 'Banho Completo',
    service_date: '2023-06-15T13:30:00',
    notes: 'Inclui banho, tosa, corte de unhas e limpeza de orelhas.'
  },
  {
    id: 2,
    pet_id: 1,
    service_name: 'Vacinação',
    service_date: '2023-05-20T10:15:00',
    notes: 'Vacina anual contra raiva e reforço da múltipla.'
  },
  {
    id: 3,
    pet_id: 2,
    service_name: 'Limpeza Dental',
    service_date: '2023-06-10T14:45:00',
    notes: 'Tártaro leve removido, sem necessidade de extrações.'
  },
  {
    id: 4,
    pet_id: 3,
    service_name: 'Banho & Escovação',
    service_date: '2023-06-08T11:30:00',
    notes: 'Banho básico e escovação. Cliente solicitou uso do próprio shampoo.'
  },
];

const mockAvailableServices = [
  { id: 1, name: 'Banho Completo' },
  { id: 2, name: 'Banho & Escovação' },
  { id: 3, name: 'Corte de Unhas' },
  { id: 4, name: 'Limpeza de Orelhas' },
  { id: 5, name: 'Limpeza Dental' },
  { id: 6, name: 'Vacinação' },
  { id: 7, name: 'Consulta de Saúde' },
  { id: 8, name: 'Tratamento contra Pulgas' },
];

const Pets: React.FC = () => {
  const theme = useTheme();
  // Use hooks for real data
  const { 
    pets: dbPets, 
    loading: petsLoading, 
    error: petsError,
    createPet,
    updatePet,
    deletePet: deletePetFromDb,
    refetch: refetchPets
  } = usePets();

  const {
    customers: dbCustomers,
    loading: customersLoading,
    error: customersError
  } = useCustomers();
  
  // Use services hook to get available services from database
  const {
    services: dbServices,
    loading: servicesDataLoading,
    error: servicesError
  } = useServices();

  // Convert database models to frontend types
  const pets = useMemo(() => dbPets ? dbPets.map(adaptPet) : [], [dbPets]);
  const customers = useMemo(() => dbCustomers ? dbCustomers.map(adaptCustomer) : [], [dbCustomers]);

  const [searchTerm, setSearchTerm] = useState('');
  const [inputValue, setInputValue] = useState(''); // New state for immediate input value
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref to store timeout ID
  
  // Optimize the filtered pets calculation
  const filteredPets = useMemo(() => {
    if (!searchTerm.trim()) {
      return pets;
    }
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    return pets.filter(pet => 
      pet.name.toLowerCase().includes(lowerSearchTerm) ||
      pet.type.toLowerCase().includes(lowerSearchTerm) ||
      (pet.breed && pet.breed.toLowerCase().includes(lowerSearchTerm)) ||
      (pet.customer && pet.customer.name.toLowerCase().includes(lowerSearchTerm))
    );
  }, [searchTerm, pets]);
  
  const [formOpen, setFormOpen] = useState(false);
  const [selectedPet, setSelectedPet] = useState<Pet | undefined>(undefined);
  
  const [servicesDialogOpen, setServicesDialogOpen] = useState(false);
  const [addServiceDialogOpen, setAddServiceDialogOpen] = useState(false);
  const [currentServices, setCurrentServices] = useState<PetService[]>([]);
  const [servicesLoading, setServicesLoading] = useState(false);
  
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [petToDelete, setPetToDelete] = useState<number | null>(null);
  
  // Add state for service deletion
  const [deleteServiceDialogOpen, setDeleteServiceDialogOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<number | null>(null);

  // Add CellMeasurerCache for variable height rows
  const cache = useMemo(() => new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 280, // Changed from 300 to match PetCard's minHeight
    minHeight: 280,     // Changed from 250 to match PetCard's actual height
    keyMapper: (rowIndex: number) => rowIndex, // Add key mapper for improved cache lookup
  }), []);
  
  // Reset cache when filtered pets change
  useEffect(() => {
    if (filteredPets && filteredPets.length > 0) {
      cache.clearAll();
    }
  }, [filteredPets, cache]);

  // Calculate number of columns based on screen width
  const getColumnCount = useCallback((width: number) => {
    if (width < 600) return 1; // Mobile view
    if (width < 960) return 2; // Tablet view
    return 3; // Desktop view - 3 columns
  }, []);

  // Calculate row count based on items and columns
  const getRowCount = useCallback((itemCount: number, columnCount: number) => {
    return Math.ceil(itemCount / columnCount);
  }, []);

  // Get item at index accounting for column layout
  const getItemAtIndex = useCallback((items: Pet[], rowIndex: number, columnIndex: number, columnCount: number) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    return itemIndex < items.length ? items[itemIndex] : null;
  }, []);

  // Memoize the handler functions
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setInputValue(value); // Update the input field immediately
    
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set a new timeout to update the search term after 150ms
    searchTimeoutRef.current = setTimeout(() => {
      setSearchTerm(value);
    }, 150);
  }, []);
  
  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
        searchTimeoutRef.current = null;
      }
    };
  }, []);

  const handleAddPet = useCallback(() => {
    setSelectedPet(undefined);
    setFormOpen(true);
  }, []);

  const handleEditPet = useCallback((pet: Pet) => {
    setSelectedPet(pet);
    setFormOpen(true);
  }, []);

  const handleSavePet = useCallback(async (petData: PetFormData, imageFile?: File | null) => {
    try {
      // Convert the imageFile to base64 for storage if it exists
      let photoUrl = petData.photo_url;
      
      if (imageFile) {
        // Convert the file to base64 for storage
        const base64String = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (event) => {
            resolve(event.target?.result as string);
          };
          reader.readAsDataURL(imageFile);
        });
        
        photoUrl = base64String;
      }
      
      if (selectedPet) {
        // Update existing pet
        await updatePet(selectedPet.id, {
          customer_id: petData.customer_id,
          name: petData.name,
          type: petData.type,
          breed: petData.breed || null,
          age: petData.age,
          size: petData.size || null,
          gender: petData.gender || null,
          fur_type: petData.fur_type || null,
          additional_notes: petData.additional_notes || null,
          photo_url: photoUrl
        } as any);
      } else {
        // Add new pet
        await createPet({
          customer_id: petData.customer_id,
          name: petData.name,
          type: petData.type,
          breed: petData.breed || null,
          age: petData.age,
          size: petData.size || null,
          gender: petData.gender || null,
          fur_type: petData.fur_type || null,
          additional_notes: petData.additional_notes || null,
          photo_url: photoUrl
        } as any);
      }
      
      // Refresh the pet list
      await refetchPets();
      setFormOpen(false);
    } catch (error) {
      console.error('Error saving pet:', error);
    }
  }, [selectedPet, updatePet, createPet, refetchPets, setFormOpen]);

  const handleDeletePet = useCallback((petId: number) => {
    setPetToDelete(petId);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeletePet = useCallback(async () => {
    if (petToDelete) {
      try {
        await deletePetFromDb(petToDelete);
        await refetchPets();
        setDeleteDialogOpen(false);
        setPetToDelete(null);
      } catch (error) {
        console.error('Error deleting pet:', error);
      }
    }
  }, [petToDelete, deletePetFromDb, refetchPets, setDeleteDialogOpen, setPetToDelete]);

  const handleViewServices = useCallback(async (pet: Pet) => {
    setSelectedPet(pet);
    setServicesLoading(true);
    
    // Fetch real pet services data from the database
    try {
      // Call the API to get services for this pet
      const response = await window.electronAPI.invoke('petServices:getByPetId', pet.id);
      
      if (response.success) {
        // Format the pet services data
        const petServices = response.data.map((service: any) => ({
          id: service.id,
          pet_id: service.pet_id,
          service_name: service.service_name,
          service_date: service.service_date,
          notes: service.notes
        }));
        
        setCurrentServices(petServices);
      } else {
        // If there's an error, set empty services
        console.error('Error fetching pet services:', response.error);
        setCurrentServices([]);
      }
    } catch (error) {
      console.error('Error fetching pet services:', error);
      // Fallback to empty array if there's an error
      setCurrentServices([]);
    } finally {
      setServicesLoading(false);
    }
    
    setServicesDialogOpen(true);
  }, [setSelectedPet, setCurrentServices, setServicesDialogOpen]);

  const handleAddService = useCallback((petId: number) => {
    setAddServiceDialogOpen(true);
  }, []);

  const handleDeleteService = useCallback((serviceId: number) => {
    setServiceToDelete(serviceId);
    setDeleteServiceDialogOpen(true);
  }, []);

  const confirmDeleteService = useCallback(async () => {
    if (serviceToDelete === null) return;
    
    try {
      // Call the API to delete the service
      const response = await window.electronAPI.invoke('petServices:delete', serviceToDelete);
      
      if (response.success) {
        // Update the services list
        setCurrentServices(prevServices => 
          prevServices.filter(service => service.id !== serviceToDelete)
        );
      } else {
        console.error('Error deleting service:', response.error);
      }
    } catch (error) {
      console.error('Error deleting service:', error);
    } finally {
      // Close the dialog
      setDeleteServiceDialogOpen(false);
      setServiceToDelete(null);
    }
  }, [serviceToDelete, setCurrentServices, setDeleteServiceDialogOpen, setServiceToDelete]);

  const handleSaveService = useCallback(async (petId: number, serviceData: { service_name: string; service_date: string; notes: string }) => {
    try {
      // Call the API to create a service
      const response = await window.electronAPI.invoke('petServices:create', {
        pet_id: petId,
        service_name: serviceData.service_name,
        service_date: serviceData.service_date,
        notes: serviceData.notes
      });
      
      if (response.success) {
        // Fetch services again to get the updated list
        const servicesResponse = await window.electronAPI.invoke('petServices:getByPetId', petId);
        
        if (servicesResponse.success) {
          const petServices = servicesResponse.data.map((service: any) => ({
            id: service.id,
            pet_id: service.pet_id,
            service_name: service.service_name,
            service_date: service.service_date,
            notes: service.notes
          }));
          
          setCurrentServices(petServices);
        }
      } else {
        console.error('Error creating service:', response.error);
      }
    } catch (error) {
      console.error('Error creating service:', error);
    }
    
    setAddServiceDialogOpen(false);
  }, [setCurrentServices, setAddServiceDialogOpen]);

  // Improve the virtualized list rendering to reduce lag - fix TypeScript errors
  const rowRenderer = useCallback(({ index, key, style, parent }: ListRowProps) => {
    return (
      <CellMeasurer
        cache={cache}
        columnIndex={0}
        key={key}
        parent={parent}
        rowIndex={index}
      >
        {({ measure }) => (
          <div 
            style={{ 
              ...style, 
              padding: '8px',
              boxSizing: 'border-box',
              display: 'flex'
            }}
          >
            {/* Create a row with potentially multiple columns */}
            {Array.from({ length: getColumnCount(parent.props.width) }).map((_, colIndex) => {
              const pet = getItemAtIndex(filteredPets, index, colIndex, getColumnCount(parent.props.width));
              if (!pet) return <div key={colIndex} style={{ flex: 1 }} />;
              
              return (
                <div 
                  key={colIndex} 
                  style={{ 
                    flex: 1, 
                    padding: '0 8px',
                    boxSizing: 'border-box'
                  }}
                >
                  <PetCard 
                    pet={pet}
                    onEdit={() => handleEditPet(pet)}
                    onDelete={() => handleDeletePet(pet.id)}
                    onViewServices={() => handleViewServices(pet)}
                  />
                </div>
              );
            })}
          </div>
        )}
      </CellMeasurer>
    );
  }, [filteredPets, cache, handleEditPet, handleDeletePet, handleViewServices, getColumnCount, getItemAtIndex]);

  // Memoize the virtualized list to reduce render costs
  interface VirtualListProps {
    height: number;
    isScrolling: boolean;
    onChildScroll: any;
    scrollTop: number;
    width: number;
  }

  const renderVirtualizedList = useCallback(({ 
    height, 
    isScrolling, 
    onChildScroll, 
    scrollTop, 
    width 
  }: VirtualListProps) => {
    const columnCount = getColumnCount(width);
    const rowCount = getRowCount(filteredPets.length, columnCount);
    
    return (
      <List
        autoHeight
        height={height || 500}
        isScrolling={isScrolling}
        onScroll={onChildScroll}
        rowCount={rowCount}
        rowHeight={cache.rowHeight}
        scrollTop={scrollTop}
        width={width}
        overscanRowCount={5} // Increased from 3 to 5 for smoother scrolling experience
        deferredMeasurementCache={cache}
        style={{
          outline: 'none',
          paddingBottom: '20px',
          overflowX: 'hidden'
        }}
        rowRenderer={rowRenderer}
      />
    );
  }, [filteredPets.length, cache, getColumnCount, getRowCount, rowRenderer]);

  // Handle loading states
  const loading = petsLoading || customersLoading || servicesDataLoading;
  const error = petsError || customersError || servicesError;

  // Add these handler functions for all dialogs
  const handleCloseForm = useCallback(() => {
    setFormOpen(false);
  }, []);

  const handleCloseServicesDialog = useCallback(() => {
    setServicesDialogOpen(false);
  }, []);

  const handleCloseAddServiceDialog = useCallback(() => {
    setAddServiceDialogOpen(false);
  }, []);

  const handleCloseDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(false);
  }, []);

  const handleCloseDeleteServiceDialog = useCallback(() => {
    setDeleteServiceDialogOpen(false);
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar dados: {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PetsIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 'bold', 
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 4,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 2,
              }
            }}
          >
            Gerenciamento de Pets
          </Typography>
        </Box>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
          onClick={handleAddPet}
        >
          Adicionar Pet
        </Button>
      </Box>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Buscar pets por nome, tipo, raça ou proprietário"
          variant="outlined"
          value={inputValue}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Divider sx={{ mb: 3 }} />
      
      {filteredPets.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Nenhum pet encontrado. Adicione um novo pet para começar.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ height: 'calc(100vh - 250px)', width: '100%' }}>
          <WindowScroller>
            {({ height, isScrolling, onChildScroll, scrollTop }) => (
              <AutoSizer disableHeight>
                {({ width }) => renderVirtualizedList({ 
                  height, 
                  isScrolling, 
                  onChildScroll, 
                  scrollTop, 
                  width 
                })}
              </AutoSizer>
            )}
          </WindowScroller>
        </Box>
      )}

      {/* Pet form dialog */}
      <PetFormDialog 
        open={formOpen}
        onClose={handleCloseForm}
        onSave={handleSavePet}
        pet={selectedPet}
        title={selectedPet ? 'Editar Pet' : 'Adicionar Novo Pet'}
        customers={customers}
      />

      {/* Pet services dialog */}
      <PetServicesDialog
        open={servicesDialogOpen}
        onClose={handleCloseServicesDialog}
        pet={selectedPet}
        services={currentServices}
        onAddService={handleAddService}
        onDeleteService={handleDeleteService}
      />

      {/* Add service dialog */}
      <AddServiceDialog
        open={addServiceDialogOpen}
        onClose={handleCloseAddServiceDialog}
        petId={selectedPet?.id || 0}
        petName={selectedPet?.name || ''}
        onSave={handleSaveService}
        availableServices={dbServices.map(service => ({ 
          id: service.id, 
          name: service.name 
        }))}
      />

      {/* Confirm delete dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Deseja excluir este pet? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancelar</Button>
          <Button onClick={confirmDeletePet} color="error">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm delete service dialog */}
      <Dialog
        open={deleteServiceDialogOpen}
        onClose={handleCloseDeleteServiceDialog}
      >
        <DialogTitle>Confirmar Exclusão de Serviço</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Deseja excluir este serviço? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteServiceDialog}>Cancelar</Button>
          <Button onClick={confirmDeleteService} color="error">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default React.memo(Pets); 