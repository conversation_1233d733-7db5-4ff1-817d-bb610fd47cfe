import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCostPriceToProducts1730000000000 implements MigrationInterface {
    name = 'AddCostPriceToProducts1730000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the column already exists
        const table = await queryRunner.getTable("products");
        const costPriceColumn = table?.findColumnByName("cost_price");
        
        if (!costPriceColumn) {
            await queryRunner.query(`ALTER TABLE "products" ADD COLUMN "cost_price" DECIMAL(10,2) NOT NULL DEFAULT 0`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("products");
        const costPriceColumn = table?.findColumnByName("cost_price");
        
        if (costPriceColumn) {
            await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "cost_price"`);
        }
    }
} 