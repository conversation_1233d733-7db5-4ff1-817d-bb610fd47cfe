## [2024-06-09] Package Appointment Notifications & Sale Dialog Logic

### What was implemented:
- Created `usePackageCompletionNotification` utility to send notifications when a package appointment is completed or when the next package appointment is scheduled. 
- Integrated this utility into both `Dashboard.tsx` and `Appointments.tsx` so that notifications are triggered immediately after marking an appointment as completed.
- Ensured notifications use PT-BR locale and Brazil timezone for all date/time formatting.
- Updated types to ensure correct handling of next scheduled appointment fields.

### Justification:
- This feature is required to keep admins informed about the lifecycle of package appointments, especially for recurring services.
- The notification logic was not present in any existing utility or hook, and a thorough search confirmed no duplicative logic. The new utility is modular and reusable.
- All changes follow the codebase's standards and leverage existing NotificationContext and hooks for maximum reuse.

### Files/Classes/Modules Searched:
- Searched for notification, package, and appointment logic in:
  - `src/renderer/contexts/NotificationContext.tsx`
  - `src/renderer/pages/Dashboard.tsx`
  - `src/renderer/pages/Appointments.tsx`
  - `src/renderer/hooks/useCustomerPackages.ts`
  - `src/renderer/types/packages.ts`
  - `src/renderer/types/appointments.ts`
  - `src/renderer/utils/dateUtils.ts`
- Confirmed no existing notification utility for this use case.

### Confirmation:
- No existing functionality could be reasonably extended for this notification logic. The new utility is justified and documented.

---

### Next Steps (per Big Pappa's new request):
- When marking an appointment as completed, if it is a package appointment, the dialog "Deseja criar uma venda para esta consulta" should NOT appear. This is because package appointments are already paid for at the time of package assignment, and a new sale should not be registered for each occurrence.
- Will update both `Dashboard.tsx` and `Appointments.tsx` to implement this logic.
