export interface Appointment {
  id: number;
  customer_id: number;
  pet_id: number;
  service_id: number;
  appointment_date: string;
  status: AppointmentStatus;
  notes: string | null;
  created_at: string | Date;
  updated_at: string | Date;
  customer?: Customer;
  pet?: Pet;
  service?: Service;
  is_package_appointment: boolean;
  source_customer_package_id?: number | null;
}

export type AppointmentStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';

export const AppointmentStatusLabels: Record<AppointmentStatus, string> = {
  scheduled: 'Agendado',
  in_progress: 'Em Andamento',
  completed: 'Concluído',
  cancelled: 'Cancelado',
  no_show: 'Faltou'
};

export const AppointmentStatusColors: Record<AppointmentStatus, string> = {
  scheduled: '#2196f3', // Blue
  in_progress: '#ff9800', // Orange
  completed: '#4caf50', // Green
  cancelled: '#f44336', // Red
  no_show: '#9e9e9e' // Grey
};

export interface Customer {
  id: number;
  name: string;
}

export interface Pet {
  id: number;
  name: string;
  type: string;
  customer_id: number;
}

export interface Service {
  id: number;
  name: string;
  duration_minutes: number;
  price: number;
}

export interface AppointmentFormData {
  customer_id: number;
  pet_id: number;
  service_id: number;
  appointment_date: string;
  status: AppointmentStatus;
  notes: string;
} 