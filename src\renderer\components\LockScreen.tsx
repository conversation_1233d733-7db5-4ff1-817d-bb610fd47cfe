import React, { useState, useEffect, useRef } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Fade from '@mui/material/Fade';

import { useTheme } from '@mui/material';
import { Lock as LockIcon, Backspace as BackspaceIcon } from '@mui/icons-material';

interface LockScreenProps {
  isLocked: boolean;
  onUnlock: (pin: string) => Promise<boolean>;
}

const LockScreen: React.FC<LockScreenProps> = ({ isLocked, onUnlock }) => {
  const theme = useTheme();
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [shake, setShake] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const attemptedRef = useRef(false);
  
  const primaryColor = theme.palette.primary;
  
  // Reset state when lock screen appears or disappears
  useEffect(() => {
    if (isLocked) {
      setPin('');
      setError('');
      setShake(false);
      setIsVerifying(false);
      attemptedRef.current = false;
    } else {
      setError('');
      setShake(false);
      setIsVerifying(false);
    }
  }, [isLocked]);
  
  useEffect(() => {
    if (shake) {
      const timer = setTimeout(() => {
        setShake(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [shake]);

  const handleDigitClick = (digit: number) => {
    if (pin.length < 4 && !isVerifying) {
      setPin(prev => {
        const newPin = prev + digit;
        if (newPin.length === 4) {
          handlePinComplete(newPin);
        }
        return newPin;
      });
      setError('');
    }
  };

  const handleBackspace = () => {
    if (!isVerifying) {
      setPin(prev => prev.slice(0, -1));
      setError('');
    }
  };

  const handlePinComplete = async (completedPin: string) => {
    if (completedPin.length !== 4) {
      setError('O PIN deve ter 4 dígitos');
      setShake(true);
      return;
    }
    
    setIsVerifying(true);
    setError('');
    
    const unlocked = await onUnlock(completedPin);
    
    if (!unlocked) {
      if (isLocked) {
        setError('PIN incorreto');
        setShake(true);
        setPin('');
      }
    }
    setIsVerifying(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isVerifying) {
      handlePinComplete(pin);
    }
  };

  // Generate digit buttons (0-9)
  const renderDigitButtons = () => {
    const digits = [1, 2, 3, 4, 5, 6, 7, 8, 9, null, 0, null];
    return digits.map((digit, index) => {
      if (digit === null) {
        return <Box key={index} sx={{ width: 72, height: 72 }} />;
      }
      
      return (
        <Button
          key={index}
          onClick={() => !isVerifying && handleDigitClick(digit)}
          disabled={isVerifying}
          sx={{
            width: 72,
            height: 72,
            borderRadius: '50%',
            fontSize: '1.5rem',
            color: 'text.primary',
            m: 0.5,
            border: '1px solid',
            borderColor: 'divider',
            transition: 'all 0.2s',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              transform: 'scale(1.05)',
            },
            '&.Mui-disabled': {
              opacity: 0.5,
            }
          }}
        >
          {digit}
        </Button>
      );
    });
  };

  return (
    <Dialog
      open={isLocked}
      fullScreen
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 400 }}
      PaperProps={{
        sx: {
          bgcolor: 'rgba(0, 0, 0, 0.3)',
          backdropFilter: 'blur(10px)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'transparent',
          boxShadow: 'none',
          overflow: 'hidden',
        },
      }}
    >
      <DialogContent sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 0, overflow: 'hidden' }}>
        <Paper
          elevation={8}
          sx={{
            width: 'auto',
            maxWidth: 370,
            p: 4,
            borderRadius: 4,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(5px)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '8px',
              background: `linear-gradient(to right, ${primaryColor.light}, ${primaryColor.main})`,
            }}
          />
          
          <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Box 
              sx={{ 
                width: 70, 
                height: 70, 
                borderRadius: '50%', 
                bgcolor: primaryColor.main,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 3,
                boxShadow: `0 4px 8px ${primaryColor.main}50`,
              }}
            >
              <LockIcon sx={{ fontSize: 32 }} />
            </Box>
            
            <Typography variant="h5" component="h2" fontWeight="500" mb={1}>
              Tela Bloqueada
            </Typography>
            
            <Typography variant="body2" color="text.secondary" mb={3} align="center">
              {isVerifying ? 'Verificando PIN...' : 'Digite seu PIN para acessar o sistema'}
            </Typography>
            
            {/* PIN dots display */}
            <Box 
              sx={{ 
                display: 'flex', 
                gap: 2, 
                mb: 3,
                animation: shake ? `${0.5}s shake` : 'none',
                '@keyframes shake': {
                  '0%, 100%': { transform: 'translateX(0)' },
                  '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-5px)' },
                  '20%, 40%, 60%, 80%': { transform: 'translateX(5px)' },
                },
              }}
            >
              {[0, 1, 2, 3].map((i) => (
                <Box
                  key={i}
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    border: '2px solid',
                    borderColor: error ? 'error.main' : primaryColor.main,
                    bgcolor: pin.length > i ? (error ? 'error.main' : primaryColor.main) : 'transparent',
                    transition: 'all 0.2s ease',
                  }}
                />
              ))}
            </Box>
            
            {error && (
              <Typography color="error" variant="body2" mb={2}>
                {error}
              </Typography>
            )}
            
            {/* Keypad */}
            <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', maxWidth: 280 }}>
              {renderDigitButtons()}
            </Box>
            
            {/* Backspace button */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <IconButton 
                onClick={() => !isVerifying && handleBackspace()}
                disabled={pin.length === 0 || isVerifying}
                sx={{ 
                  color: 'text.secondary',
                  '&.Mui-disabled': {
                    opacity: 0.3,
                  } 
                }}
              >
                <BackspaceIcon />
              </IconButton>
            </Box>
          </Box>
        </Paper>
      </DialogContent>
    </Dialog>
  );
};

export default LockScreen; 