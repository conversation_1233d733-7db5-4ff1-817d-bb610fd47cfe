export interface Pet {
  id: number;
  customer_id: number;
  name: string;
  type: string;
  breed: string | null;
  age: number | null;
  size: 'Pequeno' | 'Médio' | 'Grande' | null;
  gender: '<PERSON><PERSON>' | 'Fêmea' | null;
  fur_type: 'Curto' | '<PERSON><PERSON><PERSON>' | '<PERSON><PERSON>' | null;
  additional_notes: string | null;
  photo_url: string | null;
  is_hidden: boolean;
  status: 'active' | 'inactive';
  created_at: string | Date;
  updated_at: string | Date;
  customer?: Customer;
}

export interface Customer {
  id: number;
  name: string;
  email: string | null;
  phone: string | null;
}

export interface PetFormData {
  customer_id: number;
  name: string;
  type: string;
  breed: string;
  age: number | null;
  size: 'Pequeno' | 'Médio' | 'Grande' | '';
  gender: '<PERSON><PERSON>' | 'Fêmea' | '';
  fur_type: 'Curto' | 'Médio' | 'Longo' | '';
  additional_notes: string;
  photo_url: string | null;
}

export interface PetService {
  id: number;
  pet_id: number;
  service_name: string;
  service_date: string;
  notes: string | null;
}

export type PetType = 'Cachorro' | 'Gato' | 'Outro';

export const PetTypes: PetType[] = [
  'Cachorro', 'Gato', 'Outro'
]; 