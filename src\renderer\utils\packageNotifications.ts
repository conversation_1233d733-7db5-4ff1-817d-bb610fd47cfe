import { useCallback } from 'react';
import { useCustomerPackages } from '../hooks/useCustomerPackages';
import { useNotificationActions } from '../contexts/NotificationContext';
import { toBrazilianTimezone, formatBrazilianDate } from './dateUtils';
import { Appointment } from '../types/appointments';
import { CustomerPackage } from '../types/packages';

/**
 * Hook to notify about package appointment completion and next schedule.
 * Call this after marking a package appointment as completed.
 */
export function usePackageCompletionNotification() {
  const { getCustomerPackages } = useCustomerPackages();
  const { addNotification } = useNotificationActions();

  /**
   * Notify about next scheduled package appointment or package completion.
   * @param appointment The completed appointment object
   */
  const notifyPackageCompletion = useCallback(async (appointment: Appointment) => {
    if (!appointment.is_package_appointment || !appointment.source_customer_package_id) return;
    // Fetch the latest customer package info
    const customerPackages = await getCustomerPackages(appointment.customer_id);
    const customerPackage = customerPackages.find(
      (pkg: CustomerPackage) => pkg.id === appointment.source_customer_package_id
    );
    if (!customerPackage) return;
    const petName = appointment.pet?.name || 'pet';
    // If package is completed
    if (customerPackage.status === 'completed') {
      addNotification({
        title: 'Pacote concluído',
        message: `O pacote de serviços para ${petName} foi concluído. Todos os atendimentos foram realizados.`,
        type: 'success',
        relatedId: customerPackage.id,
      });
      return;
    }
    // If there is a next scheduled appointment
    if (customerPackage.next_scheduled_appointment_id && customerPackage.nextScheduledAppointment) {
      const nextAppt = customerPackage.nextScheduledAppointment;
      const apptDate = toBrazilianTimezone(nextAppt.appointment_date);
      const weekday = apptDate.toLocaleDateString('pt-BR', { weekday: 'long', timeZone: 'America/Sao_Paulo' });
      const date = formatBrazilianDate(apptDate, false);
      const time = apptDate.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit', hour12: false, timeZone: 'America/Sao_Paulo' });
      addNotification({
        title: 'Novo agendamento de pacote criado',
        message: `O próximo serviço do pacote para ${petName} foi agendado para ${weekday}, ${date} às ${time}.`,
        type: 'info',
        relatedId: customerPackage.next_scheduled_appointment_id,
      });
    } else if (customerPackage.next_scheduled_appointment_id) {
      // Fallback if nextScheduledAppointment is not populated
      addNotification({
        title: 'Novo agendamento de pacote criado',
        message: `O próximo serviço do pacote para ${petName} foi agendado.`,
        type: 'info',
        relatedId: customerPackage.next_scheduled_appointment_id,
      });
    }
  }, [getCustomerPackages, addNotification]);

  return { notifyPackageCompletion };
} 