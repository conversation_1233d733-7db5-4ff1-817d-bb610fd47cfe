import React from 'react';
import { Paper, Box, Typography, SvgIcon, useTheme, alpha, Divider } from '@mui/material';
import { SvgIconComponent } from '@mui/icons-material';

interface StatCardProps {
  icon: SvgIconComponent;
  title: string;
  value: string | number;
  subtitle?: string;
  color: string;
  iconColor?: string;
  onClick?: () => void;
  detailPath?: string;
  change?: number;
}

export const StatCard: React.FC<StatCardProps> = ({
  icon: Icon,
  title,
  value,
  subtitle,
  color,
  iconColor = 'white',
  onClick,
  detailPath,
  change
}) => {
  const theme = useTheme();
  const isClickable = onClick !== undefined || detailPath !== undefined;

  return (
    <Paper
      elevation={2}
      onClick={onClick}
      sx={{
        p: 3,
        position: 'relative',
        overflow: 'hidden',
        height: 150,
        width: '100%',
        cursor: isClickable ? 'pointer' : 'default',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': isClickable ? {
          transform: 'translateY(-4px)',
          boxShadow: '0 12px 20px -10px rgba(0,0,0,0.2)',
          '& .icon-box': {
            transform: 'scale(1.1)',
            boxShadow: `0 6px 10px -5px ${alpha(color, 0.6)}`,
          },
          '& .card-value': {
            color: theme.palette.primary.main,
          },
          '&::after': {
            transform: 'scale(10)',
            opacity: 0.1,
          }
        } : {},
        '&::after': {
          content: '""',
          position: 'absolute',
          top: '50%',
          right: '-10%',
          width: '80px',
          height: '80px',
          borderRadius: '50%',
          backgroundColor: color,
          opacity: 0.05,
          transition: 'transform 0.5s ease, opacity 0.5s ease',
          transform: 'scale(1)',
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', zIndex: 1 }}>
        {/* Top row with icon and title/value */}
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 1 }}>
          <Box
            className="icon-box"
            sx={{
              bgcolor: color,
              p: 2,
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              boxShadow: `0 4px 8px -4px ${alpha(color, 0.6)}`,
              zIndex: 1,
            }}
          >
            <SvgIcon
              component={Icon}
              sx={{
                fontSize: 32,
                color: iconColor,
              }}
            />
          </Box>
          
          <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
              {title}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
              <Typography 
                className="card-value"
                variant="h4" 
                component="div" 
                sx={{ 
                  flexGrow: 1,
                  transition: 'color 0.3s ease',
                }}
              >
                {value}
              </Typography>
              {change !== undefined && change !== 0 && (
                <Typography 
                  variant="body2"
                  sx={{ 
                    fontWeight: 'medium',
                    color: change > 0 ? 'success.main' : 'error.main',
                  }}
                >
                  {change > 0 ? '+' : ''}{change}%
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
        
        {/* Bottom row for description with separator */}
        {subtitle && (
          <>
            <Divider 
              sx={{ 
                mt: 'auto', 
                mb: 1, 
                opacity: 0.6,
                borderColor: alpha(theme.palette.divider, 0.5)
              }} 
            />
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{
                fontSize: '0.75rem',
                lineHeight: 1.2,
                width: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}
            >
              {subtitle}
            </Typography>
          </>
        )}
      </Box>
    </Paper>
  );
}; 