## Persistent PIN Storage & Setup Flow

**1. Goal:**
Make the screen lock P<PERSON> persistent across application restarts. The "Set PIN" dialog should only appear if no PIN has been previously configured. This system must be compatible with the existing "Change PIN" feature in the application settings.

**2. Core Implementation Details:**

*   **PIN Storage (Main Process - `electron-store` & `bcryptjs`):**
    *   A hash of the PIN (not the PIN itself) is stored persistently using `electron-store`.
    *   The key `screenLockPinHash` was added to the `StoreSchema` in `src/main/index.ts`.
    *   `bcryptjs` is used for hashing the PIN before storage and for verifying entered PINs against the stored hash. This handles salting automatically for better security.
    *   **IPC Handlers (in `src/main/ipc/auth.ts`):**
        *   `auth:setPinHash(pin: string)`: Hashes the provided PIN and saves it to `electron-store`.
        *   `auth:verifyPin(pin: string)`: Hashes the input PIN and compares it against the stored hash, returning a boolean match status.
        *   `auth:isPinConfigured()`: Checks `electron-store` for the presence of `screenLockPinHash` and returns a boolean.
        *   The `auth:changePassword` IPC handler was also reviewed/updated to ensure it correctly uses `bcryptjs` for password hashing.

*   **Authentication Context (`src/renderer/contexts/AuthContext.tsx`):**
    *   **State Management:**
        *   The plain `pin` property was removed from the `User` interface and from being stored in `localStorage`.
        *   A new boolean state `isPinConfigured` was introduced. It's updated by calling the `auth:isPinConfigured` IPC handler, primarily after login or when the user state changes.
        *   A new boolean state `isCheckingPinConfig` was added. This is set to `true` while the `auth:isPinConfigured` IPC call is in progress and `false` once it completes. This state is crucial for preventing UI flickers.
    *   **Logic Updates:**
        *   `needsPinSetup`: This computed value is now `!!user && !isPinConfigured && !isCheckingPinConfig`. The `SetupPin` dialog is only shown if a user is logged in, the PIN configuration check is complete, and no PIN is found to be configured.
        *   `setPin(pin: string)`: This function now calls the `auth:setPinHash` IPC handler. After a successful call, it re-triggers `checkPinConfigured`.
        *   `handleUnlock(pin: string)`: This function (used by the `LockScreen`) now calls the `auth:verifyPin` IPC handler.
        *   Inactivity timer logic now also considers `isPinConfigured` and `isCheckingPinConfig` to ensure it only activates when a PIN is set and stable.

*   **Setup PIN Dialog (`src/renderer/components/SetupPin.tsx`):**
    *   Its visibility is directly controlled by the `needsPinSetup` value from `AuthContext`.
    *   The `handleSubmit` function now calls the asynchronous `setPin` from `AuthContext` and handles the returned promise for success/error feedback.
    *   Loading states (`isLoading`) are managed to provide user feedback during the PIN setting process.

*   **Lock Screen Component (`src/renderer/components/LockScreen.tsx`):**
    *   The `onUnlock` prop (which is `AuthContext`'s `handleUnlock` function) is now asynchronous.
    *   An `isVerifying` state was added to disable PIN input and buttons while the `auth:verifyPin` IPC call is in progress.

*   **Settings Page (`src/renderer/pages/Settings.tsx` - Change PIN Functionality):**
    *   The "Current PIN" input field is conditionally rendered based on the `isPinConfigured` state from `AuthContext`.
    *   When submitting a PIN change:
        *   If `isPinConfigured` is true, the `currentPin` entered by the user is first verified by directly invoking the `auth:verifyPin` IPC handler.
        *   If verification passes (or if no PIN was previously configured), the new PIN is set using the `setPin` function from `AuthContext`.
    *   Loading states (`isPinChanging`) and error/success messages (using `Alert` and `addNotification`) are managed for a better user experience.

*   **Preload Script (`src/preload.ts`):**
    *   The `validInvokeChannels` array was updated to include:
        *   `auth:setPinHash`
        *   `auth:verifyPin`
        *   `auth:isPinConfigured`
        *   `auth:changePassword` (to align with the implemented handler).

**3. Key Outcomes & Improvements:**

*   **Persistence:** The screen lock PIN now persists across application restarts.
*   **Conditional Setup:** The dialog for setting up a PIN only appears if no PIN has been configured by the user.
*   **Security:** The PIN is stored as a hash using `bcryptjs`, not in plaintext.
*   **Settings Compatibility:** The "Change PIN" feature in the settings page correctly integrates with the new persistent PIN system, including verification of the current PIN.
*   **UX (Flicker Resolved):** The `SetupPin` dialog no longer flickers briefly after login if a PIN is already set, thanks to the `isCheckingPinConfig` state in `AuthContext`.
