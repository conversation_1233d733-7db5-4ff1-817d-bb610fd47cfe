import React, { useEffect, useState } from 'react';
import { useAppTheme } from '../contexts/ThemeContext';
import { alpha } from '@mui/material';

/**
 * A utility component that forces a theme refresh when needed
 * This is placed in the App component to ensure the theme changes are
 * applied immediately without needing to reload the app
 */
const ThemeRefresher: React.FC = () => {
  const { primaryColor, secondaryColor } = useAppTheme();
  const [lastPrimary, setLastPrimary] = useState(primaryColor);
  const [lastSecondary, setLastSecondary] = useState(secondaryColor);

  useEffect(() => {
    // Check if theme has changed
    if (primaryColor !== lastPrimary || secondaryColor !== lastSecondary) {
      // console.log('Theme changed, forcing refresh');
      setLastPrimary(primaryColor);
      setLastSecondary(secondaryColor);
      
      // Calculate hover colors for consistency
      const primaryHover = alpha(primaryColor, 0.9);
      const secondaryHover = alpha(secondaryColor, 0.9);
      
      // Force minimal DOM update to ensure theme is applied
      const root = document.documentElement;
      root.style.setProperty('--primary-color', primaryColor);
      root.style.setProperty('--primary-hover-color', primaryHover);
      root.style.setProperty('--secondary-color', secondaryColor);
      root.style.setProperty('--secondary-hover-color', secondaryHover);
      
      // Remove any existing theme style elements we've added previously
      const existingStyles = document.querySelectorAll('style[data-theme-refresher]');
      existingStyles.forEach(element => element.remove());
      
      // Force MUI button hover colors to update
      const style = document.createElement('style');
      style.setAttribute('data-theme-refresher', 'true');
      style.textContent = `
        .MuiButton-containedPrimary:hover {
          background-color: ${primaryHover} !important;
        }
        .MuiButton-containedSecondary:hover {
          background-color: ${secondaryHover} !important;
        }
        .MuiButton-outlinedPrimary:hover {
          background-color: ${alpha(primaryColor, 0.1)} !important;
          border-color: ${primaryColor} !important;
        }
        .MuiButton-outlinedSecondary:hover {
          background-color: ${alpha(secondaryColor, 0.1)} !important;
          border-color: ${secondaryColor} !important;
        }
        .MuiButton-textPrimary:hover {
          background-color: ${alpha(primaryColor, 0.1)} !important;
        }
        .MuiButton-textSecondary:hover {
          background-color: ${alpha(secondaryColor, 0.1)} !important;
        }
        
        /* Custom scrollbar styling */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
          background-color: ${alpha(primaryColor, 0.6)};
          border-radius: 4px;
          transition: background-color 0.3s ease;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background-color: ${primaryColor};
        }
        
        /* For Firefox */
        * {
          scrollbar-width: thin;
          scrollbar-color: ${alpha(primaryColor, 0.6)} transparent;
        }
      `;
      document.head.appendChild(style);
    }
    
    // Cleanup function to remove styles on unmount
    return () => {
      const existingStyles = document.querySelectorAll('style[data-theme-refresher]');
      existingStyles.forEach(element => element.remove());
    };
  }, [primaryColor, secondaryColor, lastPrimary, lastSecondary]);

  return null; // This component doesn't render anything
};

export default ThemeRefresher; 