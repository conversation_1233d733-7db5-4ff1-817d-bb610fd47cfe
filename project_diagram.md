# Pet Shop Management Application - Interaction Diagram

This document provides a high-level diagram illustrating the interactions between the main components and data flow within the Pet Shop Management application.

```mermaid
graph TD
    subgraph Frontend (Renderer Process - React UI)
        UI_Pages["Pages (Dashboard, Customers, Pets, Sales, etc.)"]
        UI_Components["Shared Components (Layout, Dialogs, Cards)"]
        UI_Hooks["Custom Hooks (useAppointments, useSales, etc.)"]
        UI_Contexts["Context Providers (Auth, Notifications, Theme, etc.)"]
        UI_Router["React Router (HashRouter)"]

        UI_Pages --> UI_Components
        UI_Pages --> UI_Hooks
        UI_Components --> UI_Hooks
        UI_Hooks --> IPC_Preload_Bridge
        UI_Contexts --> UI_Pages & UI_Components
        UI_Router --> UI_Pages
        UI_Router --> UI_Contexts
    end

    subgraph Backend (Main Process - Electron & Node.js)
        Main_Process["Main Process (index.ts)"]
        IPC_Handlers["IPC Handlers (auth.ts, database.ts)"]
        Service_Layer["Service Layer (CustomerService, SaleService, etc.)"]
        DB_Connection["Database Connection (connection.ts - TypeORM)"]
        Electron_Store["Electron Store (Persistent Config)"]
        Auto_Updater["Auto Updater"]
        Window_Management["Window Management (Main, Splash)"]
    end

    subgraph Database (SQLite)
        DB["SQLite Database (petshop.db)"]
        Entities["Data Models/Entities (User, Customer, Pet, Product, etc.)"]
        Migrations["Database Migrations"]
    end

    IPC_Preload_Bridge["IPC Bridge (preload.ts - contextBridge)"]

    %% Frontend to Backend Communication
    UI_Hooks -- "Invoke IPC Channel (e.g., 'customers:getAll')" --> IPC_Preload_Bridge
    IPC_Preload_Bridge -- "Securely Forwards Call" --> IPC_Handlers

    %% Backend Logic Flow
    Main_Process -- "Initializes/Manages" --> Window_Management
    Main_Process -- "Initializes" --> DB_Connection
    Main_Process -- "Sets up" --> IPC_Handlers
    Main_Process -- "Uses" --> Electron_Store
    Main_Process -- "Manages" --> Auto_Updater

    IPC_Handlers -- "Delegate to" --> Service_Layer
    IPC_Handlers -- "Directly Uses (for Packages)" --> DB_Connection
    Service_Layer -- "Interact with" --> DB_Connection
    DB_Connection -- "Uses/Manages" --> Entities
    DB_Connection -- "Manages" --> Migrations
    DB_Connection -- "Reads/Writes" --> DB

    %% Data Flow
    Entities -- "Define Schema for" --> DB

    %% Notifications Example Flow
    UI_Contexts -- "NotificationContext: Proactive Checks" --> UI_Hooks
    Service_Layer -- "Business Logic (e.g., SaleService creates PetService record)" --> DB_Connection

    %% Styling
    style Frontend fill:#D1E8FF,stroke:#333,stroke-width:2px
    style Backend fill:#E8D1FF,stroke:#333,stroke-width:2px
    style Database fill:#D1FFD1,stroke:#333,stroke-width:2px
    style IPC_Preload_Bridge fill:#FFFFD1,stroke:#333,stroke-width:2px

    classDef page fill:#C2DFFF,stroke:#000,stroke-width:1px;
    classDef component fill:#C2EACC,stroke:#000,stroke-width:1px;
    classDef hook fill:#FFE0C2,stroke:#000,stroke-width:1px;
    classDef context fill:#FFD6C2,stroke:#000,stroke-width:1px;
    classDef main fill:#E0C2FF,stroke:#000,stroke-width:1px;
    classDef service fill:#F0C2FF,stroke:#000,stroke-width:1px;
    classDef db fill:#C2FFC2,stroke:#000,stroke-width:1px;

    class UI_Pages page;
    class UI_Components component;
    class UI_Hooks hook;
    class UI_Contexts context;
    class Main_Process main;
    class IPC_Handlers main;
    class Service_Layer service;
    class DB_Connection db;
    class Entities db;
    class DB db;
````

### Diagram Explanation:

- __Frontend (Renderer Process)__:

  - __Pages__: Individual views like Dashboard, Customers page, etc.
  - __Shared Components__: Reusable UI elements like Layout, Dialogs.
  - __Custom Hooks__: Encapsulate data fetching and stateful logic for specific features (e.g., `useAppointments`).
  - __Context Providers__: Manage global state (Auth, Notifications, Theme).
  - __React Router__: Handles navigation within the frontend.
  - Frontend components and hooks use the __IPC Bridge__ to communicate with the backend.

- __IPC Bridge (`preload.ts`)__:

  - Securely exposes specific main process functionalities to the renderer process using `contextBridge`.

- __Backend (Main Process)__:

  - __Main Process (`index.ts`)__: Orchestrates app lifecycle, window creation, and initialization of backend services.
  - __IPC Handlers__: Receive requests from the frontend via the IPC bridge and delegate them.
  - __Service Layer__: Contains business logic for data manipulation (e.g., `CustomerService`, `SaleService`).
  - __Database Connection (`connection.ts`)__: Manages the TypeORM connection to SQLite.
  - __Electron Store__: Used for simple persistent configuration.
  - __Auto Updater__: Handles application updates.
  - __Window Management__: Creates and manages the splash and main application windows.

- __Database (SQLite)__:

  - __SQLite Database (`petshop.db`)__: The actual database file.
  - __Data Models/Entities__: TypeORM classes defining the table structures.
  - __Database Migrations__: Scripts for evolving the database schema.

### Key Interactions:

1. __UI Action to Data Fetch/Modification__:

   - A user interacts with a __Page__ or __Component__.
   - This triggers a function in a __Custom Hook__.
   - The Hook makes an `invoke` call through the __IPC Bridge__.
   - The __IPC Bridge__ securely forwards this to the appropriate __IPC Handler__ in the main process.
   - The __IPC Handler__ calls a method in the __Service Layer__.
   - The __Service Layer__ method uses the __Database Connection (TypeORM)__ to interact with the __SQLite Database__ (via __Entities__).
   - Data flows back through this chain to update the UI.

2. __Notifications__:

   - The `NotificationContext` in the frontend proactively uses __Custom Hooks__ to fetch data.
   - Based on this data (e.g., upcoming appointments), it generates notifications displayed in the UI.
   - Some backend actions in the __Service Layer__ (e.g., `SaleService` creating a `PetService` record) might indirectly trigger conditions that the `NotificationContext` later picks up.

3. __Authentication__:

   - Login page (UI) calls `login` function from `AuthContext`.
   - `AuthContext` uses a __Custom Hook__ (or directly invokes via IPC Bridge) to call `auth:login` __IPC Handler__.
   - `auth:login` handler verifies credentials against the `User` entity in the __Database__.

This diagram provides a simplified view. Many components within each subgraph also interact extensively with each other (e.g., Pages use many Components, Services might call other Services).

```