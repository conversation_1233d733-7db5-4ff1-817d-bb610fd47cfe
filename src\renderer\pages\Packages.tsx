import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import debounce from 'lodash/debounce';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Paper from '@mui/material/Paper';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import useTheme from '@mui/material/styles/useTheme';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  CardGiftcard as CardGiftcardIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { usePackages } from '../hooks/usePackages';
import { useServices } from '../hooks/useServices';
import { Package, PackageFormData } from '../types/packages';
import { PackageFormDialog } from '../components/Packages/PackageFormDialog';
import PetBackflipCaptcha from '../components/common/PetBackflipCaptcha';
import PackageCard from '../components/Packages/PackageCard';
// Import React Virtualized components
import {
  WindowScroller,
  AutoSizer,
  List,
  CellMeasurer,
  CellMeasurerCache
} from 'react-virtualized';

const Packages: React.FC = () => {
  const theme = useTheme();
  const { 
    packages, 
    loading, 
    error, 
    createPackage, 
    updatePackage, 
    deletePackage, 
    refetch 
  } = usePackages();
  const { services } = useServices();
  
  // Create a memoized map for faster service lookups
  const servicesMap = useMemo(() => {
    const map = new Map<number, { name: string; price: number }>();
    services.forEach(service => {
      map.set(service.id, { name: service.name, price: service.price });
    });
    return map;
  }, [services]); // Recompute only when services array changes
  
  const [searchTerm, setSearchTerm] = useState('');
  const filteredPackages = useMemo(() => {
    if (!packages) return [];
    const activePackages = packages.filter(pkg => pkg.is_active);
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      return activePackages.filter(pkg =>
        pkg.name.toLowerCase().includes(lowerSearchTerm) ||
        pkg.description.toLowerCase().includes(lowerSearchTerm)
      );
    }
    return activePackages;
  }, [packages, searchTerm]);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<Package | undefined>(undefined);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [packageToDelete, setPackageToDelete] = useState<number | null>(null);
  const [captchaSolved, setCaptchaSolved] = useState<boolean>(false);
  const [isDeletingPackage, setIsDeletingPackage] = useState<boolean>(false);
  const [deletedPackagesCount, setDeletedPackagesCount] = useState<number>(0);

  // Add CellMeasurerCache for variable height rows
  const cache = useMemo(() => new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 400, // Approximate height of a package card
    minHeight: 350,
  }), []);
  
  // Reset cache when filtered packages change
  useEffect(() => {
    if (filteredPackages && filteredPackages.length > 0) {
      cache.clearAll();
    }
  }, [filteredPackages, cache]);

  // Calculate number of columns based on screen width
  const getColumnCount = (width: number) => {
    if (width < 600) return 1; // Mobile view
    if (width < 960) return 2; // Tablet view
    return 3; // Desktop view - 3 columns
  };

  // Calculate row count based on items and columns
  const getRowCount = (itemCount: number, columnCount: number) => {
    return Math.ceil(itemCount / columnCount);
  };

  // Get item at index accounting for column layout
  const getItemAtIndex = (items: Package[], rowIndex: number, columnIndex: number, columnCount: number) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    return itemIndex < items.length ? items[itemIndex] : null;
  };

  // Create debounced search handler with 150ms delay
  const debouncedSetSearchTerm = useMemo(
    () => debounce((value: string) => {
      setSearchTerm(value);
    }, 150),
    []
  );

  // Update search handler to use debounced function
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSetSearchTerm(event.target.value);
  }, [debouncedSetSearchTerm]);

  // Clean up debounce on component unmount
  useEffect(() => {
    return () => {
      debouncedSetSearchTerm.cancel();
    };
  }, [debouncedSetSearchTerm]);

  const handleAddPackage = useCallback(() => {
    setSelectedPackage(undefined);
    setFormDialogOpen(true);
  }, []);

  const handleEditPackage = useCallback((packageItem: Package) => {
    setSelectedPackage(packageItem);
    setFormDialogOpen(true);
  }, []);

  const handleSavePackage = useCallback(async (packageData: PackageFormData) => {
    try {
      if (selectedPackage) {
        await updatePackage(selectedPackage.id, packageData);
      } else {
        await createPackage(packageData);
      }
      setFormDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Error saving package:', error);
    }
  }, [selectedPackage, updatePackage, createPackage, refetch]);

  const handleDeleteClick = useCallback((packageId: number) => {
    setPackageToDelete(packageId);
    setCaptchaSolved(false);
    setDeletedPackagesCount(0);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (packageToDelete && captchaSolved) {
      try {
        setIsDeletingPackage(true);
        const success = await deletePackage(packageToDelete);
        
        // Se a desativação foi bem-sucedida
        if (success) {
          // setDeletedPackagesCount não é mais relevante da mesma forma, mas pode ser usado para feedback se desejar.
          // setDeletedPackagesCount(1); // Indicando que 1 pacote foi desativado
        }
        
        setDeleteDialogOpen(false);
        setPackageToDelete(null);
        setCaptchaSolved(false);
        refetch();
      } catch (error) {
        console.error('Error deleting package:', error);
      } finally {
        setIsDeletingPackage(false);
      }
    }
  }, [packageToDelete, captchaSolved, deletePackage, refetch]);

  const handleCaptchaComplete = useCallback(() => {
    setCaptchaSolved(true);
  }, []);

  const handleCloseDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(false);
    setPackageToDelete(null);
    setCaptchaSolved(false);
  }, []);

  const handleCloseFormDialog = useCallback(() => {
    setFormDialogOpen(false);
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar pacotes: {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CardGiftcardIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 'bold', 
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 4,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 2,
              }
            }}
          >
            Gerenciamento de Pacotes
          </Typography>
        </Box>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
          onClick={handleAddPackage}
        >
          Criar Novo Pacote
        </Button>
      </Box>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Buscar pacotes por nome ou descrição"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Divider sx={{ mb: 3 }} />
      
      {filteredPackages.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Paper sx={{ p: 4, textAlign: 'center', maxWidth: 600 }}>
            <CardGiftcardIcon sx={{ fontSize: 60, color: 'text.secondary', opacity: 0.3, mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Nenhum pacote encontrado
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Crie pacotes para oferecer descontos em serviços recorrentes para seus clientes.
            </Typography>
            <Button 
              variant="contained" 
              startIcon={<AddIcon />}
              onClick={handleAddPackage}
            >
              Criar Primeiro Pacote
            </Button>
          </Paper>
        </Box>
      ) : (
        <Box sx={{ height: 'calc(100vh - 280px)', width: '100%' }}>
          <WindowScroller>
            {({ height, isScrolling, onChildScroll, scrollTop }) => (
              <AutoSizer disableHeight>
                {({ width }) => {
                  const columnCount = getColumnCount(width);
                  const rowCount = getRowCount(filteredPackages.length, columnCount);
                  
                  return (
                    <List
                      autoHeight
                      height={height || 500}
                      isScrolling={isScrolling}
                      onScroll={onChildScroll}
                      rowCount={rowCount}
                      rowHeight={cache.rowHeight}
                      scrollTop={scrollTop}
                      width={width}
                      overscanRowCount={3}
                      deferredMeasurementCache={cache}
                      style={{
                        outline: 'none',
                        paddingBottom: '20px',
                        overflowX: 'hidden'
                      }}
                      rowRenderer={({ index, key, style, parent }) => {
                        return (
                          <CellMeasurer
                            cache={cache}
                            columnIndex={0}
                            key={key}
                            parent={parent}
                            rowIndex={index}
                          >
                            {({ measure }) => (
                              <div 
                                style={{ 
                                  ...style, 
                                  padding: '8px',
                                  boxSizing: 'border-box',
                                  display: 'flex'
                                }}
                              >
                                {/* Create a row with potentially multiple columns */}
                                {Array.from({ length: columnCount }).map((_, colIndex) => {
                                  const packageItem = getItemAtIndex(filteredPackages, index, colIndex, columnCount);
                                  if (!packageItem) return <div key={colIndex} style={{ flex: 1 }} />;
                                  
                                  return (
                                    <div 
                                      key={colIndex} 
                                      style={{ 
                                        flex: 1, 
                                        padding: '0 8px',
                                        boxSizing: 'border-box'
                                      }}
                                    >
                                      <PackageCard
                                        packageItem={packageItem}
                                        servicesMap={servicesMap}
                                        onEdit={handleEditPackage}
                                        onDelete={handleDeleteClick}
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            )}
                          </CellMeasurer>
                        );
                      }}
                    />
                  );
                }}
              </AutoSizer>
            )}
          </WindowScroller>
        </Box>
      )}
      
      {/* Package Form Dialog */}
      <PackageFormDialog
        open={formDialogOpen}
        onClose={handleCloseFormDialog}
        onSave={handleSavePackage}
        packageItem={selectedPackage}
        title={selectedPackage ? 'Editar Pacote' : 'Criar Novo Pacote'}
        services={services}
      />
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'warning.main' }}>
          <WarningIcon color="warning" />
          Confirmar Exclusão do Pacote
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body1" fontWeight="medium">
              ATENÇÃO: Esta ação irá MARCAR O PACOTE COMO EXCLUÍDO.
            </Typography>
            <Box component="ul" sx={{ mt: 1, pl: 2 }}>
              <li>O pacote não estará mais disponível para novas compras/atribuições.</li>
              <li>Pacotes de clientes JÁ EXISTENTES que usam esta definição de pacote NÃO serão afetados e continuarão funcionando conforme seu status atual.</li>
            </Box>
          </Alert>
          
          <DialogContentText sx={{ mb: 2 }}>
            Tem certeza que deseja marcar este pacote como excluído?
            Esta ação o tornará inativo e o removerá das listagens principais.
          </DialogContentText>
          
          <PetBackflipCaptcha onComplete={handleCaptchaComplete} />
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={handleCloseDeleteDialog}
            disabled={isDeletingPackage}
          >
            Cancelar
          </Button>
          <Button 
            onClick={confirmDelete} 
            color="warning"
            disabled={!captchaSolved || isDeletingPackage}
            variant="contained"
            startIcon={isDeletingPackage ? <CircularProgress size={20} color="inherit" /> : <DeleteIcon />}
          >
            {isDeletingPackage ? 'Excluindo...' : 'Confirmar Exclusão'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Packages; 