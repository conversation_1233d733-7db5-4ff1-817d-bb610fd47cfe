Here's a detailed plan to implement the new automated, scheduled package system.

## Project Plan: Revamped Automated Package System

**Overall Goal:** Transform the package system from a voucher-based model to an automated, recurring scheduled service model, minimizing admin intervention while providing clear tracking and handling of exceptions (like no-shows).

---

### 1. Data Model Changes (Backend - `src/main/database/models/`)

We need to adjust existing models and potentially add new ones to support recurring schedules and link appointments to packages.

**1.1. `Package.ts` (Package Definition)**
*   **`id`**: `number` (Primary Key, Auto-increment)
*   **`name`**: `string`
*   **`description`**: `string` (nullable)
*   **`price`**: `number` (Decimal/Float)
*   **`service_id`**: `number` (Foreign Key to `Service.id`) - *Change: A package will now be tied to ONE specific service that repeats. If multiple services are needed in a "package deal", they should be separate packages or a different feature.*
*   **`total_occurrences`**: `number` (e.g., 4 for a monthly package of 4 baths)
*   **`frequency_type`**: `enum('weekly', 'monthly', 'custom_days')`
*   **`frequency_interval`**: `number` (e.g., 1 for `weekly` means every week; 2 for `weekly` means bi-weekly. For `monthly`, it's usually 1. For `custom_days`, this is the number of days, e.g., 10 for every 10 days).
*   **`is_active`**: `boolean` (Allows admin to temporarily disable a package type from being sold)
*   **`created_at`**: `Date`
*   **`updated_at`**: `Date`
*   *Remove `services` (JSON array), `service_quantity` (replaced by `total_occurrences`), `expiry_days` (expiry will be tied to the `CustomerPackage` or calculated).*

**1.2. `CustomerPackage.ts` (Customer's Subscribed Package)**
*   **`id`**: `number` (Primary Key, Auto-increment)
*   **`customer_id`**: `number` (Foreign Key to `Customer.id`)
*   **`package_id`**: `number` (Foreign Key to `Package.id`)
*   **`pet_id`**: `number` (Foreign Key to `Pet.id`) - *Crucial: which pet is this package for?*
*   **`status`**: `enum('pending_activation', 'active', 'on_hold_no_show', 'on_hold_customer_request', 'completed', 'cancelled')`
*   **`remaining_occurrences`**: `number`
*   **`activation_date`**: `Date` (When the package was first activated and first service scheduled)
*   **`next_scheduled_appointment_id`**: `number | null` (Foreign Key to `Appointment.id` for the upcoming package service)
*   **`current_cycle_first_scheduled_date`**: `Date | null` (The date the *very first* appointment for this customer's package was set to by the admin)
*   **`purchase_date`**: `Date`
*   **`expiry_date`**: `Date | null` (Optional: Overall expiry, e.g., "must use within 6 months")
*   **`notes`**: `string` (nullable, for admin remarks)
*   **`created_at`**: `Date`
*   **`updated_at`**: `Date`
*   *Remove `remaining_services` (replaced by `remaining_occurrences`), `is_active` (replaced by `status`). Keep `expiry_date` as an overall validity if needed.*

**1.3. `Appointment.ts` (Appointment Record)**
*   **`id`**: `number` (Primary Key, Auto-increment)
*   **`customer_id`**: `number`
*   **`pet_id`**: `number`
*   **`service_id`**: `number`
*   **`appointment_date`**: `datetime`
*   **`status`**: `enum('scheduled', 'confirmed', 'completed', 'cancelled_by_customer', 'cancelled_by_shop', 'no_show', 'rescheduled')`
*   **`notes`**: `string` (nullable)
*   **`is_package_appointment`**: `boolean` (Default `false`)
*   **`source_customer_package_id`**: `number | null` (Foreign Key to `CustomerPackage.id` if `is_package_appointment` is true)
*   **`created_at`**: `Date`
*   **`updated_at`**: `Date`

**1.4. `PackageUsageHistory.ts` (Log of Package Services Used/Missed)**
*   **`id`**: `number` (Primary Key, Auto-increment)
*   **`customer_package_id`**: `number` (Foreign Key to `CustomerPackage.id`)
*   **`appointment_id`**: `number` (Foreign Key to `Appointment.id`)
*   **`service_date`**: `Date` (Actual date service was rendered or scheduled for no-show)
*   **`status_at_usage`**: `enum('completed', 'no_show', 'system_cancelled')` (e.g. if package cancelled before appointment)
*   **`notes`**: `string` (nullable, e.g., "Admin marked no-show")
*   **`created_at`**: `Date`
*   *Remove `sale_id`, `sale_item_id`, `service_id`, `pet_id` (these are now derived via `Appointment` or `CustomerPackage`). Clarify purpose for logging.*

---

### 2. Backend Logic Changes (Main Process)

**2.1. New Service Layer for Packages (Consistency)**
*   Create `src/main/database/services/PackageService.ts`
    *   CRUD for `Package` definitions.
    *   `getAllActivePackages()`: Get packages admins can assign.
*   Create `src/main/database/services/CustomerPackageService.ts`
    *   `assignPackageToCustomer(customerId, packageId, petId, purchaseDetails)`: Creates `CustomerPackage` with `pending_activation` status.
    *   `activateCustomerPackage(customerPackageId, firstAppointmentDate, firstAppointmentTime)`:
        1.  Validates `customerPackage` status.
        2.  Sets `status` to `active`, `activation_date`, `remaining_occurrences` (from `Package.total_occurrences`), `current_cycle_first_scheduled_date`.
        3.  Calls `scheduleNextServiceForPackage(customerPackageId, firstAppointmentDate, firstAppointmentTime)`.
        4.  Returns success/failure.
    *   `scheduleNextServiceForPackage(customerPackageId, specificDate?: Date, specificTime?: string)`:
        1.  Fetches `CustomerPackage` and its `Package` definition.
        2.  If `customerPackage.remaining_occurrences <= 0` or `customerPackage.status !== 'active'`, mark `CustomerPackage` as `completed` and return.
        3.  Calculate `nextDate`:
            *   If `specificDate` is provided (first activation or manual reschedule), use it.
            *   Else, get the `appointment_date` of the `customerPackage.next_scheduled_appointment_id` (which should be the one just completed/missed).
            *   Add `Package.frequency_interval` based on `Package.frequency_type` to this date. Ensure business day logic if needed (e.g., skip weekends, or configurable).
        4.  Create new `Appointment`:
            *   `customer_id`, `pet_id` from `CustomerPackage`.
            *   `service_id` from `Package`.
            *   `appointment_date` = calculated `nextDate` + (time from previous appointment or a default time).
            *   `status` = `'scheduled'`.
            *   `is_package_appointment` = `true`.
            *   `source_customer_package_id` = `customerPackageId`.
        5.  Update `CustomerPackage.next_scheduled_appointment_id` with the new `Appointment.id`.
        6.  Log in `PackageUsageHistory` that an appointment was scheduled.
    *   `handlePackageAppointmentCompletion(appointmentId)`:
        1.  Fetches `Appointment` and related `CustomerPackage`.
        2.  If not a package appointment or already processed, ignore.
        3.  Decrement `CustomerPackage.remaining_occurrences`.
        4.  Log in `PackageUsageHistory` (`status_at_usage: 'completed'`).
        5.  Calls `scheduleNextServiceForPackage(customerPackageId)`.
    *   `handlePackageAppointmentNoShow(appointmentId)`:
        1.  Fetches `Appointment` and related `CustomerPackage`.
        2.  Policy: Does no-show consume an occurrence? (Assuming YES based on your description).
        3.  Decrement `CustomerPackage.remaining_occurrences`.
        4.  Log in `PackageUsageHistory` (`status_at_usage: 'no_show'`).
        5.  Set `CustomerPackage.status` to `on_hold_no_show`. *This is key for admin awareness.*
        6.  The `scheduleNextServiceForPackage` will NOT be called automatically. Admin must intervene.
    *   `resolveNoShowAndReschedule(customerPackageId, newDate, newTime, consumeOccurrenceIfPreviouslyNot)`:
        1.  Admin action to handle an `on_hold_no_show` package.
        2.  Optionally mark the missed one as not consuming an occurrence (if policy allows grace).
        3.  Set `CustomerPackage.status` back to `active`.
        4.  Calls `scheduleNextServiceForPackage(customerPackageId, newDate, newTime)`.
    *   `getCustomerPackages(customerId)`: List packages for a customer.
    *   `getUpcomingPackageAppointments()`: For dashboard/notifications.
    *   `getPackagesOnHold()`: For admin dashboard/attention list.

**2.2. Modify `AppointmentService.ts`**
*   When an `Appointment` status changes to `completed` or `no_show`:
    *   Check `appointment.is_package_appointment`.
    *   If true, call the respective handler in `CustomerPackageService` (e.g., `customerPackageService.handlePackageAppointmentCompletion(appointment.id)`).
*   When creating/updating appointments, ensure `is_package_appointment` and `source_customer_package_id` are handled correctly.

**2.3. IPC Handlers (`src/main/ipc/database.ts`)**
*   Create new IPC channels for all new `PackageService` and `CustomerPackageService` methods.
    *   `packages:create`, `packages:getAllActive`
    *   `customerPackages:assign`, `customerPackages:activate`, `customerPackages:getForCustomer`, `customerPackages:getOnHold`, `customerPackages:resolveNoShow`
*   Update `preload.ts` with these new valid channels.

**2.4. Modify `SaleService.ts` (if applicable)**
*   Selling a package now creates a `CustomerPackage` record, not directly sale items for future services. The `price` from `Package` definition is used for the initial transaction.
*   The actual services (appointments) under the package do not generate individual sales; they are "covered" by the initial package purchase.

---

### 3. Frontend UI/UX Changes (Renderer Process)

**3.1. New Custom Hooks (`src/renderer/hooks/`)**
*   `usePackages.ts`: For managing `Package` definitions (fetching, creating).
*   `useCustomerPackages.ts`: For managing `CustomerPackage` instances (fetching for customer, assigning, activating, resolving no-shows).

**3.2. Settings Page (`src/renderer/pages/Settings.tsx` or new `PackagesAdmin.tsx`)**
*   UI for Admin to Create/Edit `Package` definitions:
    *   Form fields: Name, Description, Price, Service (dropdown from existing services), Total Occurrences, Frequency Type (dropdown: Weekly, Monthly, Custom Days), Frequency Interval (number input).
    *   List of existing packages with edit/deactivate options.

**3.3. Customer Detail Page (`src/renderer/pages/Customers.tsx` -> select customer)**
*   "Assign Package" section/button:
    *   Dialog to select an active `Package` definition.
    *   Select `Pet` for this package.
    *   Confirm purchase (creates `CustomerPackage` with `pending_activation`).
*   List of "Customer's Packages":
    *   Shows all `CustomerPackage`s for this customer (active, pending, on_hold, completed).
    *   For `pending_activation` packages: "Activate Package" button.
        *   Dialog prompts for **First Service Date & Time**. (Calls `customerPackages:activate` IPC).
    *   For `on_hold_no_show` packages: "Resolve No-Show" button.
        *   Dialog prompts for **New Service Date & Time**. Maybe an option: "Mark original no-show as excused (doesn't consume occurrence)". (Calls `customerPackages:resolveNoShow` IPC).
    *   Displays `remaining_occurrences`, `status`, `next_scheduled_appointment_id` (link to appointment).
    *   View `PackageUsageHistory` for a specific customer package.

**3.4. Appointments Page (`src/renderer/pages/Appointments.tsx`) & Dashboard (`src/renderer/pages/Dashboard.tsx`)**
*   Visually distinguish package appointments (e.g., special icon, color coding).
*   When marking an appointment as `completed` or `no_show`, the backend logic handles package updates automatically.
*   **New Dashboard Section/Widget: "Packages Requiring Attention"**
    *   Lists `CustomerPackage`s with status `on_hold_no_show`.
    *   Direct links to "Resolve No-Show" for each.
    *   This is critical for admin awareness beyond just a notification.
*   Upcoming package appointments should naturally appear in existing appointment lists.

**3.5. `PackageUsageHistory` Display**
*   Accessible from `CustomerPackage` details.
*   A simple table/list showing: Service Date, Status (Completed, No-Show), Appointment Link, Notes.

**3.6. Notification System (`src/renderer/contexts/NotificationContext.tsx`)**
*   While direct dashboard visibility for no-shows is key, notifications can still be useful:
    *   "Package for [Customer Name] - [Pet Name] is now on hold due to a no-show."
    *   "Successfully scheduled next service for [Customer Name]'s package."
    *   "Package for [Customer Name] - [Pet Name] has been completed."

---

### 4. Handling No-Shows (Crucial User Requirement)

*   **Detection:** When an admin marks a package-linked `Appointment` as `no-show`.
*   **Backend Action:**
    1.  `Appointment.status` -> `no_show`.
    2.  `CustomerPackageService.handlePackageAppointmentNoShow()` is triggered.
    3.  `CustomerPackage.remaining_occurrences` is decremented (policy).
    4.  `CustomerPackage.status` -> `on_hold_no_show`.
    5.  `PackageUsageHistory` logs the no-show.
    6.  **No automatic rescheduling.**
*   **Admin Awareness (Frontend):**
    1.  Dashboard widget: "Packages Requiring Attention" lists all `CustomerPackage`s with status `on_hold_no_show`.
    2.  Customer Detail Page: The specific package is marked "On Hold - No Show".
    3.  Notification (secondary).
*   **Admin Resolution (Frontend):**
    1.  Admin clicks "Resolve No-Show" (from dashboard or customer page).
    2.  Dialog appears:
        *   "Package for [Customer] - [Pet] - [Service] was a no-show on [Date]."
        *   "Schedule next service for: [Date Picker] [Time Picker]"
        *   (Optional checkbox, if policy allows): "Do not count the missed appointment towards package occurrences."
    3.  Admin submits. IPC call to `customerPackages:resolveNoShow`.
*   **Backend Resolution:**
    1.  `CustomerPackageService.resolveNoShowAndReschedule()`:
        *   If "excused" was checked, increment `remaining_occurrences` if it was decremented.
        *   Set `CustomerPackage.status` to `active`.
        *   Calls `scheduleNextServiceForPackage()` with the new date/time provided by admin.

---

### 5. Logging (Requirement 6)

*   **`PackageUsageHistory`**: This is the primary log for services rendered/missed under a package. Each entry links to the `CustomerPackage` and the specific `Appointment`.
*   **`Appointment` notes**: Can store details about a specific instance.
*   **`CustomerPackage` notes**: General notes about the customer's overall package.
*   Consider adding a more generic `AuditLog` or `ActivityLog` entity if not already present, for tracking broader system actions like "Admin X activated package Y for customer Z." (This might be overkill if `PackageUsageHistory` and entity `updated_at` fields are sufficient). The existing `PetService` table for non-package services is a good model. `PackageUsageHistory` aims to be that for packages.

---

### 6. Database Migrations (`src/main/database/migrations/`)

*   Create new migration scripts for:
    1.  Altering `Package.ts` (add new fields, remove old ones).
    2.  Altering `CustomerPackage.ts` (add new fields, change enums, remove old ones).
    3.  Altering `Appointment.ts` (add `is_package_appointment`, `source_customer_package_id`).
    4.  Altering `PackageUsageHistory.ts` (adjust fields, relationships).
*   **Data Migration for Existing Packages:** This is the trickiest part.
    *   How do existing `Package`, `CustomerPackage`, `PackageUsageHistory` records map to the new structure?
    *   Option 1 (Simpler, data loss/archival): Archive old package data and start fresh with the new system. Admins manually re-enter active "voucher" packages into the new system if they want them to be scheduled.
    *   Option 2 (Complex): Write a complex migration script to convert existing `CustomerPackage` vouchers into `pending_activation` new `CustomerPackage`s. This would require mapping old `Package.services` JSON to a single `service_id`, estimating `total_occurrences`, etc. This is high effort and error-prone.
    *   **Recommendation:** Discuss with the client. Option 1 or a hybrid (manually transition only a few key active customer packages) is likely more pragmatic for a small pet shop.

---

### 7. Phased Rollout / Development Strategy

1.  **Phase 1: Backend Core & Data Models**
    *   Implement all data model changes.
    *   Write TypeORM migrations (initially for schema, data migration later).
    *   Develop `PackageService` and `CustomerPackageService` with core logic (CRUD, activation, scheduling next, completion, no-show handling).
    *   Write backend unit/integration tests for these services.
    *   Setup basic IPC handlers.
2.  **Phase 2: Admin UI for Package Definition**
    *   Frontend UI for admins to create/edit `Package` definitions.
3.  **Phase 3: Customer Package Assignment & Activation UI**
    *   Frontend UI for assigning packages to customers.
    *   Frontend UI for activating packages (setting first appointment).
4.  **Phase 4: Integration with Appointments & Dashboard**
    *   Modify Appointments page/Dashboard to display package appointments.
    *   Implement the "Packages Requiring Attention" widget.
    *   Frontend UI for resolving no-shows.
5.  **Phase 5: Logging & History Display**
    *   Frontend UI to display `PackageUsageHistory`.
6.  **Phase 6: Testing & Refinement**
    *   Thorough end-to-end testing.
    *   UAT with the client.
    *   Address `electron-store` usage for `adminUserCreated` if it conflicts or needs adjustment for package feature flags during rollout.
7.  **Phase 7: Data Migration (if chosen over archival)**
    *   Develop and test data migration scripts for existing packages.

---

This plan provides a comprehensive roadmap. The key is the tight integration between `Appointment` status changes and `CustomerPackageService` logic. The "Packages Requiring Attention" dashboard section will be vital for the admin to manage exceptions without relying solely on transient notifications.