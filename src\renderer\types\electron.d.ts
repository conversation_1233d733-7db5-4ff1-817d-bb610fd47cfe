interface ElectronAPI {
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  removeListener: (channel: string, callback: (...args: any[]) => void) => void;
}

interface ElectronWindow {
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  removeListener: (channel: string, callback: (...args: any[]) => void) => void;
  removeAllListeners: (channel: string) => void;
  getDatabaseStatus: () => Promise<{ isReady: boolean }>;
  onDatabaseReady: (callback: () => void) => void;
  sendMainWindowReady: () => void;
  store: {
    get: (key: string) => any;
    set: (key: string, val: any) => void;
  };
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    electron: ElectronWindow;
  }
}

export {}; 