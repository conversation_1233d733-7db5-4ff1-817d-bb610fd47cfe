import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Grid
} from '@mui/material';
import { DatePicker, TimePicker } from '@mui/x-date-pickers';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { ptBR } from 'date-fns/locale/pt-BR';
import { format } from 'date-fns';
import { CustomerPackage } from '../../types/packages';

interface PaymentCompletionDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (paidAt: Date) => Promise<void>;
  customerPackage: CustomerPackage | null;
  loading?: boolean;
}

export const PaymentCompletionDialog: React.FC<PaymentCompletionDialogProps> = ({
  open,
  onClose,
  onConfirm,
  customerPackage,
  loading = false
}) => {
  const [paidDate, setPaidDate] = useState<Date | null>(new Date());
  const [paidTime, setPaidTime] = useState<Date | null>(new Date());
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (open) {
      const now = new Date();
      setPaidDate(now);
      setPaidTime(now);
      setError(null);
    }
  }, [open]);

  const handleConfirm = async () => {
    if (!paidDate || !paidTime) {
      setError('Por favor, selecione a data e hora do pagamento.');
      return;
    }

    // Combine date and time
    const combinedDateTime = new Date(
      paidDate.getFullYear(),
      paidDate.getMonth(),
      paidDate.getDate(),
      paidTime.getHours(),
      paidTime.getMinutes(),
      paidTime.getSeconds()
    );

    if (combinedDateTime > new Date()) {
      setError('A data do pagamento não pode ser no futuro.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      await onConfirm(combinedDateTime);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao completar pagamento');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={isSubmitting}
      >
        <DialogTitle>
          Completar Pagamento
        </DialogTitle>

        <DialogContent>
          {customerPackage && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Pacote:</strong> {customerPackage.package?.name || 'N/A'}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Valor:</strong> R$ {customerPackage.price_paid?.toFixed(2) || '0,00'}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Data de Compra:</strong> {' '}
                {format(new Date(customerPackage.purchase_date), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
              </Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <DatePicker
                  label="Data do Pagamento"
                  value={paidDate}
                  onChange={(newValue) => setPaidDate(newValue)}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      error: !!error && !paidDate,
                      helperText: !paidDate ? 'Campo obrigatório' : ''
                    }
                  }}
                  maxDate={new Date()}
                />
              </Grid>
              <Grid item xs={6}>
                <TimePicker
                  label="Hora do Pagamento"
                  value={paidTime}
                  onChange={(newValue) => setPaidTime(newValue)}
                  format="HH:mm"
                  ampm={false}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      error: !!error && !paidTime,
                      helperText: !paidTime ? 'Campo obrigatório' : ''
                    }
                  }}
                />
              </Grid>
            </Grid>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Esta data será usada para calcular as estatísticas de receita nos relatórios.
          </Typography>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            variant="contained"
            disabled={isSubmitting || !paidDate || !paidTime}
            startIcon={isSubmitting ? <CircularProgress size={16} /> : null}
          >
            {isSubmitting ? 'Processando...' : 'Confirmar Pagamento'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};
