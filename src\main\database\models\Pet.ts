import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import type { Customer } from './Customer';

@Entity('pets')
export class Pet {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @Column()
  type!: string;

  @Column({ nullable: true })
  breed!: string;

  @Column({ nullable: true })
  age!: number;

  @Column({ nullable: true })
  size!: string;

  @Column({ nullable: true })
  gender!: string;

  @Column({ nullable: true })
  fur_type!: string;

  @Column({ nullable: true })
  additional_notes!: string;

  @Column({ nullable: true, type: 'text' })
  photo_url!: string;

  @Column({ default: false })
  is_hidden!: boolean;

  @Column({ default: 'active' })
  status!: 'active' | 'inactive';

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;

  @ManyToOne('Customer', 'pets')
  customer!: Customer;
} 