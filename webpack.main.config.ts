import type { Configuration } from 'webpack';
import { type Compilation, WebpackError } from 'webpack'; // Import WebpackError and ensure Compilation is a named import
import { rules } from './webpack.rules';
import path from 'path';
import CircularDependencyPlugin from 'circular-dependency-plugin';
import TerserPlugin from 'terser-webpack-plugin';

// Determine if you're in a special debug-production mode
// You might set an environment variable for this, e.g., DEBUG_PROD_BUILD=true
const isDebugProdBuild = process.env.DEBUG_PROD_BUILD === 'true';

export const mainConfig: Configuration = {
  mode: isDebugProdBuild ? 'development' : 'production',
  entry: {
    index: './src/main/index.ts',
    preload: './src/preload.ts',
    splashPreload: './src/preload/splash-preload.ts',
  },
  module: {
    rules
  },
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css', '.json'],
  },
  output: {
    path: path.resolve(__dirname, '.webpack/main'),
    filename: '[name].js',
    chunkFilename: '[name].chunk.js'
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          keep_classnames: true,
          keep_fnames: true,
        },
      }),
    ],
  },
  devtool: isDebugProdBuild ? 'eval-source-map' : 'source-map',
  plugins: [
    new CircularDependencyPlugin({
      exclude: /node_modules/,
      failOnError: false,
      allowAsyncCycles: false,
      cwd: process.cwd(),
      onDetected({
        paths,
        compilation,
      }: { paths: string[]; compilation: Compilation; module: any /* Adjust if @types/circular-dependency-plugin provides a more specific type */ }) {
        const cyclePath = paths.join(' -> ');
        console.warn(`\n[Circular Dependency] Detected: ${cyclePath}\n`); // Log to console for visibility

        // Use WebpackError for adding to compilation warnings/errors
        const webpackWarning = new WebpackError(`Circular dependency: ${cyclePath}`);
        webpackWarning.name = 'CircularDependencyWarning'; // Optional: give it a specific name
        compilation.warnings.push(webpackWarning);
      }
    })
  ]
};