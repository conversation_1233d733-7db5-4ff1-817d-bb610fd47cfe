CREATE TABLE IF NOT EXISTS "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "username" varchar NOT NULL, "password_hash" varchar NOT NULL, "role" varchar NOT NULL, "created_at" datetime NOT NULL DEFAULT (datetime('now')), "last_login" datetime, CONSTRAINT "UQ_fe0bb3f6520ee0469504521e710" UNIQUE ("username"));
CREATE TABLE sqlite_sequence(name,seq);
CREATE TABLE IF NOT EXISTS "customers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "email" varchar, "phone" varchar, "address" varchar, "additional_notes" varchar, "status" varchar NOT NULL DEFAULT ('active'), "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')));
CREATE TABLE IF NOT EXISTS "products" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "category" varchar NOT NULL, "description" varchar, "price" decimal(10,2) NOT NULL, "cost_price" decimal(10,2) NOT NULL DEFAULT (0), "stock_quantity" integer NOT NULL, "min_stock_level" integer NOT NULL DEFAULT (10), "is_deleted" boolean NOT NULL DEFAULT (0), "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')));
CREATE TABLE IF NOT EXISTS "services" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "description" varchar, "price" decimal(10,2) NOT NULL, "duration_minutes" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT (1), "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')));
CREATE TABLE IF NOT EXISTS "pets" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "type" varchar NOT NULL, "breed" varchar, "age" integer, "size" varchar, "gender" varchar, "fur_type" varchar, "additional_notes" varchar, "photo_url" text, "is_hidden" boolean NOT NULL DEFAULT (0), "status" varchar NOT NULL DEFAULT ('active'), "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')), "customerId" integer, CONSTRAINT "FK_b0b20a01418c42314f6d9fe9963" FOREIGN KEY ("customerId") REFERENCES "customers" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "sale_items" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "quantity" integer NOT NULL, "price_per_unit" decimal(10,2) NOT NULL, "petId" integer, "customer_package_id" integer, "is_package_service" boolean NOT NULL DEFAULT (0), "saleId" integer, "productId" integer, "serviceId" integer, CONSTRAINT "FK_c642be08de5235317d4cf3deb40" FOREIGN KEY ("saleId") REFERENCES "sales" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_d675aea38a16313e844662c48f8" FOREIGN KEY ("productId") REFERENCES "products" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_67073c462461ef932634df985d5" FOREIGN KEY ("serviceId") REFERENCES "services" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "sales" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "total_amount" decimal(10,2) NOT NULL, "payment_method" varchar NOT NULL, "status" varchar NOT NULL DEFAULT ('paid'), "sale_date" datetime NOT NULL DEFAULT (datetime('now')), "customerId" integer, CONSTRAINT "FK_3a92cf6add00043cef9833db1cd" FOREIGN KEY ("customerId") REFERENCES "customers" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "packages" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "description" text, "price" float NOT NULL, "service_id" integer NOT NULL, "total_occurrences" integer NOT NULL, "frequency_type" varchar NOT NULL, "frequency_interval" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT (1), "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')), CONSTRAINT "FK_066c8f04fb6d7a99cc0b1d31ed1" FOREIGN KEY ("service_id") REFERENCES "services" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "customer_packages" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "customer_id" integer NOT NULL, "package_id" integer NOT NULL, "pet_id" integer NOT NULL, "status" varchar NOT NULL, "remaining_occurrences" integer NOT NULL, "activation_date" datetime, "next_scheduled_appointment_id" integer, "current_cycle_first_scheduled_date" datetime, "purchase_date" datetime NOT NULL, "expiry_date" datetime, "notes" text, "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')), CONSTRAINT "FK_dda77acd7197888dde74a84d2b5" FOREIGN KEY ("customer_id") REFERENCES "customers" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_da33a86b83da2c53ada35494a09" FOREIGN KEY ("package_id") REFERENCES "packages" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_2bb2f09015c2f36c574fea64e00" FOREIGN KEY ("pet_id") REFERENCES "pets" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_3746fe8d3eaa69ed237c2f5f6d2" FOREIGN KEY ("next_scheduled_appointment_id") REFERENCES "appointments" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "appointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_date" datetime NOT NULL, "status" varchar NOT NULL DEFAULT ('scheduled'), "notes" varchar, "is_package_appointment" boolean NOT NULL DEFAULT (0), "source_customer_package_id" integer, "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')), "customerId" integer, "petId" integer, "serviceId" integer, CONSTRAINT "FK_60dbcf20669c096d319e20fca8a" FOREIGN KEY ("customerId") REFERENCES "customers" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_96e11d40768b1eea9dddc38a124" FOREIGN KEY ("petId") REFERENCES "pets" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_f77953c373efb8ab146d98e90c3" FOREIGN KEY ("serviceId") REFERENCES "services" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_b8549cb441e8f439af8aaa50cd2" FOREIGN KEY ("source_customer_package_id") REFERENCES "customer_packages" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "pet_services" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "pet_id" integer NOT NULL, "service_name" varchar NOT NULL, "service_date" datetime NOT NULL, "notes" varchar, "created_at" datetime NOT NULL DEFAULT (datetime('now')), "updated_at" datetime NOT NULL DEFAULT (datetime('now')), CONSTRAINT "FK_04c7c7780193ed306358f22f4f7" FOREIGN KEY ("pet_id") REFERENCES "pets" ("id") ON DELETE CASCADE ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "package_usage_history" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "customer_package_id" integer NOT NULL, "appointment_id" integer NOT NULL, "service_date" datetime NOT NULL, "status_at_usage" varchar NOT NULL, "notes" text, "event_type" varchar(50), "created_at" datetime NOT NULL DEFAULT (datetime('now')), CONSTRAINT "FK_10b53345d00197396e6db32d22e" FOREIGN KEY ("customer_package_id") REFERENCES "customer_packages" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_fa3bc8922073702525e8cf52c59" FOREIGN KEY ("appointment_id") REFERENCES "appointments" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION);
CREATE TABLE IF NOT EXISTS "migrations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "timestamp" bigint NOT NULL, "name" varchar NOT NULL);
