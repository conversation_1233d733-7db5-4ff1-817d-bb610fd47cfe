import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Grid from '@mui/material/Grid';
import {
  LocalizationProvider,
  DatePicker,
  TimePicker,
} from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { ptBR } from 'date-fns/locale/pt-BR';
import { format, setHours, setMinutes, setSeconds, setMilliseconds } from 'date-fns';
import { CustomerPackage } from '../../types/packages';
import { EventRepeat as EventRepeatIcon, Cancel as CancelIcon, AssignmentTurnedIn as AssignmentTurnedInIcon } from '@mui/icons-material';

interface ResolveNoShowDialogProps {
  open: boolean;
  onClose: () => void;
  onResolved: () => void;
  customerPackage: CustomerPackage | null;
  customerName: string;
}

export const ResolveNoShowDialog: React.FC<ResolveNoShowDialogProps> = ({
  open,
  onClose,
  onResolved,
  customerPackage,
  customerName,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [newDate, setNewDate] = useState<Date | null>(null);
  const [newTime, setNewTime] = useState<Date | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      // Reset state when dialog opens
      setActiveTab(0);
      setNewDate(new Date()); // Default to today
      // Default time to a common business hour, e.g., 9 AM, or make it null
      const defaultTime = setHours(setMinutes(new Date(), 0), 9);
      setNewTime(defaultTime);
      setAdminNotes('');
      setLoading(false);
      setError(null);
    }
  }, [open]);

  if (!customerPackage) return null;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setError(null); // Clear error when switching tabs
  };

  const combineDateTime = (date: Date | null, time: Date | null): Date | null => {
    if (!date || !time) return null;
    return setMilliseconds(setSeconds(setMinutes(setHours(new Date(date), time.getHours()), time.getMinutes()), 0), 0);
  };
  
  const handleReschedule = async () => {
    if (!newDate || !newTime) {
      setError('Por favor, selecione a nova data e hora para o agendamento.');
      return;
    }
    const combinedDateTime = combineDateTime(newDate, newTime);
    if (!combinedDateTime) {
        setError('Data ou hora inválida.');
        return;
    }
    if (combinedDateTime < new Date()) {
      setError('Não é possível agendar para uma data ou hora no passado.');
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const result = await window.electronAPI.invoke(
        'customerPackages:resolveNoShowReschedule',
        customerPackage.id,
        combinedDateTime.toISOString(),
        adminNotes
      );
      if (result.success) {
        onResolved();
        onClose();
      } else {
        setError(result.error || 'Erro ao reagendar o pacote.');
      }
    } catch (err: any) {
      setError(err.message || 'Erro ao comunicar com o servidor.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelPackage = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await window.electronAPI.invoke(
        'customerPackages:resolveNoShowCancel',
        customerPackage.id,
        adminNotes
      );
      if (result.success) {
        onResolved();
        onClose();
      } else {
        setError(result.error || 'Erro ao cancelar o pacote.');
      }
    } catch (err: any) {
      setError(err.message || 'Erro ao comunicar com o servidor.');
    } finally {
      setLoading(false);
    }
  };

  const packageName = customerPackage.package?.name || 'Pacote desconhecido';
  const dialogTitle = `Resolver Pendência: ${packageName} (#${customerPackage.id})`;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AssignmentTurnedInIcon sx={{ mr: 1, color: 'primary.main' }} />
            {dialogTitle}
          </Box>
          <Typography variant="body2" color="text.secondary">
            Cliente: {customerName}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: '0px !important' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="Opções de resolução">
              <Tab label="Reagendar Próximo Serviço" icon={<EventRepeatIcon />} iconPosition="start" />
              <Tab label="Cancelar Pacote" icon={<CancelIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          {activeTab === 0 && ( // Reagendar
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Selecione a nova data e hora para o próximo serviço do pacote:
              </Typography>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="Nova Data"
                    value={newDate}
                    onChange={setNewDate}
                    slots={{ textField: (params) => <TextField {...params} fullWidth /> }}
                    minDate={new Date()}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TimePicker
                    label="Nova Hora"
                    value={newTime}
                    onChange={setNewTime}
                    slots={{ textField: (params) => <TextField {...params} fullWidth /> }}
                  />
                </Grid>
              </Grid>
              <TextField
                label="Observações (opcional)"
                multiline
                rows={3}
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                fullWidth
                sx={{ mt: 2 }}
              />
            </Box>
          )}

          {activeTab === 1 && ( // Cancelar Pacote
            <Box>
              <Alert severity="warning" sx={{ mb: 2 }}>
                Tem certeza que deseja cancelar este pacote? Esta ação não pode ser desfeita e o cliente perderá os serviços restantes. O status do pacote será alterado para "Cancelado".
              </Alert>
              <TextField
                label="Motivo/Observações do cancelamento (opcional)"
                multiline
                rows={3}
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                fullWidth
                sx={{ mt: 2 }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
          <Button onClick={onClose} color="inherit" disabled={loading}>
            Voltar
          </Button>
          {activeTab === 0 && (
            <Button
              onClick={handleReschedule}
              variant="contained"
              color="primary"
              disabled={loading || !newDate || !newTime}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <EventRepeatIcon />}
            >
              {loading ? 'Reagendando...' : 'Confirmar Reagendamento'}
            </Button>
          )}
          {activeTab === 1 && (
            <Button
              onClick={handleCancelPackage}
              variant="contained"
              color="error"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CancelIcon />}
            >
              {loading ? 'Cancelando...' : 'Confirmar Cancelamento do Pacote'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
}; 