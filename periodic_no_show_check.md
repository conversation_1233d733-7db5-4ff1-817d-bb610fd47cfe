## Periodic No-Show Appointment Check

**Goal:** Add a periodic background check for no-show appointments to complement the existing checks on app startup and window focus.

**Implementation Details:**

1.  **Existing Logic Leveraged:**
    *   The core no-show detection logic resides in `AppointmentService.catchUpNoShows()` (in `src/main/database/services/AppointmentService.ts`).
    *   This function was already being called on app ready and window focus from `src/main/index.ts`.

2.  **Periodic Check Implementation (`src/main/index.ts`):**
    *   A `noShowCheckInterval` variable (NodeJS.Timeout) was added to hold the interval ID.
    *   Within the `app.on('ready', ...)` event handler, after the initial `AppointmentService.catchUpNoShows()` call and database setup:
        *   `setInterval` is used to call `AppointmentService.catchUpNoShows()` every 5 minutes (300,000 milliseconds).
        *   The callback for `setInterval` includes a try-catch block to log any errors occurring during the periodic check without crashing the interval.
    *   An `app.on('will-quit', ...)` listener was added to ensure `clearInterval(noShowCheckInterval)` is called when the application is closing. This prevents the interval from attempting to run after the app has exited.

**Files Modified:**

*   `src/main/index.ts`

**Outcome:** The application now has an additional layer of robustness for detecting no-show appointments. Even if the app remains open and in focus for extended periods without being minimized or restarted, overdue appointments will be caught and processed by the periodic background check. 