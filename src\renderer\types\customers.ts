import { Pet } from './pets';

export interface Customer {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  additional_notes?: string;
  created_at?: string;
  updated_at?: string;
  status?: 'active' | 'inactive';
  pets?: any[];
  pendingSales?: PendingSale[];
  hasPendingPayments?: boolean;
}

export interface PendingSale {
  id: number;
  total_amount: number;
  sale_date: string;
  payment_method: string;
  items_count: number;
}

export interface CustomerFormData {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  additional_notes?: string;
} 