import { useState, useEffect, useCallback } from 'react';
import { Service } from '../../main/database/models/Service';

interface UseServicesResult {
  services: Service[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getServiceById: (id: number) => Promise<Service | null>;
  createService: (serviceData: Partial<Service>) => Promise<Service | null>;
  updateService: (id: number, serviceData: Partial<Service>) => Promise<Service | null>;
  deleteService: (id: number) => Promise<boolean>;
  softDeleteService: (id: number) => Promise<boolean>;
  activateService: (id: number) => Promise<boolean>;
}

export const useServices = (): UseServicesResult => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServices = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('services:getAll');
      if (response.success) {
        // Filtra apenas serviços ativos por padrão
        setServices(response.data.filter((service: Service) => service.is_active !== false));
      } else {
        setError(response.error || 'Failed to fetch services');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const getServiceById = useCallback(async (id: number): Promise<Service | null> => {
    try {
      setLoading(true);
      const response = await window.electronAPI.invoke('services:getById', id, true);
      setLoading(false);
      
      if (response.success) {
        return response.data;
      }
      return null;
    } catch (error) {
      setError('Failed to fetch service by ID');
      setLoading(false);
      return null;
    }
  }, []);

  const createService = useCallback(async (serviceData: Partial<Service>): Promise<Service | null> => {
    try {
      const response = await window.electronAPI.invoke('services:create', serviceData);
      if (response.success) {
        await fetchServices(); // Refresh the list
        return response.data;
      }
      setError(response.error || 'Failed to create service');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchServices]);

  const updateService = useCallback(async (id: number, serviceData: Partial<Service>): Promise<Service | null> => {
    try {
      const response = await window.electronAPI.invoke('services:update', id, serviceData);
      if (response.success) {
        await fetchServices(); // Refresh the list
        return response.data;
      }
      setError(response.error || `Failed to update service with ID ${id}`);
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    }
  }, [fetchServices]);

  const deleteService = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('services:delete', id);
      if (response.success) {
        await fetchServices(); // Refresh the list
        return true;
      }
      setError(response.error || `Failed to delete service with ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchServices]);
  
  // Função de soft delete para marcar o serviço como inativo
  const softDeleteService = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('services:update', id, { is_active: false });
      if (response.success) {
        await fetchServices(); // Atualiza a lista removendo o serviço inativo
        return true;
      }
      setError(response.error || `Falha ao desativar serviço com ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchServices]);
  
  // Função para reativar um serviço previamente desativado
  const activateService = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('services:update', id, { is_active: true });
      if (response.success) {
        await fetchServices(); // Atualiza a lista incluindo o serviço reativado
        return true;
      }
      setError(response.error || `Falha ao reativar serviço com ID ${id}`);
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return false;
    }
  }, [fetchServices]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  return {
    services,
    loading,
    error,
    refetch: fetchServices,
    getServiceById,
    createService,
    updateService,
    deleteService,
    softDeleteService,
    activateService
  };
}; 