# Pet Shop Management Desktop Application - Project Documentation

## 1. Overview

This document provides a detailed overview of the Pet Shop Management desktop application, an Electron-based solution built with React, TypeScript, Material-UI, TypeORM, and SQLite. It aims to help developers understand the project's architecture, core functionalities, data flow, and key components.

## 2. Core Technologies

*   **Electron**: Framework for building cross-platform desktop applications using web technologies.
*   **React**: JavaScript library for building user interfaces.
*   **TypeScript**: Superset of JavaScript that adds static typing.
*   **Material-UI (MUI)**: React UI component library for implementing Google's Material Design.
*   **TypeORM**: Object-Relational Mapper (ORM) for TypeScript and JavaScript, used here with SQLite.
*   **SQLite**: Self-contained, serverless SQL database engine.
*   **Electron Forge**: Toolkit for packaging and distributing Electron applications.
*   **Webpack**: Module bundler for JavaScript applications.
*   **Zustand**: State management library (mentioned in `package.json`, though its direct usage hasn't been explicitly seen in the core files reviewed so far, it might be used in deeper UI components).
*   **React Router DOM**: For client-side routing within the React application.
*   **Bcryptjs**: For hashing passwords.

## 3. Project Structure and Build Process

The project is structured into typical Electron main and renderer process directories, along with configurations for building and packaging.

### 3.1. Key Configuration Files

*   **`package.json`**:
    *   Defines project metadata, dependencies (React, MUI, TypeORM, Electron, etc.), and devDependencies (Electron Forge, Webpack, TypeScript, ESLint).
    *   Scripts for starting (`npm start`), packaging (`npm run package`), making installers (`npm run make`), linting, testing, creating an admin user, and generating icons.
*   **`forge.config.ts`**:
    *   Configures Electron Forge for packaging the application.
    *   Specifies packager options like application name ("Pet Shop"), ASAR packaging, icon paths, and executable name.
    *   Defines makers for different platforms: Windows (Squirrel MSI), macOS (ZIP), Linux (RPM, Deb).
    *   Includes a publisher configuration for GitHub releases.
    *   Integrates the `WebpackPlugin` from Electron Forge to bundle main and renderer process code.
        *   **Main Process Entry**: `src/main/index.ts`
        *   **Renderer Process Entry**: `src/renderer/index.html` (HTML shell) and `src/renderer/index.tsx` (React app entry).
        *   **Preload Script**: `src/preload.ts` for the main window.
*   **Webpack Configuration (`webpack.main.config.ts`, `webpack.renderer.config.ts`, `webpack.rules.ts`, `webpack.plugins.ts`)**:
    *   **`webpack.main.config.ts`**: Bundles the main process code, including `src/main/index.ts`, `src/preload.ts` (main window preload), and `src/preload/splash-preload.ts` (splash screen preload).
    *   **`webpack.renderer.config.ts`**: Bundles the renderer process (React application).
        *   Includes rules for CSS, plugins, and optimizations like code splitting.
        *   Defines path aliases (e.g., `@components`, `@pages`) for easier module imports.
        *   Configures `webpack-dev-server` for hot reloading.
    *   **`webpack.rules.ts`**: Defines loaders for different file types (native modules, TypeScript, images). Uses `ts-loader` with `transpileOnly: true`.
    *   **`webpack.plugins.ts`**: Uses `ForkTsCheckerWebpackPlugin` for running TypeScript type checking in a separate process, complementing `transpileOnly` for faster builds.

### 3.2. Public and Assets

*   **`public/`**: Contains static assets like `index.html` (main window shell), `loading.html` (splash screen), and images. These are copied to the build.
*   **`src/assets/`**: Contains application icons and images used within the source code.

## 4. Main Process (`src/main/`)

The main process is responsible for managing application lifecycle, creating browser windows, interacting with the operating system, and handling backend logic like database operations and IPC.

### 4.1. Entry Point (`src/main/index.ts`)

*   **Initialization**: Handles Electron app events, Squirrel startup for Windows installers, and initializes `electron-store` for simple key-value persistence (e.g., `adminUserCreated` flag).
*   **Window Management**:
    *   **Splash Screen (`createSplashWindow`)**: Creates a frameless splash window (`public/loading.html`) displayed during app initialization. It has its own preload script (`splashPreload.js`).
    *   **Main Window (`createWindow`)**: Creates the main application window.
        *   Loads the React frontend via `MAIN_WINDOW_WEBPACK_ENTRY`.
        *   Uses `MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY` (`src/preload.ts`) to expose IPC functions securely.
        *   Configures Content Security Policy (CSP).
        *   Hides the default menu bar.
        *   Manages showing the main window only after both backend initialization (database) and frontend UI readiness are confirmed, providing a smooth transition from the splash screen.
*   **Database Setup**:
    *   Calls `setupDatabase()` from `src/main/database/connection.ts` when the app is ready.
    *   Sets up IPC handlers for authentication (`setupAuthHandlers` from `src/main/ipc/auth.ts`) and other database operations (by importing `src/main/ipc/database.ts`).
*   **Inter-Process Communication (IPC)**:
    *   Exposes `electron-store` get/set operations to the renderer.
    *   Provides `get-database-status` and `database-ready` mechanisms for the renderer to track database initialization.
    *   Listens for `main-window-ready` from the renderer to coordinate UI display.
*   **Auto Updater**: Integrates Electron's `autoUpdater` to check for, download, and prompt for updates.
*   **Application Lifecycle**: Handles `window-all-closed` and `activate` events.

### 4.2. Preload Scripts

*   **`src/preload.ts` (Main Window)**:
    *   Uses `contextBridge.exposeInMainWorld` to securely expose specific IPC functions to the renderer process under the `window.electron` and `window.electronAPI` objects.
    *   Maintains a **whitelist of valid IPC channels** (`validInvokeChannels`, `validOnChannels`, `validSendChannels`) for enhanced security.
    *   Exposes `invoke`, `on`, `removeListener`, `removeAllListeners`, and specific helpers like `getDatabaseStatus`, `onDatabaseReady`, and `sendMainWindowReady`.
    *   The extensive list of whitelisted channels (e.g., `customers:getAll`, `pets:create`) clearly defines the API between the renderer and main processes for data operations.
*   **`src/preload/splash-preload.ts` (Splash Screen)**:
    *   A minimal preload script exposing only a `sendToMain` function, restricted to the `splash-ready` channel, allowing the splash screen to notify the main process when it has loaded.

### 4.3. Database Layer (`src/main/database/`)

The application uses TypeORM with an SQLite database.

*   **Connection (`src/main/database/connection.ts`)**:
    *   `getDatabasePath()`: Determines the database file path, preferring standard app data locations with fallbacks.
    *   `AppDataSource`: The TypeORM `DataSource` instance.
        *   Configured for SQLite.
        *   Lists all entities (User, Customer, Pet, Product, Service, Sale, SaleItem, Appointment, PetService, Package, CustomerPackage, PackageUsageHistory).
        *   Uses `synchronize: true` (typically for development, automatically creates schema) alongside a list of explicit migrations. The code attempts to run migrations if pending.
        *   Enables logging in development.
    *   `createDefaultAdminUser()`: Creates a default admin user (`admin`/`admin123`) on first run if no users exist, using `electron-store` to track if this has been done.
    *   `setupDatabase()`: Initializes the `AppDataSource`, runs migrations, and creates the default admin user.
*   **Models (`src/main/database/models/*.ts`)**: These files define the TypeORM entities, mapping classes to database tables and defining columns, relationships, and constraints. (Detailed in Section 6).
*   **Services (`src/main/database/services/*.ts`)**: Implement the business logic for each entity.
    *   **`index.ts`**: Exports an instantiated `services` object (e.g., `services.customers`) for easy access.
    *   **`CustomerService.ts`**: Manages customer CRUD, status (active/inactive for soft delete), duplicate checks, and related actions like hiding pets or canceling appointments of inactive customers.
    *   **`PetService.ts`**: Manages pet CRUD, status (active/inactive), and visibility (`is_hidden`).
    *   **`ProductService.ts`**: Manages product CRUD, stock updates, low stock detection, and soft deletes (`is_deleted`).
    *   **`ServiceService.ts`** (for shop's services): Manages CRUD for services offered, using hard deletes.
    *   **`SaleService.ts`**: Handles complex sale creation (transactional, inventory validation, stock decrement, pet service history logging), sale deletion (transactional, optional restocking), and status updates.
    *   **`AppointmentService.ts`**: Manages appointment CRUD, status updates, and various lookup methods (upcoming, by date range).
    *   **`PetServiceService.ts`** (for services performed on pets): Logs historical records of services performed on specific pets.
*   **Migrations (`src/main/database/migrations/*.ts`)**: Timestamped files defining database schema changes (e.g., adding columns).

### 4.4. IPC Handlers (`src/main/ipc/`)

*   **`auth.ts`**:
    *   `setupAuthHandlers()`: Registers IPC handlers.
    *   `auth:login`: Handles user login, verifies credentials against the `User` table using `bcryptjs`, updates `last_login`, and returns user data (excluding password hash).
    *   `auth:logout`: Basic logout handler.
*   **`database.ts`**:
    *   Defines a large number of IPC handlers corresponding to the channels whitelisted in `preload.ts`.
    *   These handlers mostly delegate calls to the respective methods in the `services` object (from `src/main/database/services/index.ts`).
    *   They typically return a `{ success: boolean, data?: any, error?: string }` object to the renderer.
    *   Handles for `Package`, `CustomerPackage`, and `PackageUsageHistory` interact directly with TypeORM repositories instead of a dedicated service layer, an architectural inconsistency compared to other entities.

## 5. Renderer Process (`src/renderer/`)

The renderer process is responsible for the application's user interface, built with React and Material-UI.

### 5.1. Entry Point (`src/renderer/index.tsx`)

*   Standard React application bootstrap.
*   Renders the main `<App />` component into the `#root` HTML element (defined in `src/renderer/index.html`).
*   Wraps `<App />` in `<React.StrictMode>`.

### 5.2. Main Application Component (`src/renderer/App.tsx`)

*   **Context Providers**: Wraps the entire application in several context providers:
    *   `ThemeProviderWrapper`: For dynamic Material-UI theming.
    *   `DatabaseProvider`: Tracks database readiness.
    *   `AuthProvider`: Manages user authentication state and logic.
    *   `NotificationProvider`: Handles a proactive and persistent notification system.
    *   `HelpProvider`: Manages a help dialog system.
*   **Global UI Components**:
    *   `CssBaseline`: MUI base styles.
    *   `ThemeRefresher`: Custom component, possibly for applying theme changes.
    *   `DatabaseLoader`: Displays a loading UI until the database is ready, then renders the main app content.
    *   `SetupPin`: Component for users to set up a PIN (likely for quick lock/unlock).
    *   `HelpDialog`: The UI for the help system.
*   **Routing (`react-router-dom` with `HashRouter`)**:
    *   `/login`: `Login` page.
    *   `/*`: All other routes are nested within `MainLayout`.
        *   Defines routes for Dashboard, Inventory, Customers, Pets, Sales, Appointments, Packages, Reports, and Settings.
*   **UI Readiness Signal**: Sends `main-window-ready` to the main process via IPC after a short delay to ensure the UI is mounted.

### 5.3. Contexts (`src/renderer/contexts/`)

*   **`AuthContext.tsx`**:
    *   Manages `user` state, `isLocked` state (for inactivity lock).
    *   Provides `login`, `logout`, `setPin` functions.
    *   Implements an inactivity timer (5 minutes) that locks the screen if a PIN is set.
    *   Handles PIN unlock via a `LockScreen` component.
    *   Redirects unauthenticated users to `/login`.
    *   Stores user data (including PIN) in `localStorage` but clears it on initial app load (no session persistence across restarts by default).
*   **`NotificationContext.tsx`**:
    *   A sophisticated system for managing and displaying notifications.
    *   Notifications have types (info, warning, error, success), timestamps, read status, etc.
    *   Persists notifications to `localStorage` and filters expired ones (older than 7 days).
    *   Plays simple Web Audio API sounds for different notification types.
    *   **Proactively generates notifications**:
        *   Periodically checks for upcoming appointments, no-show appointments, expiring no-shows, and overdue pending sales.
        *   Uses custom hooks (`useAppointments`, `useSales`) for data and implements its own caching layer for this data.
        *   Tracks already notified events to prevent duplicates.
    *   Listens to `window.focus` and custom DOM events (`appointment-needs-marking`, `appointment-status-changed`) to trigger checks or specific notifications.
    *   Provides `useNotificationData` and `useNotificationActions` hooks.
*   **`ThemeContext.tsx`**:
    *   Allows dynamic customization of Material-UI primary and secondary colors.
    *   Persists theme choices to `localStorage`.
    *   Uses debouncing for color state updates and `localStorage` writes to improve performance.
    *   Dynamically creates the MUI theme object, overriding palette colors and styles for `MuiButton`, `MuiDrawer`, and global scrollbars.
*   **`HelpContext.tsx`**:
    *   Manages the visibility (`isHelpOpen`) of a help dialog.
    *   Provides `currentPagePath` (from `useLocation`) to enable context-sensitive help.
    *   Opens the help dialog in response to a global `open-help-dialog` DOM event.
*   **`DatabaseContext.tsx`**:
    *   Tracks `isDatabaseReady` state.
    *   Communicates with the main process (`getDatabaseStatus`, `onDatabaseReady` event) and uses polling as a fallback to determine when the backend database is initialized.
    *   Used by `DatabaseLoader` to delay rendering the main app UI.

### 5.4. Custom Hooks (`src/renderer/hooks/`)

These hooks abstract data fetching and manipulation logic for different entities, providing a clean API to UI components. They typically handle loading states, errors, and IPC calls.

*   **`useAppointments.ts`**: Manages appointment data. Includes client-side caching for `getAppointmentsByDateRange` and cache invalidation on CUD operations.
*   **`useSales.ts`**: Manages sales data. Includes client-side caching for `getSalesByDateRange` and `getSaleItems`, with cache invalidation.
*   **`useProducts.ts`**: Manages product data. Includes client-side caching specifically for `getLowStockProducts`. Allows manual state update via `setProductsState` for optimized stock updates.
*   **`usePets.ts`**: Manages pet data. No client-side caching implemented in this hook.
*   **`useServices.ts`**: Manages data for services offered by the shop. Filters for active services on the client-side by default. Supports both hard and soft deletes.

### 5.5. UI Components (`src/renderer/components/`)

*   **Layout (`src/renderer/components/Layout/MainLayout.tsx`)**:
    *   The main UI structure for authenticated users.
    *   Features a responsive, collapsible navigation drawer (sidebar) with categorized menu items and tooltips.
    *   Includes a top `AppBar` with application title, help icon, and notifications icon/menu.
    *   The `NotificationsMenu` is a detailed dropdown listing notifications from `NotificationContext`.
    *   Manages transitions and performance optimizations for drawer collapse/expand.
*   **Dashboard (`src/renderer/pages/Dashboard.tsx`)**:
    *   A central hub displaying KPIs (revenue, appointments, low stock) via `StatCard` components.
    *   Shows an `AppointmentList` for upcoming appointments with interactive management (edit, delete, status change).
    *   Features charts (revenue, service distribution, stock status) using `react-chartjs-2`.
    *   Allows toggling between "Today" and "Week" views.
    *   Manages dialogs for appointment editing, deletion confirmation, and sale creation prompts.
    *   Highly interactive and data-driven.
*   **Other Components**:
    *   `LockScreen.tsx`: UI for the inactivity lock screen, prompting for PIN.
    *   `SetupPin.tsx`: UI for users to set up their PIN.
    *   `DatabaseLoader.tsx`: Displays loading UI while waiting for `DatabaseContext.isDatabaseReady`.
    *   `HelpDialog.tsx`: The UI for the help system, likely using `HelpContext`.
    *   Various form dialogs (e.g., `AppointmentFormDialog.tsx`) and entity-specific cards (e.g., `CustomerCard.tsx`) are present in the file structure, indicating UIs for managing each data type.

### 5.6. Pages (`src/renderer/pages/`)

These components represent the main sections of the application, accessible via routing:
`Appointments.tsx`, `Customers.tsx`, `Dashboard.tsx`, `Inventory.tsx`, `Login.tsx`, `Packages.tsx`, `Pets.tsx`, `Reports.tsx`, `Sales.tsx`, `Settings.tsx`. Each page likely uses the corresponding custom hook to fetch and manage its data.

## 6. Data Models (TypeORM Entities - `src/main/database/models/`)

*   **`User.ts`**: `id`, `username` (unique), `password_hash`, `role`, `created_at`, `last_login`.
*   **`Customer.ts`**: `id`, `name`, `email`, `phone`, `address`, `additional_notes`, `status` ('active'/'inactive'), `created_at`, `updated_at`. Has a one-to-many relationship with `Pet`.
*   **`Pet.ts`**: `id`, `name`, `type`, `breed`, `age`, `size`, `gender`, `fur_type`, `additional_notes`, `photo_url`, `is_hidden` (boolean), `status` ('active'/'inactive'). Has a many-to-one relationship with `Customer`.
*   **`Product.ts`**: `id`, `name`, `category`, `description`, `price` (decimal), `cost_price` (decimal), `stock_quantity`, `min_stock_level`, `is_deleted` (boolean).
*   **`Service.ts`** (Shop's services): `id`, `name`, `description`, `price` (decimal), `duration_minutes`, `is_active` (boolean).
*   **`Sale.ts`**: `id`, `total_amount`, `payment_method`, `status`, `sale_date`. Has a many-to-one relationship with `Customer` and a one-to-many relationship with `SaleItem`.
*   **`SaleItem.ts`**: `id`, `quantity`, `price_per_unit`, `petId` (nullable), `customer_package_id` (nullable), `is_package_service` (boolean). Has many-to-one relationships with `Sale`, `Product` (nullable), and `Service` (nullable).
*   **`Appointment.ts`**: `id`, `appointment_date` (datetime), `status`, `notes`. Has many-to-one relationships with `Customer`, `Pet`, and `Service`.
*   **`PetService.ts`** (Services performed on pets): `id`, `pet_id`, `service_name` (string), `service_date`, `notes`. Has a many-to-one relationship with `Pet` (with `onDelete: 'CASCADE'`).
*   **`Package.ts`**: `id`, `name`, `description`, `price` (float), `services` (JSON array of Service IDs), `service_quantity`, `expiry_days`.
*   **`CustomerPackage.ts`**: `id`, `customer_id`, `package_id`, `remaining_services`, `purchase_date`, `expiry_date`, `is_active` (boolean). Has many-to-one relationships with `Customer` and `Package`.
*   **`PackageUsageHistory.ts`**: `id`, `customer_package_id`, `service_id` (nullable), `pet_id` (nullable), `sale_id` (nullable), `sale_item_id` (nullable), `usage_date`, `notes`. Has many-to-one relationships with `CustomerPackage`, `Service`, `Pet`, `Sale`, `SaleItem`.

## 7. Key Features and Workflows

*   **User Authentication**: Login with username/password. PIN setup for inactivity lock.
*   **Dashboard**: Overview of KPIs, upcoming appointments, revenue charts, service distribution, stock status.
*   **Entity Management**: CRUD operations for Customers, Pets, Products, Services, Packages.
    *   Soft deletes for Customers, Pets, Products.
    *   Active/inactive status for Services.
*   **Sales Processing**: Create sales, manage sale items, update inventory automatically.
*   **Appointment Scheduling**: Create, view, update, and manage appointments. Status tracking (scheduled, completed, cancelled, no-show).
*   **Package Management**: Define service packages, sell packages to customers, track package usage and expiry.
*   **Inventory Control**: Track product stock, identify low-stock items.
*   **Notification System**: Proactive alerts for upcoming appointments, no-shows, expiring items, overdue pending sales. Persistent notifications with sound.
*   **Dynamic Theming**: Customizable primary and secondary application colors.
*   **Help System**: Context-sensitive help dialog.
*   **Auto Updates**: Application can check for and install updates.

## 8. Potential Areas for Review/Enhancement (Observations)

*   **`synchronize: true` with Migrations**: The use of `AppDataSource.synchronize = true` alongside explicit migrations in `src/main/database/connection.ts` is unusual. Typically, `synchronize: true` is for development and auto-creates/alters schema, while migrations are for controlled, versioned schema changes in production. This combination could lead to unexpected behavior if not carefully managed. It's generally recommended to use `synchronize: false` when migrations are in place for production.
*   **Service Layer Inconsistency**: Most entities have their logic encapsulated in a service class (e.g., `CustomerService`). However, IPC handlers for `Package`, `CustomerPackage`, and `PackageUsageHistory` interact directly with TypeORM repositories. Standardizing this to use a service layer for all entities would improve consistency.
*   **`PetService.service_name`**: The `PetService` entity stores `service_name` as a string instead of a foreign key to the `Service` entity. This might be intentional for historical accuracy but sacrifices some relational integrity and makes it harder to, for example, find all pets that received a specific "Grooming" service if the ID of "Grooming" is what's primarily used elsewhere.
*   **`CustomerService.getPetCount()`**: This method in `CustomerService.ts` appears to be incorrectly implemented, as it queries the customer repository to count customers, not pets for a customer.
*   **Session Persistence**: `AuthContext` clears `localStorage` on app load, meaning no session persistence across restarts. This might be intentional for security in a desktop app context but could be an area for enhancement if desired.
*   **Zustand Usage**: `package.json` lists `zustand` as a dependency, but its direct usage wasn't apparent in the core files reviewed. It might be used in more specific UI components not yet analyzed.
*   **Error Handling in Services**: Some service methods in `src/main/database/services/` catch errors and `console.error` them but then re-throw. The IPC handlers in `src/main/ipc/database.ts` also catch errors and format them. This double error handling is generally fine, but ensuring consistent error propagation and user feedback is important.
*   **Client-Side Filtering in `useServices`**: The `useServices` hook fetches all services and then filters for active ones on the client. If the number of services (active + inactive) becomes very large, fetching all of them first could be less efficient than having the backend provide a way to fetch only active ones.

This documentation provides a foundational understanding of the Pet Shop Management application. Further deep dives into specific UI components and less central utility files would provide even more granularity.