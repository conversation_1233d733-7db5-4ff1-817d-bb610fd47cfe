# Pet Shop Desktop Application Documentation

## 1. Introduction

This document provides a comprehensive overview of the Pet Shop Desktop Application, an Electron-based application designed for managing pet shop operations. It utilizes React for the user interface, SQLite for data storage, and Material UI for styling.

The application allows users to manage customers, pets, appointments, sales, service packages, and various application settings.

## 2. Project Structure

The project is a typical Electron application with a main process, a renderer process, and a preload script.

-   **`src/main/main.ts`**: Handles the main process logic, including window creation, application lifecycle events, and IPC (Inter-Process Communication) setup for database interactions and other backend functionalities.
-   **`src/renderer`**: Contains the frontend React application.
    -   **`App.tsx`**: The root component of the React application, setting up routing and global contexts (Auth, Theme, Notifications).
    -   **`pages/`**: Contains components for each main section/page of the application (e.g., `Dashboard.tsx`, `Customers.tsx`, `Pets.tsx`, `Appointments.tsx`, `Sales.tsx`, `Packages.tsx`, `Settings.tsx`).
    -   **`components/`**: Contains reusable UI components used across different pages, organized by feature (e.g., `Customers/CustomerCard.tsx`, `Pets/PetFormDialog.tsx`).
    -   **`hooks/`**: Custom React hooks for managing data fetching and business logic related to specific entities (e.g., `useCustomers.ts`, `usePets.ts`).
    -   **`contexts/`**: React Context API implementations for global state management (e.g., `AuthContext.tsx`, `ThemeContext.tsx`, `NotificationContext.tsx`).
    -   **`types/`**: TypeScript type definitions for various data models used in the application.
    -   **`utils/`**: Utility functions.
    -   **`theme.ts`**: Defines the base Material UI theme.
-   **`src/preload/preload.ts`**: A script that runs in a privileged environment, exposing selected Electron and Node.js APIs (like database interaction functions) to the renderer process in a secure manner via `window.electronAPI`.
-   **`database/`**: Likely contains the SQLite database file and schema definitions (though schema details are primarily seen in `main.ts` table creation queries).

## 3. Core Modules/Features

### 3.1. Dashboard (`Dashboard.tsx`)

-   Provides an overview of key metrics and quick access to common actions.
-   Displays statistics like total customers, pets, upcoming appointments, and recent sales.
-   Features quick links to add new customers, pets, or appointments.
-   Shows a list of upcoming appointments and recent sales.

### 3.2. Customers (`Customers.tsx`)

-   Manages customer information (CRUD operations).
-   Displays customers in a card layout, utilizing `react-virtualized` for performance with large lists.
-   Allows searching and filtering customers (including a toggle for active/inactive customers and customers with pending payments).
-   **Key functionalities:**
    -   Adding new customers with a form dialog (`CustomerFormDialog.tsx`).
    -   Editing existing customer details.
    -   Deactivating (soft deleting) customers. Reactivating customers.
    -   Handles cases where a new customer might be similar to an existing inactive customer, prompting the user to either reactivate or create new.
    -   Viewing and managing pets associated with a customer (`CustomerPetsDialog.tsx`).
    -   Viewing and managing service packages purchased by a customer (`CustomerPackagesDialog.tsx`).
    -   Buying new packages for a customer (`BuyPackageDialog.tsx`).
    -   Viewing and managing pending sales (fiado) for a customer (`PendingSalesDialog.tsx`), including marking sales as paid.

### 3.3. Pets (`Pets.tsx`)

-   Manages pet information (CRUD operations).
-   Displays pets in a card layout, also using `react-virtualized`.
-   Allows searching for pets.
-   **Key functionalities:**
    -   Adding new pets with a form dialog (`PetFormDialog.tsx`), including associating them with a customer.
    -   Editing existing pet details.
    -   Deleting pets.
    -   Viewing and managing services performed on a pet (`PetServicesDialog.tsx`, `AddServiceDialog.tsx`). (Note: Service history seems to be based on mock data or partially implemented in the `Pets.tsx` file, while actual services are linked via appointments).

### 3.4. Appointments (`Appointments.tsx`)

-   Manages appointments for services (CRUD operations).
-   Offers both a list view (using `react-virtualized`) and a calendar view (`AppointmentCalendar.tsx`) for appointments.
-   Allows searching for appointments.
-   **Key functionalities:**
    -   Creating new appointments (`AppointmentFormDialog.tsx`), linking customer, pet, and service.
    -   Editing existing appointments.
    -   Deleting appointments.
    -   Updating appointment status (e.g., scheduled, confirmed, completed, canceled, no_show).
    -   Prompts to create a sale when an appointment is marked as 'completed'.
    -   Handles notifications for 'no_show' appointments.

### 3.5. Sales (`Sales.tsx`)

-   Manages sales transactions (CRUD operations).
-   Displays sales in a list, potentially with filtering and search capabilities.
-   **Key functionalities:**
    -   Creating new sales, either manually or initiated from a completed appointment or package purchase.
    -   Adding items (services or products) to a sale.
    -   Specifying payment methods (including 'Fiado' for pending payments).
    -   Calculating totals and discounts.
    -   Viewing sale details.
    -   Printing receipts (functionality to be implemented).
    -   Managing pending payments (fiado).

### 3.6. Packages (`Packages.tsx`)

-   Manages service packages (CRUD operations).
-   Packages bundle multiple services, often at a discounted price.
-   Displays packages in a card layout using `react-virtualized`.
-   Allows searching for packages.
-   **Key functionalities:**
    -   Creating new packages (`PackageFormDialog.tsx`), defining name, description, price, and included services/quantities.
    -   Editing existing packages.
    -   Deleting packages (with a captcha confirmation due to the impact on customer packages).
    -   When a package is purchased by a customer (via `Customers.tsx`), a corresponding sale is created.

### 3.7. Settings (`Settings.tsx`)

-   Provides various application configuration options, organized into tabs:
    -   **Segurança (Security):** Change user password, set/change a PIN for application lock.
    -   **Aparência (Appearance):** Customize the application's primary and secondary theme colors with a live preview.
    -   **Serviços (Services):** Manage the list of available services offered by the pet shop (CRUD operations for services, including name, description, duration, and price). These services are then used when creating appointments or defining packages.
    -   **Recibo (Receipt):** Configure store information (name, address, phone, email, CNPJ, additional notes) that appears on sales receipts.
    -   **Fiado (Pending Sales):** Configure settings for pending sales notifications, such as enabling/disabling notifications and setting the threshold (in days) for when a notification should be triggered for an overdue pending sale.

## 4. Data Management

-   **Database:** SQLite is used as the database, managed through the main process.
-   **IPC Communication:** The renderer process communicates with the main process to perform database operations. This is done via `window.electronAPI.invoke('channel:action', payload)` calls defined in `preload.ts`.
-   **Main Process DB Logic (`main.ts`):** Contains functions that execute SQL queries (CREATE, SELECT, INSERT, UPDATE, DELETE) on the SQLite database. It handles table creation on application startup if tables don't exist.
-   **Custom Hooks (`src/renderer/hooks/`):** These hooks encapsulate data fetching logic for different entities. They typically use `useEffect` to fetch initial data and provide functions for CRUD operations, which in turn call the `electronAPI`.
    -   `useAuth`: Manages user authentication state.
    -   `useAppointments`: Manages appointment data.
    -   `useCustomers`: Manages customer data, including related pets and packages.
    -   `usePackages`: Manages package definitions and customer-specific package instances.
    -   `usePets`: Manages pet data.
    -   `useSales`: Manages sales data.
    -   `useServices`: Manages service definitions (primarily used in Settings and for populating forms).
-   **Data Adaptation:** Helper functions (e.g., `adaptCustomer`, `adaptPet`) are often used within page components or hooks to transform data from the database format to the frontend's TypeScript types.

## 5. State Management

-   **Local Component State:** React's `useState` and `useReducer` (though `useReducer` is less common in the viewed files) are used for managing component-level state (e.g., form inputs, dialog visibility, filtered lists).
-   **Derived State:** `useMemo` is extensively used to memoize computations and prevent unnecessary re-renders, especially for filtering and sorting lists.
-   **Callbacks:** `useCallback` is used to memoize event handlers and functions passed to child components.
-   **Global State (Context API):**
    -   **`AuthContext`**: Manages user authentication status and PIN.
    -   **`ThemeContext` (`useAppTheme`)**: Manages the application's theme (primary and secondary colors) and provides a function to update it.
    -   **`NotificationContext` (`useNotifications`)**: Provides a system for displaying global notifications/alerts to the user.

## 6. Key Workflows/Interactions

-   **Customer Onboarding:** User creates a new customer. The system checks for similar inactive customers to prevent duplicates.
-   **Pet Registration:** User adds a pet, associating it with an existing customer.
-   **Appointment Scheduling:** User selects a customer, pet, and service to schedule an appointment. Appointments can be viewed in a list or calendar.
-   **Service Completion & Billing:** When an appointment is marked 'completed', the user is prompted to create a sale for the service.
-   **Package Purchase:** A customer can purchase a pre-defined package of services. This action also creates a sale.
-   **Sales Transaction:** Sales can be created manually, from appointments, or from package purchases. Payment methods include 'Fiado' (on credit).
-   **Pending Payment Management:** Users can view customers with pending payments and mark these sales as paid.
-   **Service Definition:** Admins/users can define and manage the services offered by the pet shop via the Settings page.
-   **Theme Customization:** Users can change the application's look and feel by selecting primary and secondary colors in Settings.

## 7. API (IPC Communication)

The renderer process interacts with the main process using an API exposed via `window.electronAPI`. Key channels observed include:

-   `auth:login`, `auth:changePassword`
-   `db:getCustomers`, `db:createCustomer`, `db:updateCustomer`, `db:deleteCustomer`, `db:reactivateCustomer`, `db:getInactiveCustomers`, `db:getPendingSalesByCustomerId`, `db:markSaleAsPaid`
-   `db:getPets`, `db:getPetsByCustomerId`, `db:createPet`, `db:updatePet`, `db:deletePet`
-   `db:getAppointments`, `db:createAppointment`, `db:updateAppointment`, `db:deleteAppointment`, `db:updateAppointmentStatus`
-   `db:getServices`, `db:createService`, `db:updateService`, `db:deleteService` (soft delete for services)
-   `db:getPackages`, `db:createPackage`, `db:updatePackage`, `db:deletePackage`
-   `db:getCustomerPackages`, `db:createCustomerPackage`, `db:deleteCustomerPackage`, `db:getPackageUsageHistory`, `db:usePackageService`
-   `sales:create`, `sales:getAll`, `sales:getById`, `sales:update`, `sales:delete`
-   `app:getStoreInfo`, `app:saveStoreInfo` (Likely, based on Settings page functionality)
-   `app:getPendingSalesSettings`, `app:savePendingSalesSettings` (Likely)
-   `app:getAppVersion`
-   `app:quit`
-   `app:lock`, `app:unlock`
-   `notifications:checkOverduePendingSales`

These channels map to functions in `main.ts` that perform operations, often involving the SQLite database.

## 8. Styling and UI

-   **Material UI (MUI):** The primary UI component library. Used for layout (Box, Grid), typography, buttons, dialogs, cards, form inputs, icons, etc.
-   **Custom Theme (`theme.ts`, `ThemeContext`):** A base theme is defined and can be customized by the user (primary/secondary colors) via the Settings page.
-   **`react-virtualized`:** Used for rendering long lists of customers, pets, appointments, and packages efficiently to maintain UI performance.
-   **Responsiveness:** MUI components inherently support responsiveness. The column count in virtualized lists (e.g., CustomerCard, PetCard) adapts based on screen width.
-   **Localization:** UI text is in PT-BR (Brazilian Portuguese).

## 9. Potential Areas for Improvement/Further Investigation

### S1: Code Stability & Scalability

-   **Error Handling:** While `try...catch` blocks are present, ensure consistent and user-friendly error reporting across all API calls and UI interactions. Consider a centralized error handling mechanism.
-   **Database Migrations:** For schema changes in the future, a proper database migration system (e.g., using `knex-migrations` or similar) would be beneficial instead of relying solely on `CREATE TABLE IF NOT EXISTS`.
-   **Input Validation:** Enhance input validation both on the frontend (form level) and backend (main process before database operations) to ensure data integrity.
-   **Testing:** Implement unit tests for hooks and utility functions, and consider integration tests for API endpoints (IPC calls) and end-to-end tests for critical user flows.
-   **Large Datasets:** While `react-virtualized` helps the frontend, ensure backend queries in `main.ts` are optimized for performance as data grows (e.g., proper indexing on SQLite tables, especially for columns used in WHERE clauses and JOINs).

### S2: Performance & Security

-   **Performance:**
    -   **Bundle Size:** Analyze the final Electron bundle size and explore optimizations (code splitting, tree shaking) if it becomes too large.
    -   **Memoization:** Continue judicious use of `useMemo` and `useCallback`. Profile the application to identify any performance bottlenecks.
    -   **Image Handling:** If pet photos or other images are heavily used, consider image optimization (compression, lazy loading for images not in virtualized lists).
-   **Security:**
    -   **Preload Script:** The use of a preload script is good practice. Ensure it only exposes necessary functionalities and sanitizes any data passed between processes.
    -   **SQL Injection:** Ensure all SQL queries in `main.ts` use parameterized queries (e.g., `db.run('UPDATE users SET name = ? WHERE id = ?', [name, id])`) to prevent SQL injection vulnerabilities. The current use of template literals for some queries might be a risk if inputs are not strictly controlled, though many seem to use parameters.
    -   **Data at Rest:** If sensitive data is stored, consider encryption for the SQLite database, although this adds complexity.
    -   **Dependencies:** Regularly update dependencies to patch known vulnerabilities.

### S3: Readability & Maintainability

-   **Code Comments:** Add more comments to explain complex logic, especially in hooks and main process functions.
-   **Type Safety:** Continue leveraging TypeScript. Ensure types are comprehensive and accurate.
-   **Modularity:** Hooks and contexts help with modularity. Ensure components remain focused on their specific responsibilities.
-   **Magic Strings/Numbers:** Replace magic strings (e.g., status values, IPC channel names) with constants or enums for better maintainability and to avoid typos.
-   **`any` Type:** Minimize the use of the `any` type in TypeScript. Provide more specific types where possible.
-   **File Structure:** The current structure seems reasonable. As the application grows, consider further refinements if needed.
-   **Consistency:** Ensure consistent coding styles and patterns across the codebase.

### Further Investigation Areas:

-   **Pet Service History:** The `Pets.tsx` page has UI elements for pet services, but the data source seems to be mock data or not fully integrated with the appointment/sales system. Clarify how service history for a pet is tracked and displayed.
-   **Product Management:** The sales system allows adding items. It's unclear if there's a dedicated module for managing products (inventory, pricing) beyond services.
-   **Reporting/Analytics:** Explore adding more advanced reporting features beyond the basic dashboard.
-   **User Roles/Permissions:** Currently, it seems like a single-user or single-role system. If multiple users with different access levels are needed, a role-based access control (RBAC) system would be required.
-   **Backup and Restore:** Consider implementing a database backup and restore functionality for data safety.
