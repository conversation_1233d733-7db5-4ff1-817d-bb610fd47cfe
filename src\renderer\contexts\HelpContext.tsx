import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { useLocation } from 'react-router-dom';

// Define the context type
interface HelpContextType {
  isHelpOpen: boolean;
  openHelp: () => void;
  closeHelp: () => void;
  currentPagePath: string;
}

// Create the context with default values
const HelpContext = createContext<HelpContextType>({
  isHelpOpen: false,
  openHelp: () => {},
  closeHelp: () => {},
  currentPagePath: '/'
});

// Hook to use the help context
export const useHelp = () => useContext(HelpContext);

interface HelpProviderProps {
  children: ReactNode;
}

export const HelpProvider: React.FC<HelpProviderProps> = ({ children }) => {
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const location = useLocation();
  const currentPagePath = location.pathname;

  const openHelp = useCallback(() => {
    setIsHelpOpen(true);
  }, []);

  const closeHelp = useCallback(() => {
    setIsHelpOpen(false);
  }, []);

  // Listen for global help event
  useEffect(() => {
    const handleHelpClick = () => {
      openHelp();
    };
    
    window.addEventListener('open-help-dialog', handleHelpClick);
    
    return () => {
      window.removeEventListener('open-help-dialog', handleHelpClick);
    };
  }, [openHelp]);

  return (
    <HelpContext.Provider
      value={{
        isHelpOpen,
        openHelp,
        closeHelp,
        currentPagePath
      }}
    >
      {children}
    </HelpContext.Provider>
  );
}; 