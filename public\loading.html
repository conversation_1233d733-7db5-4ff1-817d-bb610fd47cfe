<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Carregando...</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        user-select: none;
        overflow: hidden;
      }
      
      .container {
        text-align: center;
      }
      
      .logo {
        width: 220px;
        height: 220px;
        margin-bottom: 20px;
        animation: pulse 1.5s infinite ease-in-out;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        margin: 20px auto;
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-left-color: #2196f3;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .text {
        color: #333;
        font-size: 18px;
        margin-top: 15px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img src="logo.png" alt="Logo" class="logo" />
      <div class="spinner"></div>
      <div class="text">Carregando o sistema, por favor aguarde...</div>
    </div>
    
    <script>
      // Expose a function that the main process can call to get notified when splash is ready
      window.splash = {
        ready: function() {
          if (window.electron) {
            window.electron.sendToMain('splash-ready');
          }
        }
      };
      
      // Call the ready function when the window is fully loaded
      window.onload = function() {
        if (window.splash) {
          window.splash.ready();
        }
      };
    </script>
  </body>
</html> 