import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Grid from '@mui/material/Grid';
import FormHelperText from '@mui/material/FormHelperText';
import { SelectChangeEvent } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import { ptBR } from 'date-fns/locale';
import { 
  LocalizationProvider,
  DateTimePicker
} from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { 
  Appointment, 
  AppointmentFormData, 
  AppointmentStatus, 
  AppointmentStatusLabels,
  Customer,
  Pet,
  Service
} from '../../types/appointments';
import { 
  formatBrazilianDate, 
  getBrazilianNow, 
  parseFormDate, 
  formatForFormInput, 
  toBrazilianTimezone,
  BRAZIL_TIMEZONE,
  BRAZIL_LOCALE,
  parseBrazilianDateString
} from '../../utils/dateUtils';

interface AppointmentFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (appointmentData: AppointmentFormData) => void;
  appointment?: Appointment;
  title: string;
  customers: Customer[];
  pets: Pet[];
  services: Service[];
  initialCustomerId?: number;
}

export const AppointmentFormDialog: React.FC<AppointmentFormDialogProps> = ({
  open,
  onClose,
  onSave,
  appointment,
  title,
  customers,
  pets,
  services,
  initialCustomerId
}) => {
  // Store the initial form state in a ref to avoid re-triggering effects
  const [formData, setFormData] = useState<AppointmentFormData>({
    customer_id: 0,
    pet_id: 0,
    service_id: 0,
    appointment_date: formatForFormInput(getBrazilianNow()),
    status: 'scheduled',
    notes: ''
  });
  
  const [errors, setErrors] = useState<Partial<Record<keyof AppointmentFormData, string>>>({});
  const [customerPets, setCustomerPets] = useState<Pet[]>([]);

  // Only initialize form data when the dialog opens or appointment changes
  useEffect(() => {
    if (!open) return;
    
    // console.log("Dialog opened, initializing form...");
    
    if (appointment) {
      // Format date for display in Brazilian format dd/MM/yyyy hh:mm
      const dateObj = toBrazilianTimezone(appointment.appointment_date);
      const formattedDate = formatBrazilianDate(dateObj);
      
      setFormData({
        customer_id: appointment.customer_id,
        pet_id: appointment.pet_id,
        service_id: appointment.service_id,
        appointment_date: formattedDate,
        status: appointment.status,
        notes: appointment.notes || ''
      });
      
      // console.log("Form initialized with appointment data:", {
      //   customer_id: appointment.customer_id,
      //   pet_id: appointment.pet_id,
      //   date: formatBrazilianDate(dateObj)
      // });
    } else {
      // New appointment - use current time in Brazil timezone
      const brazilNow = getBrazilianNow();
      const formattedDate = formatBrazilianDate(brazilNow);
      
      setFormData({
        customer_id: initialCustomerId || 0,
        pet_id: 0,
        service_id: 0,
        appointment_date: formattedDate,
        status: 'scheduled',
        notes: ''
      });
      
      // console.log("Form initialized with default data in Brazil timezone:", formatBrazilianDate(brazilNow));
    }
    
    // Reset errors
    setErrors({});
  }, [open, appointment, initialCustomerId]);
  
  // Update available pets when customer changes
  // This is a separate effect from form initialization
  useEffect(() => {
    if (!open) return;
    
    const customerId = formData.customer_id;
    
    if (customerId > 0) {
      const filteredPets = pets.filter(pet => pet.customer_id === customerId);
      // console.log(`Customer ID ${customerId}: Found ${filteredPets.length} pets`);
      setCustomerPets(filteredPets);
    } else {
      setCustomerPets([]);
    }
  }, [formData.customer_id, pets, open]);

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof AppointmentFormData, string>> = {};
    
    if (formData.customer_id === 0) {
      newErrors.customer_id = 'Cliente é obrigatório';
    }
    
    if (formData.pet_id === 0) {
      newErrors.pet_id = 'Pet é obrigatório';
    }
    
    if (formData.service_id === 0) {
      newErrors.service_id = 'Serviço é obrigatório';
    }
    
    if (!formData.appointment_date) {
      newErrors.appointment_date = 'Data e hora do agendamento são obrigatórias';
    } else {
      // Basic check for non-empty after picker interaction
      // The DateTimePicker and formatBrazilianDate should ensure valid format
      // if a date is picked.
      // The existing parseBrazilianDateString in handleSubmit will validate further.
      const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})\s+(\d{2}):(\d{2})$/;
      if (!dateRegex.test(formData.appointment_date.replace(',', ''))) {
        newErrors.appointment_date = 'Formato inválido. Use: dd/MM/yyyy hh:mm';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    // console.log("Submitting form with data:", formData);
    if (validateForm()) {
      // Normalize the date by removing comma if present
      const normalizedDate = formData.appointment_date.replace(',', '');
      
      // Convert the date from Brazilian format to ISO format for the backend
      const parsedDate = parseBrazilianDateString(normalizedDate);
      const formattedForBackend = parsedDate.toISOString();
      
      const submissionData = {
        ...formData,
        appointment_date: formattedForBackend
      };
      
      // console.log("Submitting with converted date:", submissionData);
      onSave(submissionData);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    // console.log(`Input changed: ${name} = ${value}`);
    
    setFormData(prev => {
      const updated = { ...prev, [name]: value };
      // console.log("Updated formData:", updated);
      return updated;
    });
    
    if (errors[name as keyof AppointmentFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (e: SelectChangeEvent<string | number>) => {
    const { name, value } = e.target;
    const numValue = Number(value);
    
    // console.log(`Select changed: ${name} = ${value} (${typeof numValue})`);
    
    if (name === 'customer_id') {
      // Reset pet when customer changes
      setFormData(prev => {
        const updated = { 
          ...prev, 
          [name]: numValue,
          pet_id: 0 
        };
        // console.log("Updated formData with new customer:", updated);
        return updated;
      });
    } else {
      setFormData(prev => {
        const updated = { ...prev, [name]: numValue };
        // console.log("Updated formData:", updated);
        return updated;
      });
    }
    
    if (errors[name as keyof AppointmentFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleStatusChange = (e: SelectChangeEvent<string>) => {
    const { value } = e.target;
    // console.log(`Status changed: ${value}`);
    
    setFormData(prev => {
      const updated = { ...prev, status: value as AppointmentStatus };
      // console.log("Updated formData with new status:", updated);
      return updated;
    });
  };

  // DEBUG OUTPUT
  // console.log("Current form state:", {
  //   customer_id: formData.customer_id,
  //   pet_id: formData.pet_id,
  //   service_id: formData.service_id,
  //   customerPets: customerPets.length
  // });

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.customer_id}>
              <InputLabel id="customer-label">Cliente</InputLabel>
              <Select
                labelId="customer-label"
                name="customer_id"
                value={formData.customer_id}
                onChange={handleSelectChange}
                label="Cliente"
              >
                <MenuItem value={0}><em>Selecione um cliente</em></MenuItem>
                {customers.map((customer) => (
                  <MenuItem key={customer.id} value={customer.id}>
                    {customer.name}
                  </MenuItem>
                ))}
              </Select>
              {errors.customer_id && <FormHelperText>{errors.customer_id}</FormHelperText>}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.pet_id} disabled={formData.customer_id === 0}>
              <InputLabel id="pet-label">Pet</InputLabel>
              <Select
                labelId="pet-label"
                name="pet_id"
                value={formData.pet_id}
                onChange={handleSelectChange}
                label="Pet"
              >
                <MenuItem value={0}><em>Selecione um pet</em></MenuItem>
                {customerPets.map((pet) => (
                  <MenuItem key={pet.id} value={pet.id}>
                    {pet.name} ({pet.type})
                  </MenuItem>
                ))}
              </Select>
              {errors.pet_id && <FormHelperText>{errors.pet_id}</FormHelperText>}
              {formData.customer_id === 0 && <FormHelperText>Selecione um cliente primeiro</FormHelperText>}
              {formData.customer_id > 0 && customerPets.length === 0 && (
                <FormHelperText>Nenhum pet encontrado para este cliente</FormHelperText>
              )}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.service_id}>
              <InputLabel id="service-label">Serviço</InputLabel>
              <Select
                labelId="service-label"
                name="service_id"
                value={formData.service_id}
                onChange={handleSelectChange}
                label="Serviço"
              >
                <MenuItem value={0}><em>Selecione um serviço</em></MenuItem>
                {services.map((service) => (
                  <MenuItem key={service.id} value={service.id}>
                    {service.name} - R${service.price.toFixed(2)} ({service.duration_minutes} min)
                  </MenuItem>
                ))}
              </Select>
              {errors.service_id && <FormHelperText>{errors.service_id}</FormHelperText>}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
              <DateTimePicker
                label="Data e Hora do Agendamento"
                value={formData.appointment_date ? parseBrazilianDateString(formData.appointment_date.replace(',', '')) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange({
                    target: {
                      name: 'appointment_date',
                      value: newValue ? formatBrazilianDate(newValue) : ''
                    }
                  } as React.ChangeEvent<HTMLInputElement>);
                }}
                ampm={false}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    error: !!errors.appointment_date,
                    helperText: errors.appointment_date || '',
                    InputLabelProps: {
                      shrink: true,
                    },
                     placeholder: "dd/MM/yyyy hh:mm",
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="status-label">Status</InputLabel>
              <Select
                labelId="status-label"
                name="status"
                value={formData.status}
                onChange={handleStatusChange}
                label="Status"
              >
                {Object.entries(AppointmentStatusLabels).map(([status, label]) => (
                  <MenuItem key={status} value={status}>
                    {status === 'no_show' ? 'Faltou' : label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              name="notes"
              label="Observações"
              value={formData.notes}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={3}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancelar</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Salvar
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 