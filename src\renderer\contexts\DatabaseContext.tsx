import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface DatabaseContextType {
  isDatabaseReady: boolean;
}

const DatabaseContext = createContext<DatabaseContextType>({
  isDatabaseReady: false,
});

export const useDatabaseStatus = () => useContext(DatabaseContext);

interface DatabaseProviderProps {
  children: ReactNode;
}

export const DatabaseProvider: React.FC<DatabaseProviderProps> = ({ children }) => {
  const [isDatabaseReady, setIsDatabaseReady] = useState(false);

  useEffect(() => {
    // Check initial status
    const checkDatabaseStatus = async () => {
      try {
        const status = await window.electron.getDatabaseStatus();
        setIsDatabaseReady(status.isReady);
      } catch (error) {
        console.error('Failed to get database status:', error);
      }
    };

    checkDatabaseStatus();

    // Listen for database-ready event
    const handleDatabaseReady = () => {
      setIsDatabaseReady(true);
    };

    window.electron.onDatabaseReady(handleDatabaseReady);

    // Poll for status changes every second until database is ready
    const interval = setInterval(async () => {
      if (!isDatabaseReady) {
        const status = await window.electron.getDatabaseStatus();
        if (status.isReady) {
          setIsDatabaseReady(true);
          clearInterval(interval);
        }
      } else {
        clearInterval(interval);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
      window.electron.removeAllListeners('database-ready');
    };
  }, [isDatabaseReady]);

  return (
    <DatabaseContext.Provider value={{ isDatabaseReady }}>
      {children}
    </DatabaseContext.Provider>
  );
}; 