import React, { useState, useMemo, useCallback, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Divider from '@mui/material/Divider';
import {
  Pets as PetsIcon,
  Delete as DeleteIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Pet, PetService } from '../../types/pets';

// Utilitário de debounce simples
const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Função para formatar datas - extraída para fora do componente para não ser recriada
const formatDateString = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Interface para dados precomputados
interface ServiceWithSearchData extends PetService {
  _searchData?: string;
  _formattedDate?: string;
}

interface PetServicesDialogProps {
  open: boolean;
  onClose: () => void;
  pet?: Pet;
  services: PetService[];
  onAddService: (petId: number) => void;
  onDeleteService?: (serviceId: number) => void;
}

export const PetServicesDialog = React.memo(({
  open,
  onClose,
  pet,
  services,
  onAddService,
  onDeleteService
}: PetServicesDialogProps) => {
  const [inputSearchTerm, setInputSearchTerm] = useState<string>('');
  const debouncedSearchTerm = useDebounce(inputSearchTerm, 150);
  
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setInputSearchTerm(event.target.value);
  }, []);

  // Cache de datas formatadas para evitar recálculos
  const dateFormatCache = useMemo(() => {
    const cache = new Map<string, string>();
    return {
      get: (dateString: string): string => {
        if (!cache.has(dateString)) {
          cache.set(dateString, formatDateString(dateString));
        }
        return cache.get(dateString) || '';
      }
    };
  }, []);

  // Pré-computar dados de pesquisa e formatação para cada serviço - otimizado
  const servicesWithSearchData = useMemo<ServiceWithSearchData[]>(() => {
    // Lazy initialization - só executa se o dialog estiver aberto
    if (!open || !services.length) return [];

    return services.map(service => {
      // Usar o cache para a data formatada
      const formattedDate = dateFormatCache.get(service.service_date);
      
      // Criar objeto Date apenas uma vez por serviço
      const serviceDate = new Date(service.service_date);
      
      // Obter nome do mês em português apenas uma vez
      const monthFull = serviceDate.toLocaleString('pt-BR', { month: 'long' }).toLowerCase();
      const monthShort = serviceDate.toLocaleString('pt-BR', { month: 'short' }).toLowerCase().replace('.', '');
      
      // Criar string de busca precomputada
      const searchData = [
        service.service_name.toLowerCase(),
        service.notes ? service.notes.toLowerCase() : '',
        formattedDate.toLowerCase(),
        monthFull,
        monthShort
      ].join(' ');
      
      return {
        ...service,
        _searchData: searchData,
        _formattedDate: formattedDate
      };
    });
  }, [services, dateFormatCache, open]);

  // Filtrar serviços usando os dados precomputados
  const filteredServices = useMemo(() => {
    if (!pet || !debouncedSearchTerm.trim()) {
      return servicesWithSearchData;
    }

    const term = debouncedSearchTerm.toLowerCase().trim();
    
    return servicesWithSearchData.filter(service => 
      service._searchData?.includes(term)
    );
  }, [servicesWithSearchData, debouncedSearchTerm, pet]);

  // Não renderizar nada se o pet não estiver definido ou o dialog estiver fechado
  if (!pet || !open) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      // Adicionar transição suave
      TransitionProps={{
        // Reduzir o trabalho durante a animação de abertura
        onEntering: () => {
          // Nada aqui por enquanto - mantém o código limpo
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PetsIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="span">
            Serviços realizados pelo(a) {pet.name}
          </Typography>
          <Chip 
            label={pet.type} 
            size="small" 
            color="primary" 
            sx={{ ml: 2 }} 
          />
        </Box>
      </DialogTitle>
      
      {/* Barra de pesquisa fixada fora do DialogContent para não rolar */}
      <Box 
        sx={{ 
          px: 3, 
          pt: 1, 
          pb: 2,
          position: 'sticky',
          top: 0,
          backgroundColor: 'background.paper',
          zIndex: 5,
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <TextField
          fullWidth
          placeholder="Buscar por nome do serviço, observações, data ou mês (ex: março ou mar)"
          variant="outlined"
          size="small"
          value={inputSearchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
          }}
        />
      </Box>
      
      <DialogContent sx={{ pt: 2 }}>
        {filteredServices.length === 0 ? (
          <Box sx={{ py: 4, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              {services.length === 0 
                ? "Nenhum serviço registrado ainda." 
                : "Nenhum serviço encontrado com os termos da pesquisa."}
            </Typography>
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Serviço</TableCell>
                  <TableCell>Data</TableCell>
                  <TableCell>Observações</TableCell>
                  {onDeleteService && <TableCell align="right">Ações</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredServices.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell>{service.service_name}</TableCell>
                    <TableCell>{service._formattedDate}</TableCell>
                    <TableCell>{service.notes || '-'}</TableCell>
                    {onDeleteService && (
                      <TableCell align="right">
                        <IconButton 
                          aria-label="excluir" 
                          size="small" 
                          color="error"
                          onClick={() => onDeleteService(service.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => onAddService(pet.id)}>Adicionar Serviço</Button>
        <Button onClick={onClose}>Fechar</Button>
      </DialogActions>
    </Dialog>
  );
}); 