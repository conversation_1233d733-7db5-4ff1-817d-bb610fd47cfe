import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';
import { SelectChangeEvent } from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  CardGiftcard as CardGiftcardIcon
} from '@mui/icons-material';
import { SaleItemFormData } from '../../types/sales';
import { Product } from '../../types/inventory';
import { Pet } from '../../types/pets';
import { CustomerPackage } from '../../types/packages';

interface SaleItemFormProps {
  onAddItem: (item: SaleItemFormData) => void;
  onRemoveItem: (index: number) => void;
  products: Product[];
  services: any[]; // Service type
  items: SaleItemFormData[];
  pets: Pet[]; // Add pets prop
  customerId: number | ''; // Add customerId to filter pets
  customerPackages: CustomerPackage[]; // Add customerPackages prop
  hidePackageOption?: boolean; // Add option to hide package option
  hideServiceOption?: boolean; // Add option to hide service option
}

export const SaleItemForm: React.FC<SaleItemFormProps> = ({
  onAddItem,
  onRemoveItem,
  products,
  services,
  items,
  pets,
  customerId,
  customerPackages,
  hidePackageOption = false,
  hideServiceOption = false
}) => {
  const [itemType, setItemType] = useState<'product' | 'service' | 'package'>('product');
  const [itemId, setItemId] = useState<number>(0);
  const [quantity, setQuantity] = useState<number>(1);
  const [price, setPrice] = useState<number>(0);
  const [itemName, setItemName] = useState<string>('');
  const [petId, setPetId] = useState<number>(0);
  const [packageId, setPackageId] = useState<number>(0);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  
  // Keep track of how many services have been used from each package in the current sale
  const [pendingPackageUses, setPendingPackageUses] = useState<{ [packageId: number]: number }>({});

  // Filter pets by customer ID if a customer is selected
  const filteredPets = customerId && customerId !== 0 
    ? pets.filter(pet => pet.customer_id === customerId)
    : pets;

  // Calculate number of services used from each package in the current sale
  useEffect(() => {
    const packageUsesCount: { [packageId: number]: number } = {};
    
    // Count services for each package in current items
    items.forEach(item => {
      if (item.type === 'package' && item.customer_package_id) {
        packageUsesCount[item.customer_package_id] = 
          (packageUsesCount[item.customer_package_id] || 0) + 1;
      }
    });
    
    setPendingPackageUses(packageUsesCount);
  }, [items]);

  // Filter active packages with remaining services, with real-time tracking of usage
  const availablePackages = customerId && customerId !== 0
    ? customerPackages.map(pkg => {
        // Calculate effective remaining services (adjusted for pending uses in the current sale)
        const pendingUses = pendingPackageUses[pkg.id] || 0;
        const effectiveRemaining = pkg.remaining_occurrences - pendingUses;
        
        // Return a modified package object with an adjusted count
        return {
          ...pkg,
          effective_remaining: effectiveRemaining
        };
      }).filter(pkg => pkg.effective_remaining > 0 && pkg.status === 'active')
    : [];

  // Calculate how many of each product are already in the cart
  const getAlreadyAddedQuantity = (productId: number): number => {
    return items
      .filter(item => item.type === 'product' && item.id === productId)
      .reduce((sum, item) => sum + item.quantity, 0);
  };

  // Calculate effective remaining stock for a product
  const getEffectiveRemainingStock = (product: Product): number => {
    const alreadyAdded = getAlreadyAddedQuantity(product.id);
    return Math.max(0, product.stock_quantity - alreadyAdded);
  };

  // Reset form when item type changes
  useEffect(() => {
    setItemId(0);
    setQuantity(1);
    setPrice(0);
    setItemName('');
    setPetId(0);
    setPackageId(0);
    setErrors({});
  }, [itemType]);

  // Update price and validate quantity when product or service is selected or when items change
  useEffect(() => {
    if (itemId === 0) return;

    if (itemType === 'product') {
      const product = products.find(p => p.id === itemId);
      if (product) {
        setPrice(product.price);
        setItemName(product.name);
        
        // Limit quantity to effective remaining stock
        const effectiveStock = getEffectiveRemainingStock(product);
        if (quantity > effectiveStock) {
          setQuantity(effectiveStock > 0 ? effectiveStock : 1);
        }
      }
    } else if (itemType === 'service') {
      const service = services.find(s => s.id === itemId);
      if (service) {
        setPrice(service.price);
        setItemName(service.name);
      }
    } else if (itemType === 'package') {
      // For package type, we need to set the package service information
      if (packageId > 0) {
        const selectedPackage = customerPackages.find(pkg => pkg.id === packageId);
        if (selectedPackage) {
          // We set a zero price since it has already been paid for in the package
          setPrice(0);
          
          // Find the selected service ID
          if (selectedPackage.package && itemId > 0) {
            const service = services.find(s => s.id === itemId);
            if (service) {
              setItemName(`${service.name} (Pacote: ${selectedPackage.package.name})`);
            }
          }
        }
      }
    }
  }, [itemId, packageId, itemType, products, services, customerPackages, items, quantity]);

  // Modify itemType if needed when hidePackageOption or hideServiceOption changes
  useEffect(() => {
    if ((hidePackageOption && itemType === 'package') || 
        (hideServiceOption && itemType === 'service')) {
      setItemType('product');
    }
  }, [hidePackageOption, hideServiceOption, itemType]);

  const handleItemTypeChange = (event: SelectChangeEvent<string>) => {
    setItemType(event.target.value as 'product' | 'service' | 'package');
  };

  const handleItemChange = (event: SelectChangeEvent<number>) => {
    setItemId(Number(event.target.value));
    // Reset quantity when changing items
    setQuantity(1);
  };

  const handlePackageChange = (event: SelectChangeEvent<number>) => {
    setPackageId(Number(event.target.value));
    // Reset service selection
    setItemId(0);
  };

  const handlePetChange = (event: SelectChangeEvent<number>) => {
    setPetId(Number(event.target.value));
  };

  const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    setQuantity(isNaN(value) || value < 1 ? 1 : value);
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (itemId === 0) {
      newErrors.itemId = 'Por favor, selecione um item';
    }

    if (quantity < 1) {
      newErrors.quantity = 'A quantidade deve ser maior que zero';
    }

    // Check inventory levels for products (considering items already in cart)
    if (itemType === 'product' && itemId > 0) {
      const product = products.find(p => p.id === itemId);
      if (product) {
        const alreadyAdded = getAlreadyAddedQuantity(product.id);
        const effectiveRemaining = product.stock_quantity - alreadyAdded;
        
        if (effectiveRemaining < quantity) {
          if (effectiveRemaining <= 0) {
            newErrors.quantity = `Não há mais estoque disponível. ${alreadyAdded} já está no carrinho.`;
          } else {
            newErrors.quantity = `Apenas ${effectiveRemaining} mais disponíveis. ${alreadyAdded} já está no carrinho.`;
          }
        }
      }
    }

    // Validate pet selection for services and packages
    if ((itemType === 'service' || itemType === 'package') && itemId > 0 && petId === 0) {
      newErrors.petId = 'Por favor, selecione um pet para este serviço';
    }

    // Validate package selection
    if (itemType === 'package' && packageId === 0) {
      newErrors.packageId = 'Por favor, selecione um pacote';
    }

    // Validate that the service is included in the selected package
    if (itemType === 'package' && packageId > 0 && itemId > 0) {
      const selectedPackage = customerPackages.find(pkg => pkg.id === packageId);
      if (selectedPackage?.package && selectedPackage.package.service_id !== itemId) {
        newErrors.itemId = 'Este serviço não está incluído no pacote selecionado';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddItem = () => {
    if (validateForm()) {
      const newItem: SaleItemFormData = {
        type: itemType,
        id: itemId,
        quantity,
        price_per_unit: price,
        name: itemName,
        petId: (itemType === 'service' || itemType === 'package') ? petId : null,
        customer_package_id: itemType === 'package' ? packageId : null
      };
      onAddItem(newItem);
      
      // Reset form
      setItemId(0);
      setQuantity(1);
      setPackageId(0);
      if (itemType === 'service' || itemType === 'package') {
        setPetId(0);
      }
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('pt-BR', { 
      style: 'currency', 
      currency: 'BRL' 
    }).format(amount);
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Adicionar Item
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={2}>
            <FormControl fullWidth>
              <InputLabel>Tipo</InputLabel>
              <Select
                value={itemType}
                onChange={handleItemTypeChange}
                label="Tipo"
              >
                <MenuItem value="product">Produto</MenuItem>
                {!hideServiceOption && (
                  <MenuItem value="service">Serviço</MenuItem>
                )}
              </Select>
            </FormControl>
          </Grid>

          {itemType === 'package' && (
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth error={!!errors.packageId}>
                <InputLabel>Pacote</InputLabel>
                <Select
                  value={packageId}
                  onChange={handlePackageChange}
                  label="Pacote"
                  disabled={availablePackages.length === 0}
                >
                  <MenuItem value={0}><em>Selecione um pacote</em></MenuItem>
                  {availablePackages.map(pkg => (
                    <MenuItem key={pkg.id} value={pkg.id}>
                      {pkg.package?.name} #{pkg.id} ({pkg.effective_remaining} uso{pkg.effective_remaining !== 1 ? 's' : ''} restante{pkg.effective_remaining !== 1 ? 's' : ''})
                    </MenuItem>
                  ))}
                </Select>
                {errors.packageId && (
                  <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                    {errors.packageId}
                  </Box>
                )}
                {availablePackages.length === 0 && (
                  <Box component="span" sx={{ color: 'text.secondary', fontSize: '0.75rem', mt: 0.5 }}>
                    Este cliente não possui pacotes ativos
                  </Box>
                )}
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12} sm={itemType === 'package' ? 3 : (itemType === 'service' ? 3 : 4)}>
            <FormControl fullWidth error={!!errors.itemId}>
              <InputLabel>{itemType === 'product' ? 'Produto' : 'Serviço'}</InputLabel>
              <Select
                value={itemId}
                onChange={handleItemChange}
                label={itemType === 'product' ? 'Produto' : 'Serviço'}
                disabled={itemType === 'package' && packageId === 0}
              >
                <MenuItem value={0}><em>Selecione {itemType === 'product' ? 'um produto' : 'um serviço'}</em></MenuItem>
                {itemType === 'product' ? (
                  products.map(product => {
                    const effectiveStock = getEffectiveRemainingStock(product);
                    const alreadyAdded = getAlreadyAddedQuantity(product.id);
                    return (
                      <MenuItem 
                        key={product.id} 
                        value={product.id}
                        disabled={effectiveStock <= 0}
                      >
                        {product.name} - {formatCurrency(product.price)} 
                        {alreadyAdded > 0 && (
                          <span style={{ color: 'blue', marginLeft: '8px' }}>
                            ({alreadyAdded} no carrinho)
                          </span>
                        )}
                        {effectiveStock <= 5 ? 
                          <span style={{ color: effectiveStock > 0 ? 'orange' : 'red', marginLeft: '8px' }}>
                            ({effectiveStock} disponível)
                          </span> : 
                          <span style={{ color: 'green', marginLeft: '8px' }}>
                            ({effectiveStock} disponível)
                          </span>
                        }
                      </MenuItem>
                    );
                  })
                ) : (
                  itemType === 'package' && packageId > 0 ? (
                    // Only show services that are included in the selected package
                    (() => {
                      const selectedPackage = customerPackages.find(pkg => pkg.id === packageId);
                      if (!selectedPackage || !selectedPackage.package) return null;
                      
                      const availableServiceId = selectedPackage.package.service_id;
                      return services
                        .filter(service => service.id === availableServiceId)
                        .map(service => (
                          <MenuItem key={service.id} value={service.id}>
                            {service.name} {' '}
                            <Chip 
                              size="small" 
                              label="Incluso no pacote" 
                              color="success" 
                              sx={{ ml: 1, fontSize: '0.7rem' }} 
                            />
                          </MenuItem>
                        ));
                    })()
                  ) : (
                    services.map(service => (
                      <MenuItem key={service.id} value={service.id}>
                        {service.name} - {formatCurrency(service.price)}
                      </MenuItem>
                    ))
                  )
                )}
              </Select>
              {errors.itemId && (
                <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                  {errors.itemId}
                </Box>
              )}
            </FormControl>
          </Grid>

          {(itemType === 'service' || itemType === 'package') && (
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth error={!!errors.petId}>
                <InputLabel>Pet</InputLabel>
                <Select
                  value={petId}
                  onChange={handlePetChange}
                  label="Pet"
                  disabled={!customerId || filteredPets.length === 0}
                >
                  <MenuItem value={0}><em>Selecione um pet</em></MenuItem>
                  {filteredPets.map(pet => (
                    <MenuItem key={pet.id} value={pet.id}>
                      {pet.name} ({pet.type})
                    </MenuItem>
                  ))}
                </Select>
                {errors.petId && (
                  <Box component="span" sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5 }}>
                    {errors.petId}
                  </Box>
                )}
                {filteredPets.length === 0 && customerId && (
                  <Box component="span" sx={{ color: 'text.secondary', fontSize: '0.75rem', mt: 0.5 }}>
                    Este cliente não possui pets cadastrados
                  </Box>
                )}
              </FormControl>
            </Grid>
          )}

          <Grid item xs={6} sm={2}>
            <TextField
              fullWidth
              label="Quantidade"
              type="number"
              value={quantity}
              onChange={handleQuantityChange}
              inputProps={{ min: 1 }}
              error={!!errors.quantity}
              helperText={errors.quantity}
              disabled={itemType === 'package'} // For packages, quantity is always 1
            />
          </Grid>

          {itemType !== 'package' && (
            <Grid item xs={6} sm={2}>
              <TextField
                fullWidth
                label="Preço Unitário"
                value={formatCurrency(price)}
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>
          )}

          <Grid item xs={12} sm={itemType === 'package' ? 3 : 1}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              onClick={handleAddItem}
              disabled={itemId === 0 || (itemType === 'package' && packageId === 0)}
            >
              <AddIcon />
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {items.length > 0 && (
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Itens da Venda
          </Typography>
          <Box sx={{ mt: 2 }}>
            {items.map((item, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 1,
                  mb: 1,
                  borderBottom: '1px solid #eee',
                }}
              >
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body1">
                      {item.name}
                    </Typography>
                    {item.type === 'package' && (
                      <Chip 
                        size="small" 
                        label="Pacote" 
                        color="primary"
                        variant="outlined"
                        icon={<CardGiftcardIcon />}
                        sx={{ ml: 1 }} 
                      />
                    )}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {item.quantity} x {formatCurrency(item.price_per_unit)} = {formatCurrency(item.price_per_unit * item.quantity)}
                    {item.type === 'service' && item.petId && pets.find(p => p.id === item.petId) && (
                      <span> | Pet: {pets.find(p => p.id === item.petId)?.name}</span>
                    )}
                  </Typography>
                </Box>
                <IconButton 
                  size="small" 
                  color="error" 
                  onClick={() => onRemoveItem(index)}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            ))}
          </Box>
        </Paper>
      )}
    </Box>
  );
}; 