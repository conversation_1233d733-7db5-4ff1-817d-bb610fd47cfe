# Circular Dependencies Detected

This list was generated from the Webpack build output using the `circular-dependency-plugin`. Each line represents a detected cycle that needs to be refactored.

## Core / IPC Cycles

*   `src\main\index.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts`
*   `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\database\connection.ts`
*   `src\main\ipc\auth.ts` -> `src\main\index.ts` -> `src\main\ipc\auth.ts`

## Service Layer Cycles (often involving `services/index.ts`, `connection.ts`, and `ipc/database.ts`)

*   `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\CustomerService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts`
*   `src\main\database\services\AppointmentService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\CustomerService.ts` -> `src\main\database\services\AppointmentService.ts`
*   `src\main\database\services\CustomerPackageService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\CustomerService.ts` -> `src\main\database\services\AppointmentService.ts` -> `src\main\database\services\CustomerPackageService.ts`
*   `src\main\database\services\index.ts` -> `src\main\database\services\CustomerService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts`
*   `src\main\database\services\CustomerService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\CustomerService.ts`
*   `src\main\database\services\PetService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\CustomerService.ts` -> `src\main\database\services\PetService.ts`
*   `src\main\database\services\ProductService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\ProductService.ts`
*   `src\main\database\services\ServiceService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\ServiceService.ts`
*   `src\main\database\services\SaleService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\SaleService.ts`
*   `src\main\database\services\PetServiceService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\PetServiceService.ts`
*   `src\main\database\services\PackageService.ts` -> `src\main\database\connection.ts` -> `src\main\index.ts` -> `src\main\ipc\database.ts` -> `src\main\database\services\index.ts` -> `src\main\database\services\PackageService.ts`

## Model Layer Cycles (Direct Relationships)

*   `src\main\database\models\Customer.ts` -> `src\main\database\models\Pet.ts` -> `src\main\database\models\Customer.ts`
*   `src\main\database\models\Pet.ts` -> `src\main\database\models\Customer.ts` -> `src\main\database\models\Pet.ts`
*   `src\main\database\models\Sale.ts` -> `src\main\database\models\SaleItem.ts` -> `src\main\database\models\Sale.ts`
*   `src\main\database\models\SaleItem.ts` -> `src\main\database\models\Sale.ts` -> `src\main\database\models\SaleItem.ts`
*   `src\main\database\models\Appointment.ts` -> `src\main\database\models\CustomerPackage.ts` -> `src\main\database\models\Appointment.ts`
*   `src\main\database\models\CustomerPackage.ts` -> `src\main\database\models\Appointment.ts` -> `src\main\database\models\CustomerPackage.ts`

---

**Notes for Review:**

*   The plugin might report variations of the same fundamental cycle (e.g., A -> B -> A and B -> A -> B). Focus on breaking the underlying loop.
*   Cycles involving `services/index.ts` often mean a service within that directory is importing from its own barrel file instead of directly from a sibling module.
*   Cycles between database models (e.g., Customer <-> Pet) are common if they directly import each other for defining relationships (e.g., ORM definitions). These might require careful use of `import type` if only types are needed, or forward declarations/references if supported by your ORM/database library for relationship definitions.