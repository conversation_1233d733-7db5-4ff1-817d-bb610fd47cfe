{"name": "pet-shop-management", "version": "0.0.2", "description": "Oi", "main": ".webpack/main", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "test": "jest", "create-admin": "ts-node src/scripts/createAdminUser.ts", "generate-icons": "electron-icon-builder --input=./src/assets/icon.png --output=./src/assets"}, "author": "unknown7987", "license": "MIT", "homepage": "https://www.google.com", "repository": {"type": "git", "url": "https://github.com/unknown7987/spacepet-releases"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.16.14", "@mui/lab": "^5.0.0-alpha.160", "@mui/material": "^5.16.14", "@mui/x-data-grid": "^6.19.4", "@mui/x-date-pickers": "^6.20.2", "@supabase/supabase-js": "^2.49.8", "@types/better-sqlite3": "^7.6.12", "@types/react-virtualized": "^9.22.2", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/recharts": "^1.8.29", "@types/sqlite3": "^3.1.11", "@types/uuid": "^10.0.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "better-sqlite3": "^11.8.1", "chart.js": "^4.4.8", "date-fns": "^3.6.0", "electron-squirrel-startup": "^1.0.0", "electron-store": "^10.0.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "react-virtualized": "^9.22.6", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.1", "reflect-metadata": "^0.2.2", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "typeorm": "^0.3.20", "uuid": "^11.1.0", "zustand": "^4.5.1"}, "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@electron-forge/plugin-auto-unpack-natives": "^7.2.0", "@electron-forge/plugin-webpack": "^7.2.0", "@electron-forge/publisher-github": "^7.8.0", "@electron/rebuild": "^3.7.1", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/circular-dependency-plugin": "^5.0.8", "@types/file-loader": "^5.0.4", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.16", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-virtualized-auto-sizer": "^1.0.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "circular-dependency-plugin": "^5.2.2", "css-loader": "^6.0.0", "electron": "28.2.3", "electron-icon-builder": "^2.0.1", "electron-rebuild": "^3.2.9", "eslint": "^8.0.1", "eslint-plugin-import": "^2.25.0", "eslint-plugin-react": "^7.33.2", "fork-ts-checker-webpack-plugin": "^7.2.13", "jest": "^29.7.0", "node-loader": "^2.0.0", "style-loader": "^3.0.0", "ts-jest": "^29.1.2", "ts-loader": "^9.2.2", "ts-node": "^10.0.0", "typescript": "~4.5.4", "webpack": "^5.98.0"}}